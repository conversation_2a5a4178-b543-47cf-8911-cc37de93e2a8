{"name": "memory-keeper", "version": "1.0.0", "description": "A Chrome extension built with React and Ant Design", "main": "index.js", "scripts": {"start": "webpack --watch --progress --config webpack.dev.js", "build": "webpack --progress --config webpack.prod.js", "type-check": "tsc --noEmit", "type-check:watch": "tsc --noEmit --watch"}, "keywords": ["chrome", "extension", "react", "antd"], "author": "", "license": "MIT", "dependencies": {"@aws-sdk/client-s3": "^3.808.0", "@aws-sdk/s3-request-presigner": "^3.808.0", "antd": "^5.10.0", "core-js": "^3.42.0", "esdk-obs-browserjs": "^3.24.3", "lodash": "^4.17.21", "moment": "^2.30.1", "pako": "^2.1.0", "react": "^18.2.0", "react-dom": "^18.2.0", "uuid": "^9.0.1"}, "devDependencies": {"@babel/core": "^7.22.5", "@babel/preset-env": "^7.22.5", "@babel/preset-react": "^7.22.5", "@types/chrome": "^0.0.326", "@types/lodash": "^4.17.17", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@types/uuid": "^10.0.0", "babel-loader": "^9.1.2", "copy-webpack-plugin": "^11.0.0", "css-loader": "^6.8.1", "html-webpack-plugin": "^5.5.3", "style-loader": "^3.3.3", "ts-loader": "^9.5.2", "typescript": "^5.8.3", "webpack": "^5.88.0", "webpack-cli": "^5.1.4", "webpack-merge": "^5.9.0"}}