/**
 * 事件处理器类型
 */
export type EventHandler<T = any> = (data: T) => void | Promise<void>;

/**
 * 事件订阅
 */
export interface EventSubscription {
  unsubscribe(): void;
}

/**
 * 事件发射器接口
 */
export interface IEventEmitter {
  // 事件订阅
  on<T>(event: string, handler: EventHandler<T>): EventSubscription;
  once<T>(event: string, handler: EventHandler<T>): EventSubscription;
  off(event: string, handler?: EventHandler<any>): void;

  // 事件发布
  emit<T>(event: string, data?: T): void;

  // 事件管理
  clear(): void;
  getListeners(event: string): EventHandler<any>[];
  hasListeners(event: string): boolean;
}

/**
 * 事件监听器信息
 */
interface ListenerInfo {
  handler: EventHandler<any>;
  once: boolean;
  priority?: number;
}

/**
 * 事件统计信息
 */
export interface EventStats {
  totalEvents: number;
  totalListeners: number;
  eventCounts: Record<string, number>;
  lastEventTime?: Date;
}

/**
 * 事件配置
 */
export interface EventEmitterConfig {
  maxListeners?: number;
  enableStats?: boolean;
  enableDebug?: boolean;
}

/**
 * 默认事件发射器实现
 */
export class EventEmitter implements IEventEmitter {
  private listeners: Map<string, ListenerInfo[]> = new Map();
  private config: EventEmitterConfig;
  private stats: EventStats = {
    totalEvents: 0,
    totalListeners: 0,
    eventCounts: {}
  };

  constructor(config: EventEmitterConfig = {}) {
    this.config = {
      maxListeners: 100,
      enableStats: true,
      enableDebug: false,
      ...config
    };
  }

  /**
   * 订阅事件
   */
  on<T>(event: string, handler: EventHandler<T>, priority?: number): EventSubscription {
    return this.addListener(event, handler, false, priority);
  }

  /**
   * 订阅一次性事件
   */
  once<T>(event: string, handler: EventHandler<T>, priority?: number): EventSubscription {
    return this.addListener(event, handler, true, priority);
  }

  /**
   * 取消订阅
   */
  off(event: string, handler?: EventHandler<any>): void {
    if (!this.listeners.has(event)) {
      return;
    }

    if (!handler) {
      // 移除所有监听器
      const listeners = this.listeners.get(event)!;
      this.stats.totalListeners -= listeners.length;
      this.listeners.delete(event);
      return;
    }

    const listeners = this.listeners.get(event)!;
    const index = listeners.findIndex(listener => listener.handler === handler);

    if (index > -1) {
      listeners.splice(index, 1);
      this.stats.totalListeners--;

      // 如果没有监听器了，删除事件
      if (listeners.length === 0) {
        this.listeners.delete(event);
      }
    }
  }

  /**
   * 发射事件
   */
  emit<T>(event: string, data?: T): void {
    if (this.config.enableDebug) {
      console.debug(`[EventEmitter] 发射事件: ${event}`, data);
    }

    // 更新统计信息
    if (this.config.enableStats) {
      this.stats.totalEvents++;
      this.stats.eventCounts[event] = (this.stats.eventCounts[event] || 0) + 1;
      this.stats.lastEventTime = new Date();
    }

    if (!this.listeners.has(event)) {
      return;
    }

    const listeners = this.listeners.get(event)!.slice(); // 复制数组避免修改问题

    // 按优先级排序（高优先级先执行）
    listeners.sort((a, b) => (b.priority || 0) - (a.priority || 0));

    for (const listener of listeners) {
      try {
        const result = listener.handler(data);

        // 处理异步处理器
        if (result instanceof Promise) {
          result.catch(error => {
            console.error(`事件处理器错误 [${event}]:`, error);
          });
        }

        // 移除一次性监听器
        if (listener.once) {
          this.off(event, listener.handler);
        }
      } catch (error) {
        console.error(`事件处理器错误 [${event}]:`, error);
      }
    }
  }

  /**
   * 异步发射事件（等待所有处理器完成）
   */
  async emitAsync<T>(event: string, data?: T): Promise<void> {
    if (this.config.enableDebug) {
      console.debug(`[EventEmitter] 异步发射事件: ${event}`, data);
    }

    // 更新统计信息
    if (this.config.enableStats) {
      this.stats.totalEvents++;
      this.stats.eventCounts[event] = (this.stats.eventCounts[event] || 0) + 1;
      this.stats.lastEventTime = new Date();
    }

    if (!this.listeners.has(event)) {
      return;
    }

    const listeners = this.listeners.get(event)!.slice();
    listeners.sort((a, b) => (b.priority || 0) - (a.priority || 0));

    const promises: Promise<void>[] = [];

    for (const listener of listeners) {
      try {
        const result = listener.handler(data);

        if (result instanceof Promise) {
          promises.push(result);
        }

        // 移除一次性监听器
        if (listener.once) {
          this.off(event, listener.handler);
        }
      } catch (error) {
        console.error(`事件处理器错误 [${event}]:`, error);
      }
    }

    // 等待所有异步处理器完成
    if (promises.length > 0) {
      await Promise.allSettled(promises);
    }
  }

  /**
   * 清除所有监听器
   */
  clear(): void {
    this.listeners.clear();
    this.stats.totalListeners = 0;
  }

  /**
   * 获取事件监听器
   */
  getListeners(event: string): EventHandler<any>[] {
    const listeners = this.listeners.get(event);
    return listeners ? listeners.map(l => l.handler) : [];
  }

  /**
   * 检查是否有监听器
   */
  hasListeners(event: string): boolean {
    const listeners = this.listeners.get(event);
    return listeners ? listeners.length > 0 : false;
  }

  /**
   * 获取所有事件名称
   */
  getEventNames(): string[] {
    return Array.from(this.listeners.keys());
  }

  /**
   * 获取监听器数量
   */
  getListenerCount(event?: string): number {
    if (event) {
      const listeners = this.listeners.get(event);
      return listeners ? listeners.length : 0;
    }
    return this.stats.totalListeners;
  }

  /**
   * 获取统计信息
   */
  getStats(): EventStats {
    return { ...this.stats };
  }

  /**
   * 重置统计信息
   */
  resetStats(): void {
    this.stats = {
      totalEvents: 0,
      totalListeners: this.stats.totalListeners,
      eventCounts: {}
    };
  }

  /**
   * 设置最大监听器数量
   */
  setMaxListeners(max: number): void {
    this.config.maxListeners = max;
  }

  /**
   * 添加监听器
   */
  private addListener<T>(
    event: string, 
    handler: EventHandler<T>, 
    once: boolean, 
    priority?: number
  ): EventSubscription {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }

    const listeners = this.listeners.get(event)!;

    // 检查最大监听器数量
    if (this.config.maxListeners && listeners.length >= this.config.maxListeners) {
      console.warn(`事件 ${event} 的监听器数量已达到最大值 ${this.config.maxListeners}`);
    }

    listeners.push({ handler, once, priority });
    this.stats.totalListeners++;

    return {
      unsubscribe: () => this.off(event, handler)
    };
  }
}

/**
 * 事件命名空间管理器
 */
export class NamespacedEventEmitter {
  private emitters: Map<string, EventEmitter> = new Map();

  /**
   * 获取命名空间的事件发射器
   */
  getNamespace(namespace: string): EventEmitter {
    if (!this.emitters.has(namespace)) {
      this.emitters.set(namespace, new EventEmitter());
    }
    return this.emitters.get(namespace)!;
  }

  /**
   * 在指定命名空间发射事件
   */
  emit<T>(namespace: string, event: string, data?: T): void {
    const emitter = this.getNamespace(namespace);
    emitter.emit(event, data);
  }

  /**
   * 在指定命名空间订阅事件
   */
  on<T>(namespace: string, event: string, handler: EventHandler<T>): EventSubscription {
    const emitter = this.getNamespace(namespace);
    return emitter.on(event, handler);
  }

  /**
   * 清除指定命名空间
   */
  clearNamespace(namespace: string): void {
    const emitter = this.emitters.get(namespace);
    if (emitter) {
      emitter.clear();
      this.emitters.delete(namespace);
    }
  }

  /**
   * 清除所有命名空间
   */
  clear(): void {
    this.emitters.forEach(emitter => emitter.clear());
    this.emitters.clear();
  }

  /**
   * 获取所有命名空间
   */
  getNamespaces(): string[] {
    return Array.from(this.emitters.keys());
  }
}

/**
 * 全局事件发射器实例
 */
export const eventBus = new EventEmitter({
  maxListeners: 1000,
  enableStats: true,
  enableDebug: false
});

/**
 * 全局命名空间事件管理器
 */
export const namespacedEventBus = new NamespacedEventEmitter();
