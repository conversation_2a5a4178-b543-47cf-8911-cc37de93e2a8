/**
 * 日期格式枚举
 */
export enum DateFormat {
  ISO = 'YYYY-MM-DDTHH:mm:ss.SSSZ',
  DATE = 'YYYY-MM-DD',
  TIME = 'HH:mm:ss',
  DATETIME = 'YYYY-MM-DD HH:mm:ss',
  CHINESE_DATE = 'YYYY年MM月DD日',
  CHINESE_DATETIME = 'YYYY年MM月DD日 HH:mm:ss',
  US_DATE = 'MM/DD/YYYY',
  EU_DATE = 'DD/MM/YYYY'
}

/**
 * 时间单位枚举
 */
export enum TimeUnit {
  MILLISECOND = 'millisecond',
  SECOND = 'second',
  MINUTE = 'minute',
  HOUR = 'hour',
  DAY = 'day',
  WEEK = 'week',
  MONTH = 'month',
  YEAR = 'year'
}

/**
 * 日期范围接口
 */
export interface DateRange {
  start: Date;
  end: Date;
}

/**
 * 日期差异结果
 */
export interface DateDifference {
  years: number;
  months: number;
  days: number;
  hours: number;
  minutes: number;
  seconds: number;
  milliseconds: number;
  totalDays: number;
  totalHours: number;
  totalMinutes: number;
  totalSeconds: number;
  totalMilliseconds: number;
}

/**
 * 日期工具类
 */
export class DateUtils {
  private static readonly MILLISECONDS_PER_SECOND = 1000;
  private static readonly MILLISECONDS_PER_MINUTE = 60 * 1000;
  private static readonly MILLISECONDS_PER_HOUR = 60 * 60 * 1000;
  private static readonly MILLISECONDS_PER_DAY = 24 * 60 * 60 * 1000;

  /**
   * 格式化日期
   */
  static format(date: Date, format: string = DateFormat.DATETIME): string {
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();
    const hours = date.getHours();
    const minutes = date.getMinutes();
    const seconds = date.getSeconds();
    const milliseconds = date.getMilliseconds();

    const pad = (num: number, length: number = 2): string => {
      return num.toString().padStart(length, '0');
    };

    return format
      .replace(/YYYY/g, year.toString())
      .replace(/MM/g, pad(month))
      .replace(/DD/g, pad(day))
      .replace(/HH/g, pad(hours))
      .replace(/mm/g, pad(minutes))
      .replace(/ss/g, pad(seconds))
      .replace(/SSS/g, pad(milliseconds, 3))
      .replace(/Z/g, this.getTimezoneOffset(date));
  }

  /**
   * 解析日期字符串
   */
  static parse(dateString: string, format?: string): Date {
    // 尝试标准解析
    const date = new Date(dateString);
    if (!isNaN(date.getTime())) {
      return date;
    }

    // 如果提供了格式，尝试按格式解析
    if (format) {
      return this.parseWithFormat(dateString, format);
    }

    // 尝试常见格式
    const commonFormats = [
      /^(\d{4})-(\d{2})-(\d{2})$/,  // YYYY-MM-DD
      /^(\d{4})\/(\d{2})\/(\d{2})$/, // YYYY/MM/DD
      /^(\d{2})\/(\d{2})\/(\d{4})$/, // MM/DD/YYYY
      /^(\d{2})-(\d{2})-(\d{4})$/   // DD-MM-YYYY
    ];

    for (const regex of commonFormats) {
      const match = dateString.match(regex);
      if (match) {
        const [, p1, p2, p3] = match;
        // 根据格式确定年月日的位置
        if (regex === commonFormats[2]) { // MM/DD/YYYY
          return new Date(parseInt(p3), parseInt(p1) - 1, parseInt(p2));
        } else if (regex === commonFormats[3]) { // DD-MM-YYYY
          return new Date(parseInt(p3), parseInt(p2) - 1, parseInt(p1));
        } else { // YYYY-MM-DD or YYYY/MM/DD
          return new Date(parseInt(p1), parseInt(p2) - 1, parseInt(p3));
        }
      }
    }

    throw new Error(`无法解析日期字符串: ${dateString}`);
  }

  /**
   * 按指定格式解析日期
   */
  private static parseWithFormat(dateString: string, format: string): Date {
    // 简化实现，实际项目中可能需要更复杂的解析逻辑
    const formatRegex = format
      .replace(/YYYY/g, '(\\d{4})')
      .replace(/MM/g, '(\\d{2})')
      .replace(/DD/g, '(\\d{2})')
      .replace(/HH/g, '(\\d{2})')
      .replace(/mm/g, '(\\d{2})')
      .replace(/ss/g, '(\\d{2})');

    const match = dateString.match(new RegExp(formatRegex));
    if (!match) {
      throw new Error(`日期字符串 "${dateString}" 不匹配格式 "${format}"`);
    }

    // 根据格式提取各部分
    const parts = match.slice(1);
    const formatParts = format.match(/(YYYY|MM|DD|HH|mm|ss)/g) || [];

    let year = 0, month = 0, day = 1, hours = 0, minutes = 0, seconds = 0;

    formatParts.forEach((part, index) => {
      const value = parseInt(parts[index]);
      switch (part) {
        case 'YYYY': year = value; break;
        case 'MM': month = value - 1; break; // 月份从0开始
        case 'DD': day = value; break;
        case 'HH': hours = value; break;
        case 'mm': minutes = value; break;
        case 'ss': seconds = value; break;
      }
    });

    return new Date(year, month, day, hours, minutes, seconds);
  }

  /**
   * 获取当前时间
   */
  static now(): Date {
    return new Date();
  }

  /**
   * 获取今天的开始时间
   */
  static startOfDay(date: Date = new Date()): Date {
    const result = new Date(date);
    result.setHours(0, 0, 0, 0);
    return result;
  }

  /**
   * 获取今天的结束时间
   */
  static endOfDay(date: Date = new Date()): Date {
    const result = new Date(date);
    result.setHours(23, 59, 59, 999);
    return result;
  }

  /**
   * 获取本周的开始时间（周一）
   */
  static startOfWeek(date: Date = new Date()): Date {
    const result = new Date(date);
    const day = result.getDay();
    const diff = result.getDate() - day + (day === 0 ? -6 : 1); // 调整为周一开始
    result.setDate(diff);
    return this.startOfDay(result);
  }

  /**
   * 获取本月的开始时间
   */
  static startOfMonth(date: Date = new Date()): Date {
    const result = new Date(date);
    result.setDate(1);
    return this.startOfDay(result);
  }

  /**
   * 获取本年的开始时间
   */
  static startOfYear(date: Date = new Date()): Date {
    const result = new Date(date);
    result.setMonth(0, 1);
    return this.startOfDay(result);
  }

  /**
   * 添加时间
   */
  static add(date: Date, amount: number, unit: TimeUnit): Date {
    const result = new Date(date);

    switch (unit) {
      case TimeUnit.MILLISECOND:
        result.setMilliseconds(result.getMilliseconds() + amount);
        break;
      case TimeUnit.SECOND:
        result.setSeconds(result.getSeconds() + amount);
        break;
      case TimeUnit.MINUTE:
        result.setMinutes(result.getMinutes() + amount);
        break;
      case TimeUnit.HOUR:
        result.setHours(result.getHours() + amount);
        break;
      case TimeUnit.DAY:
        result.setDate(result.getDate() + amount);
        break;
      case TimeUnit.WEEK:
        result.setDate(result.getDate() + amount * 7);
        break;
      case TimeUnit.MONTH:
        result.setMonth(result.getMonth() + amount);
        break;
      case TimeUnit.YEAR:
        result.setFullYear(result.getFullYear() + amount);
        break;
    }

    return result;
  }

  /**
   * 减去时间
   */
  static subtract(date: Date, amount: number, unit: TimeUnit): Date {
    return this.add(date, -amount, unit);
  }

  /**
   * 计算两个日期的差异
   */
  static difference(date1: Date, date2: Date): DateDifference {
    const totalMilliseconds = Math.abs(date1.getTime() - date2.getTime());
    const totalSeconds = Math.floor(totalMilliseconds / this.MILLISECONDS_PER_SECOND);
    const totalMinutes = Math.floor(totalMilliseconds / this.MILLISECONDS_PER_MINUTE);
    const totalHours = Math.floor(totalMilliseconds / this.MILLISECONDS_PER_HOUR);
    const totalDays = Math.floor(totalMilliseconds / this.MILLISECONDS_PER_DAY);

    const years = Math.floor(totalDays / 365);
    const months = Math.floor((totalDays % 365) / 30);
    const days = totalDays % 30;
    const hours = Math.floor((totalMilliseconds % this.MILLISECONDS_PER_DAY) / this.MILLISECONDS_PER_HOUR);
    const minutes = Math.floor((totalMilliseconds % this.MILLISECONDS_PER_HOUR) / this.MILLISECONDS_PER_MINUTE);
    const seconds = Math.floor((totalMilliseconds % this.MILLISECONDS_PER_MINUTE) / this.MILLISECONDS_PER_SECOND);
    const milliseconds = totalMilliseconds % this.MILLISECONDS_PER_SECOND;

    return {
      years,
      months,
      days,
      hours,
      minutes,
      seconds,
      milliseconds,
      totalDays,
      totalHours,
      totalMinutes,
      totalSeconds,
      totalMilliseconds
    };
  }

  /**
   * 检查是否为同一天
   */
  static isSameDay(date1: Date, date2: Date): boolean {
    return date1.getFullYear() === date2.getFullYear() &&
           date1.getMonth() === date2.getMonth() &&
           date1.getDate() === date2.getDate();
  }

  /**
   * 检查是否为今天
   */
  static isToday(date: Date): boolean {
    return this.isSameDay(date, new Date());
  }

  /**
   * 检查是否为昨天
   */
  static isYesterday(date: Date): boolean {
    const yesterday = this.subtract(new Date(), 1, TimeUnit.DAY);
    return this.isSameDay(date, yesterday);
  }

  /**
   * 检查是否为明天
   */
  static isTomorrow(date: Date): boolean {
    const tomorrow = this.add(new Date(), 1, TimeUnit.DAY);
    return this.isSameDay(date, tomorrow);
  }

  /**
   * 检查是否为周末
   */
  static isWeekend(date: Date): boolean {
    const day = date.getDay();
    return day === 0 || day === 6; // 周日或周六
  }

  /**
   * 检查是否为闰年
   */
  static isLeapYear(year: number): boolean {
    return (year % 4 === 0 && year % 100 !== 0) || (year % 400 === 0);
  }

  /**
   * 获取月份的天数
   */
  static getDaysInMonth(year: number, month: number): number {
    return new Date(year, month + 1, 0).getDate();
  }

  /**
   * 获取时区偏移字符串
   */
  private static getTimezoneOffset(date: Date): string {
    const offset = -date.getTimezoneOffset();
    const hours = Math.floor(Math.abs(offset) / 60);
    const minutes = Math.abs(offset) % 60;
    const sign = offset >= 0 ? '+' : '-';
    return `${sign}${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
  }

  /**
   * 相对时间描述
   */
  static getRelativeTime(date: Date, baseDate: Date = new Date()): string {
    const diff = this.difference(date, baseDate);
    const isPast = date < baseDate;
    const suffix = isPast ? '前' : '后';

    if (diff.totalDays >= 365) {
      const years = Math.floor(diff.totalDays / 365);
      return `${years}年${suffix}`;
    } else if (diff.totalDays >= 30) {
      const months = Math.floor(diff.totalDays / 30);
      return `${months}个月${suffix}`;
    } else if (diff.totalDays >= 7) {
      const weeks = Math.floor(diff.totalDays / 7);
      return `${weeks}周${suffix}`;
    } else if (diff.totalDays >= 1) {
      return `${diff.totalDays}天${suffix}`;
    } else if (diff.totalHours >= 1) {
      return `${diff.totalHours}小时${suffix}`;
    } else if (diff.totalMinutes >= 1) {
      return `${diff.totalMinutes}分钟${suffix}`;
    } else {
      return '刚刚';
    }
  }

  /**
   * 创建日期范围
   */
  static createRange(start: Date, end: Date): DateRange {
    return { start, end };
  }

  /**
   * 检查日期是否在范围内
   */
  static isInRange(date: Date, range: DateRange): boolean {
    return date >= range.start && date <= range.end;
  }

  /**
   * 获取日期范围内的所有日期
   */
  static getDatesInRange(range: DateRange): Date[] {
    const dates: Date[] = [];
    const current = new Date(range.start);

    while (current <= range.end) {
      dates.push(new Date(current));
      current.setDate(current.getDate() + 1);
    }

    return dates;
  }
}
