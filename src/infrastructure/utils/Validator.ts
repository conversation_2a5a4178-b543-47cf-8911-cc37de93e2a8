/**
 * 自定义验证器函数
 */
export type CustomValidator = (value: any) => boolean;

/**
 * 验证规则
 */
export interface ValidationRule {
  type: 'required' | 'string' | 'number' | 'boolean' | 'array' | 'object' |
        'email' | 'url' | 'uuid' | 'minLength' | 'maxLength' | 'min' | 'max' | 
        'pattern' | 'custom' | 'date' | 'integer' | 'positive' | 'negative';
  value?: any;
  message?: string;
  validator?: CustomValidator;
}

/**
 * 验证模式
 */
export interface ValidationSchema {
  [key: string]: ValidationRule[];
}

/**
 * 验证错误
 */
export interface ValidationError {
  field: string;
  message: string;
  value: any;
  rule: string;
}

/**
 * 验证结果
 */
export interface ValidationResult {
  valid: boolean;
  errors: ValidationError[];
}

/**
 * 数据验证器接口
 */
export interface IValidator {
  // 基础验证
  isRequired(value: any): boolean;
  isString(value: any): boolean;
  isNumber(value: any): boolean;
  isBoolean(value: any): boolean;
  isArray(value: any): boolean;
  isObject(value: any): boolean;
  isDate(value: any): boolean;
  isInteger(value: any): boolean;

  // 格式验证
  isEmail(value: string): boolean;
  isUrl(value: string): boolean;
  isUuid(value: string): boolean;
  matchesPattern(value: string, pattern: RegExp): boolean;

  // 范围验证
  minLength(value: string | any[], min: number): boolean;
  maxLength(value: string | any[], max: number): boolean;
  min(value: number, min: number): boolean;
  max(value: number, max: number): boolean;
  isPositive(value: number): boolean;
  isNegative(value: number): boolean;

  // 自定义验证
  custom(value: any, validator: CustomValidator): boolean;

  // 批量验证
  validate(data: any, schema: ValidationSchema): ValidationResult;
  validateField(value: any, rules: ValidationRule[]): ValidationError[];
}

/**
 * 默认验证器实现
 */
export class Validator implements IValidator {
  private defaultMessages: Record<string, string> = {
    required: '此字段是必需的',
    string: '必须是字符串类型',
    number: '必须是数字类型',
    boolean: '必须是布尔类型',
    array: '必须是数组类型',
    object: '必须是对象类型',
    date: '必须是有效的日期',
    integer: '必须是整数',
    email: '必须是有效的邮箱地址',
    url: '必须是有效的URL',
    uuid: '必须是有效的UUID',
    minLength: '长度不能少于 {value} 个字符',
    maxLength: '长度不能超过 {value} 个字符',
    min: '值不能小于 {value}',
    max: '值不能大于 {value}',
    positive: '必须是正数',
    negative: '必须是负数',
    pattern: '格式不正确',
    custom: '验证失败'
  };

  /**
   * 检查值是否存在
   */
  isRequired(value: any): boolean {
    return value !== null && value !== undefined && value !== '';
  }

  /**
   * 检查是否为字符串
   */
  isString(value: any): boolean {
    return typeof value === 'string';
  }

  /**
   * 检查是否为数字
   */
  isNumber(value: any): boolean {
    return typeof value === 'number' && !isNaN(value) && isFinite(value);
  }

  /**
   * 检查是否为布尔值
   */
  isBoolean(value: any): boolean {
    return typeof value === 'boolean';
  }

  /**
   * 检查是否为数组
   */
  isArray(value: any): boolean {
    return Array.isArray(value);
  }

  /**
   * 检查是否为对象
   */
  isObject(value: any): boolean {
    return value !== null && typeof value === 'object' && !Array.isArray(value);
  }

  /**
   * 检查是否为日期
   */
  isDate(value: any): boolean {
    return value instanceof Date && !isNaN(value.getTime());
  }

  /**
   * 检查是否为整数
   */
  isInteger(value: any): boolean {
    return this.isNumber(value) && Number.isInteger(value);
  }

  /**
   * 检查是否为有效邮箱
   */
  isEmail(value: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(value);
  }

  /**
   * 检查是否为有效URL
   */
  isUrl(value: string): boolean {
    try {
      new URL(value);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * 检查是否为有效UUID
   */
  isUuid(value: string): boolean {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return uuidRegex.test(value);
  }

  /**
   * 检查是否匹配正则表达式
   */
  matchesPattern(value: string, pattern: RegExp): boolean {
    return pattern.test(value);
  }

  /**
   * 检查最小长度
   */
  minLength(value: string | any[], min: number): boolean {
    return value.length >= min;
  }

  /**
   * 检查最大长度
   */
  maxLength(value: string | any[], max: number): boolean {
    return value.length <= max;
  }

  /**
   * 检查最小值
   */
  min(value: number, min: number): boolean {
    return value >= min;
  }

  /**
   * 检查最大值
   */
  max(value: number, max: number): boolean {
    return value <= max;
  }

  /**
   * 检查是否为正数
   */
  isPositive(value: number): boolean {
    return this.isNumber(value) && value > 0;
  }

  /**
   * 检查是否为负数
   */
  isNegative(value: number): boolean {
    return this.isNumber(value) && value < 0;
  }

  /**
   * 自定义验证
   */
  custom(value: any, validator: CustomValidator): boolean {
    try {
      return validator(value);
    } catch {
      return false;
    }
  }

  /**
   * 验证单个字段
   */
  validateField(value: any, rules: ValidationRule[]): ValidationError[] {
    const errors: ValidationError[] = [];

    for (const rule of rules) {
      let isValid = true;
      let errorMessage = rule.message || this.defaultMessages[rule.type] || '验证失败';

      switch (rule.type) {
        case 'required':
          isValid = this.isRequired(value);
          break;
        case 'string':
          isValid = !this.isRequired(value) || this.isString(value);
          break;
        case 'number':
          isValid = !this.isRequired(value) || this.isNumber(value);
          break;
        case 'boolean':
          isValid = !this.isRequired(value) || this.isBoolean(value);
          break;
        case 'array':
          isValid = !this.isRequired(value) || this.isArray(value);
          break;
        case 'object':
          isValid = !this.isRequired(value) || this.isObject(value);
          break;
        case 'date':
          isValid = !this.isRequired(value) || this.isDate(value);
          break;
        case 'integer':
          isValid = !this.isRequired(value) || this.isInteger(value);
          break;
        case 'email':
          isValid = !this.isRequired(value) || (this.isString(value) && this.isEmail(value));
          break;
        case 'url':
          isValid = !this.isRequired(value) || (this.isString(value) && this.isUrl(value));
          break;
        case 'uuid':
          isValid = !this.isRequired(value) || (this.isString(value) && this.isUuid(value));
          break;
        case 'minLength':
          isValid = !this.isRequired(value) || this.minLength(value, rule.value);
          errorMessage = errorMessage.replace('{value}', rule.value);
          break;
        case 'maxLength':
          isValid = !this.isRequired(value) || this.maxLength(value, rule.value);
          errorMessage = errorMessage.replace('{value}', rule.value);
          break;
        case 'min':
          isValid = !this.isRequired(value) || (this.isNumber(value) && this.min(value, rule.value));
          errorMessage = errorMessage.replace('{value}', rule.value);
          break;
        case 'max':
          isValid = !this.isRequired(value) || (this.isNumber(value) && this.max(value, rule.value));
          errorMessage = errorMessage.replace('{value}', rule.value);
          break;
        case 'positive':
          isValid = !this.isRequired(value) || this.isPositive(value);
          break;
        case 'negative':
          isValid = !this.isRequired(value) || this.isNegative(value);
          break;
        case 'pattern':
          isValid = !this.isRequired(value) || (this.isString(value) && this.matchesPattern(value, rule.value));
          break;
        case 'custom':
          isValid = !this.isRequired(value) || (rule.validator && this.custom(value, rule.validator));
          break;
      }

      if (!isValid) {
        errors.push({
          field: '',
          message: errorMessage,
          value,
          rule: rule.type
        });
      }
    }

    return errors;
  }

  /**
   * 批量验证
   */
  validate(data: any, schema: ValidationSchema): ValidationResult {
    const errors: ValidationError[] = [];

    for (const [field, rules] of Object.entries(schema)) {
      const value = this.getNestedValue(data, field);
      const fieldErrors = this.validateField(value, rules);
      
      fieldErrors.forEach(error => {
        error.field = field;
        errors.push(error);
      });
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * 获取嵌套对象的值
   */
  private getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : undefined;
    }, obj);
  }

  /**
   * 设置默认错误消息
   */
  setDefaultMessage(type: string, message: string): void {
    this.defaultMessages[type] = message;
  }

  /**
   * 获取默认错误消息
   */
  getDefaultMessage(type: string): string {
    return this.defaultMessages[type] || '验证失败';
  }
}

/**
 * 验证器工厂
 */
export class ValidatorFactory {
  private static instance: Validator;

  /**
   * 获取默认验证器实例
   */
  static getInstance(): Validator {
    if (!this.instance) {
      this.instance = new Validator();
    }
    return this.instance;
  }

  /**
   * 创建新的验证器实例
   */
  static create(): Validator {
    return new Validator();
  }
}

/**
 * 常用验证规则预设
 */
export const CommonValidationRules = {
  // 用户名：3-20个字符，只能包含字母、数字、下划线
  username: [
    { type: 'required' as const },
    { type: 'string' as const },
    { type: 'minLength' as const, value: 3, message: '用户名至少需要3个字符' },
    { type: 'maxLength' as const, value: 20, message: '用户名不能超过20个字符' },
    { type: 'pattern' as const, value: /^[a-zA-Z0-9_]+$/, message: '用户名只能包含字母、数字和下划线' }
  ],

  // 密码：至少8个字符，包含大小写字母和数字
  password: [
    { type: 'required' as const },
    { type: 'string' as const },
    { type: 'minLength' as const, value: 8, message: '密码至少需要8个字符' },
    { type: 'pattern' as const, value: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, message: '密码必须包含大小写字母和数字' }
  ],

  // 邮箱
  email: [
    { type: 'required' as const },
    { type: 'string' as const },
    { type: 'email' as const }
  ],

  // 手机号（中国）
  phone: [
    { type: 'required' as const },
    { type: 'string' as const },
    { type: 'pattern' as const, value: /^1[3-9]\d{9}$/, message: '请输入有效的手机号码' }
  ],

  // 身份证号（中国）
  idCard: [
    { type: 'required' as const },
    { type: 'string' as const },
    { type: 'pattern' as const, value: /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/, message: '请输入有效的身份证号码' }
  ],

  // URL
  url: [
    { type: 'required' as const },
    { type: 'string' as const },
    { type: 'url' as const }
  ],

  // 正整数
  positiveInteger: [
    { type: 'required' as const },
    { type: 'integer' as const },
    { type: 'positive' as const }
  ],

  // 年龄
  age: [
    { type: 'required' as const },
    { type: 'integer' as const },
    { type: 'min' as const, value: 0, message: '年龄不能为负数' },
    { type: 'max' as const, value: 150, message: '年龄不能超过150岁' }
  ]
};

/**
 * 验证工具类
 */
export class ValidationUtils {
  /**
   * 快速验证邮箱
   */
  static isValidEmail(email: string): boolean {
    return validator.isEmail(email);
  }

  /**
   * 快速验证URL
   */
  static isValidUrl(url: string): boolean {
    return validator.isUrl(url);
  }

  /**
   * 快速验证手机号（中国）
   */
  static isValidPhone(phone: string): boolean {
    return /^1[3-9]\d{9}$/.test(phone);
  }

  /**
   * 快速验证身份证号（中国）
   */
  static isValidIdCard(idCard: string): boolean {
    return /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/.test(idCard);
  }

  /**
   * 验证密码强度
   */
  static validatePasswordStrength(password: string): {
    score: number;
    level: 'weak' | 'medium' | 'strong' | 'very-strong';
    suggestions: string[];
  } {
    let score = 0;
    const suggestions: string[] = [];

    // 长度检查
    if (password.length >= 8) score += 1;
    else suggestions.push('密码长度至少8个字符');

    if (password.length >= 12) score += 1;

    // 字符类型检查
    if (/[a-z]/.test(password)) score += 1;
    else suggestions.push('包含小写字母');

    if (/[A-Z]/.test(password)) score += 1;
    else suggestions.push('包含大写字母');

    if (/\d/.test(password)) score += 1;
    else suggestions.push('包含数字');

    if (/[^a-zA-Z0-9]/.test(password)) score += 1;
    else suggestions.push('包含特殊字符');

    // 复杂度检查
    if (password.length >= 16) score += 1;
    if (/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[^a-zA-Z0-9])/.test(password)) score += 1;

    let level: 'weak' | 'medium' | 'strong' | 'very-strong';
    if (score <= 2) level = 'weak';
    else if (score <= 4) level = 'medium';
    else if (score <= 6) level = 'strong';
    else level = 'very-strong';

    return { score, level, suggestions };
  }

  /**
   * 创建自定义验证规则
   */
  static createCustomRule(
    validator: CustomValidator,
    message: string = '验证失败'
  ): ValidationRule {
    return {
      type: 'custom',
      validator,
      message
    };
  }

  /**
   * 组合多个验证规则
   */
  static combineRules(...ruleArrays: ValidationRule[][]): ValidationRule[] {
    return ruleArrays.flat();
  }

  /**
   * 创建条件验证规则
   */
  static createConditionalRule(
    condition: (data: any) => boolean,
    rules: ValidationRule[]
  ): ValidationRule {
    return {
      type: 'custom',
      validator: (value: any) => {
        // 这里需要访问完整的数据对象，但当前接口限制只能访问单个值
        // 实际使用时可能需要扩展接口
        return true;
      },
      message: '条件验证失败'
    };
  }
}

/**
 * 全局验证器实例
 */
export const validator = ValidatorFactory.getInstance();
