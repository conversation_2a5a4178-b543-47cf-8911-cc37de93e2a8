/**
 * 日志级别枚举
 */
export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3
}

/**
 * 日志记录器接口
 */
export interface ILogger {
  debug(message: string, ...args: any[]): void;
  info(message: string, ...args: any[]): void;
  warn(message: string, ...args: any[]): void;
  error(message: string, ...args: any[]): void;

  setLevel(level: LogLevel): void;
  getLevel(): LogLevel;

  addAppender(appender: ILogAppender): void;
  removeAppender(appender: ILogAppender): void;
}

/**
 * 日志输出器接口
 */
export interface ILogAppender {
  append(level: LogLevel, message: string, timestamp: Date, ...args: any[]): void;
}

/**
 * 控制台日志输出器
 */
export class ConsoleAppender implements ILogAppender {
  private enableColors: boolean;

  constructor(enableColors: boolean = true) {
    this.enableColors = enableColors;
  }

  append(level: LogLevel, message: string, timestamp: Date, ...args: any[]): void {
    const timeStr = timestamp.toISOString();
    const levelStr = LogLevel[level].padEnd(5);
    const fullMessage = `[${timeStr}] ${levelStr} ${message}`;

    if (this.enableColors && typeof window === 'undefined') {
      // Node.js环境下的颜色支持
      const colors = {
        [LogLevel.DEBUG]: '\x1b[36m', // 青色
        [LogLevel.INFO]: '\x1b[32m',  // 绿色
        [LogLevel.WARN]: '\x1b[33m',  // 黄色
        [LogLevel.ERROR]: '\x1b[31m'  // 红色
      };
      const reset = '\x1b[0m';
      const coloredMessage = `${colors[level]}${fullMessage}${reset}`;
      console.log(coloredMessage, ...args);
    } else {
      // 浏览器环境
      switch (level) {
        case LogLevel.DEBUG:
          console.debug(fullMessage, ...args);
          break;
        case LogLevel.INFO:
          console.info(fullMessage, ...args);
          break;
        case LogLevel.WARN:
          console.warn(fullMessage, ...args);
          break;
        case LogLevel.ERROR:
          console.error(fullMessage, ...args);
          break;
      }
    }
  }
}

/**
 * 内存日志输出器
 */
export class MemoryAppender implements ILogAppender {
  private logs: LogEntry[] = [];
  private maxSize: number;

  constructor(maxSize: number = 1000) {
    this.maxSize = maxSize;
  }

  append(level: LogLevel, message: string, timestamp: Date, ...args: any[]): void {
    this.logs.push({
      level,
      message,
      timestamp,
      args
    });

    // 保持日志数量在限制内
    if (this.logs.length > this.maxSize) {
      this.logs.shift();
    }
  }

  getLogs(): LogEntry[] {
    return [...this.logs];
  }

  getLogsByLevel(level: LogLevel): LogEntry[] {
    return this.logs.filter(log => log.level === level);
  }

  getLogsAfter(timestamp: Date): LogEntry[] {
    return this.logs.filter(log => log.timestamp > timestamp);
  }

  clear(): void {
    this.logs = [];
  }

  export(): string {
    return JSON.stringify(this.logs, null, 2);
  }
}

/**
 * 文件日志输出器（浏览器环境下载文件）
 */
export class FileAppender implements ILogAppender {
  private logs: string[] = [];
  private maxSize: number;
  private autoDownload: boolean;

  constructor(maxSize: number = 10000, autoDownload: boolean = false) {
    this.maxSize = maxSize;
    this.autoDownload = autoDownload;
  }

  append(level: LogLevel, message: string, timestamp: Date, ...args: any[]): void {
    const timeStr = timestamp.toISOString();
    const levelStr = LogLevel[level].padEnd(5);
    const argsStr = args.length > 0 ? ' ' + args.map(arg => 
      typeof arg === 'object' ? JSON.stringify(arg) : String(arg)
    ).join(' ') : '';
    
    const logLine = `[${timeStr}] ${levelStr} ${message}${argsStr}`;
    this.logs.push(logLine);

    // 保持日志数量在限制内
    if (this.logs.length > this.maxSize) {
      this.logs.shift();
    }

    // 自动下载（仅在错误级别）
    if (this.autoDownload && level === LogLevel.ERROR) {
      this.downloadLogs();
    }
  }

  downloadLogs(filename?: string): void {
    const content = this.logs.join('\n');
    const blob = new Blob([content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    
    const a = document.createElement('a');
    a.href = url;
    a.download = filename || `logs_${new Date().toISOString().split('T')[0]}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }

  clear(): void {
    this.logs = [];
  }
}

/**
 * 日志条目
 */
export interface LogEntry {
  level: LogLevel;
  message: string;
  timestamp: Date;
  args: any[];
}

/**
 * 日志配置
 */
export interface LoggerConfig {
  level?: LogLevel;
  enableConsole?: boolean;
  enableMemory?: boolean;
  memorySize?: number;
  enableFile?: boolean;
  fileSize?: number;
  autoDownload?: boolean;
}

/**
 * 默认日志记录器实现
 */
export class Logger implements ILogger {
  private level: LogLevel = LogLevel.INFO;
  private appenders: ILogAppender[] = [];
  private memoryAppender?: MemoryAppender;

  constructor(config?: LoggerConfig) {
    this.configure(config || {});
  }

  debug(message: string, ...args: any[]): void {
    this.log(LogLevel.DEBUG, message, ...args);
  }

  info(message: string, ...args: any[]): void {
    this.log(LogLevel.INFO, message, ...args);
  }

  warn(message: string, ...args: any[]): void {
    this.log(LogLevel.WARN, message, ...args);
  }

  error(message: string, ...args: any[]): void {
    this.log(LogLevel.ERROR, message, ...args);
  }

  setLevel(level: LogLevel): void {
    this.level = level;
  }

  getLevel(): LogLevel {
    return this.level;
  }

  addAppender(appender: ILogAppender): void {
    this.appenders.push(appender);
  }

  removeAppender(appender: ILogAppender): void {
    const index = this.appenders.indexOf(appender);
    if (index > -1) {
      this.appenders.splice(index, 1);
    }
  }

  configure(config: LoggerConfig): void {
    // 设置日志级别
    if (config.level !== undefined) {
      this.level = config.level;
    }

    // 清除现有appenders
    this.appenders = [];

    // 添加控制台appender
    if (config.enableConsole !== false) {
      this.addAppender(new ConsoleAppender());
    }

    // 添加内存appender
    if (config.enableMemory !== false) {
      this.memoryAppender = new MemoryAppender(config.memorySize || 1000);
      this.addAppender(this.memoryAppender);
    }

    // 添加文件appender
    if (config.enableFile) {
      this.addAppender(new FileAppender(
        config.fileSize || 10000,
        config.autoDownload || false
      ));
    }
  }

  getMemoryLogs(): LogEntry[] {
    return this.memoryAppender?.getLogs() || [];
  }

  clearMemoryLogs(): void {
    this.memoryAppender?.clear();
  }

  exportLogs(): string {
    return this.memoryAppender?.export() || '[]';
  }

  private log(level: LogLevel, message: string, ...args: any[]): void {
    if (level >= this.level) {
      const timestamp = new Date();
      this.appenders.forEach(appender => {
        try {
          appender.append(level, message, timestamp, ...args);
        } catch (error) {
          // 避免日志记录器本身出错导致应用崩溃
          console.error('日志输出器错误:', error);
        }
      });
    }
  }
}

/**
 * 日志记录器工厂
 */
export class LoggerFactory {
  private static loggers: Map<string, Logger> = new Map();
  private static defaultConfig: LoggerConfig = {
    level: LogLevel.INFO,
    enableConsole: true,
    enableMemory: true,
    memorySize: 1000
  };

  /**
   * 获取或创建日志记录器
   */
  static getLogger(name: string = 'default', config?: LoggerConfig): Logger {
    if (!this.loggers.has(name)) {
      const loggerConfig = { ...this.defaultConfig, ...config };
      this.loggers.set(name, new Logger(loggerConfig));
    }
    return this.loggers.get(name)!;
  }

  /**
   * 设置默认配置
   */
  static setDefaultConfig(config: LoggerConfig): void {
    this.defaultConfig = { ...this.defaultConfig, ...config };
  }

  /**
   * 清除所有日志记录器
   */
  static clear(): void {
    this.loggers.clear();
  }

  /**
   * 获取所有日志记录器名称
   */
  static getLoggerNames(): string[] {
    return Array.from(this.loggers.keys());
  }
}

/**
 * 全局日志记录器实例
 */
export const logger = LoggerFactory.getLogger('global');
