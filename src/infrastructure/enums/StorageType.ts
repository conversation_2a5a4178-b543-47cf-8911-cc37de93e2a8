/**
 * 存储类型枚举
 * 定义系统支持的所有存储类型
 */
export enum StorageType {
  // 云存储提供者
  HUAWEI_OBS = 'huaweiObs',
  MINIO = 'minio',
  AMAZON_S3 = 'amazonS3',
  ALIYUN_OSS = 'aliyunOss',
  TENCENT_COS = 'tencentCos',
  
  // 本地存储
  LOCAL_STORAGE = 'localStorage',
  INDEXED_DB = 'indexedDB',
  
  // 内存存储（测试用）
  MEMORY_STORAGE = 'memoryStorage'
}

/**
 * 存储类型工具函数
 */
export class StorageTypeUtils {
  /**
   * 获取所有支持的存储类型
   */
  static getAllTypes(): StorageType[] {
    return Object.values(StorageType);
  }
  
  /**
   * 检查存储类型是否有效
   */
  static isValidType(type: string): type is StorageType {
    return Object.values(StorageType).includes(type as StorageType);
  }
  
  /**
   * 获取存储类型的显示名称
   */
  static getDisplayName(type: StorageType): string {
    const displayNames: Record<StorageType, string> = {
      [StorageType.HUAWEI_OBS]: '华为云对象存储',
      [StorageType.MINIO]: 'MinIO对象存储',
      [StorageType.AMAZON_S3]: '亚马逊S3存储',
      [StorageType.ALIYUN_OSS]: '阿里云对象存储',
      [StorageType.TENCENT_COS]: '腾讯云对象存储',
      [StorageType.LOCAL_STORAGE]: '本地存储',
      [StorageType.INDEXED_DB]: '浏览器数据库',
      [StorageType.MEMORY_STORAGE]: '内存存储'
    };
    return displayNames[type] || type;
  }
  
  /**
   * 检查是否为云存储类型
   */
  static isCloudStorage(type: StorageType): boolean {
    const cloudTypes = [
      StorageType.HUAWEI_OBS,
      StorageType.MINIO,
      StorageType.AMAZON_S3,
      StorageType.ALIYUN_OSS,
      StorageType.TENCENT_COS
    ];
    return cloudTypes.includes(type);
  }
  
  /**
   * 检查是否为本地存储类型
   */
  static isLocalStorage(type: StorageType): boolean {
    const localTypes = [
      StorageType.LOCAL_STORAGE,
      StorageType.INDEXED_DB,
      StorageType.MEMORY_STORAGE
    ];
    return localTypes.includes(type);
  }
}
