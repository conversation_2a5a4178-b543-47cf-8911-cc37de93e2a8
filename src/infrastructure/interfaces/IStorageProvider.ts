import { StorageType } from '../enums/StorageType';
import { IStorageConfig } from '../types/StorageConfig';
import { 
  StorageResult, 
  ObjectMetadata, 
  GetOptions, 
  PutOptions, 
  ListOptions, 
  SignedUrlOptions,
  BatchOptions,
  StreamOptions,
  StorageStats
} from '../types/StorageResult';

/**
 * 存储提供者统一接口
 * 所有存储提供者必须实现此接口
 */
export interface IStorageProvider {
  // 基本属性
  readonly name: string;
  readonly type: StorageType;
  readonly isInitialized: boolean;
  
  // 生命周期管理
  initialize(config: IStorageConfig): Promise<void>;
  dispose(): Promise<void>;
  
  // 连接测试
  testConnection(): Promise<boolean>;
  
  // 基础CRUD操作
  get(key: string, options?: GetOptions): Promise<StorageResult<any>>;
  put(key: string, data: any, options?: PutOptions): Promise<StorageResult<void>>;
  delete(key: string): Promise<StorageResult<void>>;
  list(prefix?: string, options?: ListOptions): Promise<StorageResult<string[]>>;
  
  // 批量操作
  getBatch(keys: string[], options?: BatchOptions): Promise<StorageResult<Record<string, any>>>;
  putBatch(items: Record<string, any>, options?: BatchOptions): Promise<StorageResult<void>>;
  deleteBatch(keys: string[], options?: BatchOptions): Promise<StorageResult<void>>;
  
  // 元数据操作
  getMetadata(key: string): Promise<StorageResult<ObjectMetadata>>;
  
  // 流式操作（大文件支持）
  getStream(key: string, options?: StreamOptions): Promise<ReadableStream>;
  putStream(key: string, stream: ReadableStream, options?: PutOptions & StreamOptions): Promise<StorageResult<void>>;
  
  // URL生成
  getSignedUrl(key: string, options?: SignedUrlOptions): Promise<string>;
  
  // 统计信息
  getStats(): Promise<StorageResult<StorageStats>>;
  
  // 配置管理
  getConfig(): IStorageConfig;
  updateConfig(config: Partial<IStorageConfig>): Promise<void>;
}

/**
 * 存储提供者抽象基类
 * 提供通用功能的默认实现
 */
export abstract class BaseStorageProvider implements IStorageProvider {
  protected config!: IStorageConfig;
  protected initialized: boolean = false;
  
  constructor(protected readonly providerName: string, protected readonly providerType: StorageType) {}
  
  get name(): string {
    return this.providerName;
  }
  
  get type(): StorageType {
    return this.providerType;
  }
  
  get isInitialized(): boolean {
    return this.initialized;
  }
  
  getConfig(): IStorageConfig {
    return { ...this.config };
  }
  
  /**
   * 验证是否已初始化
   */
  protected ensureInitialized(): void {
    if (!this.initialized) {
      throw new Error(`存储提供者 ${this.name} 尚未初始化`);
    }
  }
  
  /**
   * 重试机制包装器
   */
  protected async withRetry<T>(
    operation: () => Promise<T>,
    maxRetries?: number,
    retryDelay?: number
  ): Promise<T> {
    const retries = maxRetries || this.config?.retryCount || 3;
    const delay = retryDelay || this.config?.retryDelay || 1000;
    
    let lastError: Error;
    
    for (let attempt = 0; attempt <= retries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error as Error;
        
        if (attempt === retries) {
          break;
        }
        
        // 等待后重试
        await new Promise(resolve => setTimeout(resolve, delay * Math.pow(2, attempt)));
      }
    }
    
    throw lastError!;
  }
  
  /**
   * 超时控制包装器
   */
  protected async withTimeout<T>(
    operation: Promise<T>,
    timeoutMs?: number
  ): Promise<T> {
    const timeout = timeoutMs || this.config?.timeout || 30000;
    
    return Promise.race([
      operation,
      new Promise<never>((_, reject) => {
        setTimeout(() => {
          reject(new Error(`操作超时: ${timeout}ms`));
        }, timeout);
      })
    ]);
  }
  
  // 抽象方法，子类必须实现
  abstract initialize(config: IStorageConfig): Promise<void>;
  abstract dispose(): Promise<void>;
  abstract testConnection(): Promise<boolean>;
  abstract get(key: string, options?: GetOptions): Promise<StorageResult<any>>;
  abstract put(key: string, data: any, options?: PutOptions): Promise<StorageResult<void>>;
  abstract delete(key: string): Promise<StorageResult<void>>;
  abstract list(prefix?: string, options?: ListOptions): Promise<StorageResult<string[]>>;
  abstract getBatch(keys: string[], options?: BatchOptions): Promise<StorageResult<Record<string, any>>>;
  abstract putBatch(items: Record<string, any>, options?: BatchOptions): Promise<StorageResult<void>>;
  abstract deleteBatch(keys: string[], options?: BatchOptions): Promise<StorageResult<void>>;
  abstract getMetadata(key: string): Promise<StorageResult<ObjectMetadata>>;
  abstract getStream(key: string, options?: StreamOptions): Promise<ReadableStream>;
  abstract putStream(key: string, stream: ReadableStream, options?: PutOptions & StreamOptions): Promise<StorageResult<void>>;
  abstract getSignedUrl(key: string, options?: SignedUrlOptions): Promise<string>;
  abstract getStats(): Promise<StorageResult<StorageStats>>;
  abstract updateConfig(config: Partial<IStorageConfig>): Promise<void>;
}
