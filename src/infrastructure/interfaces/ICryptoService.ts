/**
 * 加密服务统一接口
 */
export interface ICryptoService {
  // 基础加密解密
  encrypt(data: string | ArrayBuffer, key?: string): Promise<EncryptResult>;
  decrypt(encryptedData: string | ArrayBuffer, key?: string): Promise<DecryptResult>;
  
  // 密钥管理
  generateKey(): Promise<string>;
  deriveKey(password: string, salt: string): Promise<string>;
  
  // 哈希函数
  hash(data: string | ArrayBuffer, algorithm?: HashAlgorithm): Promise<string>;
  
  // 流式加密
  createEncryptStream(key: string): TransformStream;
  createDecryptStream(key: string): TransformStream;
  
  // 数字签名
  sign(data: string | ArrayBuffer, privateKey: string): Promise<string>;
  verify(data: string | ArrayBuffer, signature: string, publicKey: string): Promise<boolean>;
}

/**
 * 加密结果
 */
export interface EncryptResult {
  encryptedData: string;
  iv: string;
  salt?: string;
  algorithm: string;
}

/**
 * 解密结果
 */
export interface DecryptResult {
  decryptedData: string | ArrayBuffer;
  algorithm: string;
}

/**
 * 哈希算法枚举
 */
export enum HashAlgorithm {
  SHA256 = 'SHA-256',
  SHA512 = 'SHA-512',
  SHA1 = 'SHA-1'
}

/**
 * 加密算法枚举
 */
export enum EncryptionAlgorithm {
  AES_GCM = 'AES-GCM',
  AES_CBC = 'AES-CBC',
  AES_CTR = 'AES-CTR'
}

/**
 * 密钥派生算法枚举
 */
export enum KeyDerivationAlgorithm {
  PBKDF2 = 'PBKDF2'
}

/**
 * 加密配置选项
 */
export interface CryptoOptions {
  algorithm?: EncryptionAlgorithm;
  keyLength?: number;
  iterations?: number;
  ivLength?: number;
}

/**
 * 密钥信息
 */
export interface KeyInfo {
  id: string;
  algorithm: string;
  length: number;
  createdAt: Date;
  expiresAt?: Date;
}

/**
 * 加密上下文
 */
export interface CryptoContext {
  algorithm: EncryptionAlgorithm;
  keyId?: string;
  iv: string;
  salt?: string;
  metadata?: Record<string, any>;
}

/**
 * 高级加密服务接口
 */
export interface IAdvancedCryptoService extends ICryptoService {
  // 密钥管理
  storeKey(keyId: string, key: string, metadata?: Record<string, any>): Promise<void>;
  retrieveKey(keyId: string): Promise<string>;
  deleteKey(keyId: string): Promise<void>;
  listKeys(): Promise<KeyInfo[]>;
  
  // 批量操作
  encryptBatch(items: Array<{ id: string; data: string | ArrayBuffer }>): Promise<Array<{ id: string; result: EncryptResult }>>;
  decryptBatch(items: Array<{ id: string; encryptedData: string | ArrayBuffer; key: string }>): Promise<Array<{ id: string; result: DecryptResult }>>;
  
  // 文件加密
  encryptFile(file: File, key?: string): Promise<EncryptResult>;
  decryptFile(encryptedData: ArrayBuffer, key: string): Promise<File>;
  
  // 密钥轮换
  rotateKey(oldKeyId: string): Promise<string>;
  migrateData(oldKey: string, newKey: string, data: string): Promise<string>;
}

/**
 * 密钥存储接口
 */
export interface IKeyStore {
  store(keyId: string, key: string, metadata?: Record<string, any>): Promise<void>;
  retrieve(keyId: string): Promise<string | null>;
  delete(keyId: string): Promise<void>;
  list(): Promise<KeyInfo[]>;
  exists(keyId: string): Promise<boolean>;
}

/**
 * 加密事件类型
 */
export enum CryptoEventType {
  KEY_GENERATED = 'key_generated',
  KEY_DERIVED = 'key_derived',
  DATA_ENCRYPTED = 'data_encrypted',
  DATA_DECRYPTED = 'data_decrypted',
  KEY_ROTATED = 'key_rotated',
  ERROR = 'error'
}

/**
 * 加密事件
 */
export interface CryptoEvent {
  type: CryptoEventType;
  timestamp: Date;
  keyId?: string;
  algorithm?: string;
  dataSize?: number;
  error?: Error;
  metadata?: Record<string, any>;
}

/**
 * 加密监听器
 */
export type CryptoEventListener = (event: CryptoEvent) => void;

/**
 * 加密服务监控接口
 */
export interface ICryptoMonitor {
  addEventListener(type: CryptoEventType, listener: CryptoEventListener): void;
  removeEventListener(type: CryptoEventType, listener: CryptoEventListener): void;
  getStats(): CryptoStats;
  resetStats(): void;
}

/**
 * 加密统计信息
 */
export interface CryptoStats {
  totalOperations: number;
  encryptOperations: number;
  decryptOperations: number;
  keyGenerations: number;
  errors: number;
  averageOperationTime: number;
  lastOperationTime?: Date;
}

/**
 * 密钥派生选项
 */
export interface KeyDerivationOptions {
  algorithm?: KeyDerivationAlgorithm;
  iterations?: number;
  keyLength?: number;
  hashAlgorithm?: HashAlgorithm;
}

/**
 * 签名选项
 */
export interface SignatureOptions {
  algorithm?: 'RSASSA-PKCS1-v1_5' | 'RSA-PSS' | 'ECDSA';
  hashAlgorithm?: HashAlgorithm;
}

/**
 * 密钥对
 */
export interface KeyPair {
  publicKey: string;
  privateKey: string;
  algorithm: string;
}

/**
 * 非对称加密服务接口
 */
export interface IAsymmetricCryptoService {
  generateKeyPair(algorithm?: string): Promise<KeyPair>;
  encryptWithPublicKey(data: string | ArrayBuffer, publicKey: string): Promise<string>;
  decryptWithPrivateKey(encryptedData: string, privateKey: string): Promise<ArrayBuffer>;
  signWithPrivateKey(data: string | ArrayBuffer, privateKey: string, options?: SignatureOptions): Promise<string>;
  verifyWithPublicKey(data: string | ArrayBuffer, signature: string, publicKey: string, options?: SignatureOptions): Promise<boolean>;
}

/**
 * 加密工具类
 */
export class CryptoUtils {
  /**
   * 生成随机盐值
   */
  static generateSalt(length: number = 16): string {
    const array = new Uint8Array(length);
    crypto.getRandomValues(array);
    return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
  }

  /**
   * 生成随机IV
   */
  static generateIV(length: number = 12): Uint8Array {
    return crypto.getRandomValues(new Uint8Array(length));
  }

  /**
   * 安全比较两个字符串
   */
  static secureCompare(a: string, b: string): boolean {
    if (a.length !== b.length) {
      return false;
    }

    let result = 0;
    for (let i = 0; i < a.length; i++) {
      result |= a.charCodeAt(i) ^ b.charCodeAt(i);
    }

    return result === 0;
  }

  /**
   * 验证密钥强度
   */
  static validateKeyStrength(key: string): {
    isValid: boolean;
    score: number;
    issues: string[];
  } {
    const issues: string[] = [];
    let score = 0;

    if (key.length < 8) {
      issues.push('密钥长度至少需要8个字符');
    } else {
      score += Math.min(key.length * 2, 20);
    }

    if (!/[a-z]/.test(key)) {
      issues.push('密钥应包含小写字母');
    } else {
      score += 10;
    }

    if (!/[A-Z]/.test(key)) {
      issues.push('密钥应包含大写字母');
    } else {
      score += 10;
    }

    if (!/[0-9]/.test(key)) {
      issues.push('密钥应包含数字');
    } else {
      score += 10;
    }

    if (!/[^a-zA-Z0-9]/.test(key)) {
      issues.push('密钥应包含特殊字符');
    } else {
      score += 15;
    }

    return {
      isValid: issues.length === 0 && score >= 50,
      score: Math.min(score, 100),
      issues
    };
  }

  /**
   * 格式化加密数据
   */
  static formatEncryptedData(result: EncryptResult): string {
    return `${result.algorithm}:${result.iv}:${result.encryptedData}${result.salt ? ':' + result.salt : ''}`;
  }

  /**
   * 解析加密数据
   */
  static parseEncryptedData(formattedData: string): {
    algorithm: string;
    iv: string;
    encryptedData: string;
    salt?: string;
  } {
    const parts = formattedData.split(':');
    if (parts.length < 3) {
      throw new Error('加密数据格式错误');
    }

    return {
      algorithm: parts[0],
      iv: parts[1],
      encryptedData: parts[2],
      salt: parts[3]
    };
  }

  /**
   * 计算数据熵
   */
  static calculateEntropy(data: string): number {
    const frequency: Record<string, number> = {};
    
    for (const char of data) {
      frequency[char] = (frequency[char] || 0) + 1;
    }

    let entropy = 0;
    const length = data.length;

    for (const count of Object.values(frequency)) {
      const probability = count / length;
      entropy -= probability * Math.log2(probability);
    }

    return entropy;
  }
}
