/**
 * HTTP客户端统一接口
 */
export interface IHttpClient {
  // 基础HTTP方法
  get<T>(url: string, options?: RequestOptions): Promise<HttpResponse<T>>;
  post<T>(url: string, data?: any, options?: RequestOptions): Promise<HttpResponse<T>>;
  put<T>(url: string, data?: any, options?: RequestOptions): Promise<HttpResponse<T>>;
  delete<T>(url: string, options?: RequestOptions): Promise<HttpResponse<T>>;
  patch<T>(url: string, data?: any, options?: RequestOptions): Promise<HttpResponse<T>>;
  head<T>(url: string, options?: RequestOptions): Promise<HttpResponse<T>>;
  
  // 流式请求
  getStream(url: string, options?: RequestOptions): Promise<ReadableStream>;
  postStream(url: string, stream: ReadableStream, options?: RequestOptions): Promise<HttpResponse<void>>;
  
  // 拦截器管理
  addRequestInterceptor(interceptor: RequestInterceptor): string;
  addResponseInterceptor(interceptor: ResponseInterceptor): string;
  removeRequestInterceptor(id: string): void;
  removeResponseInterceptor(id: string): void;
  
  // 配置管理
  setDefaultConfig(config: Partial<RequestOptions>): void;
  getDefaultConfig(): RequestOptions;
}

/**
 * 请求选项
 */
export interface RequestOptions {
  headers?: Record<string, string>;
  timeout?: number;
  retryCount?: number;
  retryDelay?: number;
  responseType?: 'json' | 'text' | 'blob' | 'arraybuffer';
  withCredentials?: boolean;
  signal?: AbortSignal;
}

/**
 * HTTP响应
 */
export interface HttpResponse<T> {
  data: T;
  status: number;
  statusText: string;
  headers: Record<string, string>;
  config: RequestOptions;
}

/**
 * 请求配置接口
 */
export interface RequestConfig extends RequestOptions {
  url: string;
  method: string;
  data?: any;
}

/**
 * 拦截器类型
 */
export type RequestInterceptor = (config: RequestConfig) =>
  RequestConfig | Promise<RequestConfig>;

export type ResponseInterceptor = <T>(response: HttpResponse<T>) => 
  HttpResponse<T> | Promise<HttpResponse<T>>;

/**
 * HTTP错误
 */
export class HttpError extends Error {
  constructor(
    message: string,
    public status: number,
    public response?: any,
    public config?: RequestOptions
  ) {
    super(message);
    this.name = 'HttpError';
  }
}

/**
 * 网络状态枚举
 */
export enum NetworkStatus {
  ONLINE = 'online',
  OFFLINE = 'offline',
  SLOW = 'slow'
}

/**
 * 网络监控接口
 */
export interface INetworkMonitor {
  getStatus(): NetworkStatus;
  onStatusChange(callback: (status: NetworkStatus) => void): () => void;
  isOnline(): boolean;
  getConnectionSpeed(): number; // Mbps
}

/**
 * 请求统计信息
 */
export interface RequestStats {
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  averageResponseTime: number;
  lastRequestTime?: Date;
}

/**
 * 高级HTTP客户端接口
 */
export interface IAdvancedHttpClient extends IHttpClient {
  // 批量请求
  batch<T>(requests: BatchRequest[]): Promise<BatchResponse<T>[]>;
  
  // 并发控制
  setConcurrencyLimit(limit: number): void;
  getConcurrencyLimit(): number;
  
  // 缓存控制
  enableCache(options?: CacheOptions): void;
  disableCache(): void;
  clearCache(): void;
  
  // 统计信息
  getStats(): RequestStats;
  resetStats(): void;
  
  // 网络监控
  getNetworkMonitor(): INetworkMonitor;
}

/**
 * 批量请求
 */
export interface BatchRequest {
  id: string;
  method: string;
  url: string;
  data?: any;
  options?: RequestOptions;
}

/**
 * 批量响应
 */
export interface BatchResponse<T> {
  id: string;
  success: boolean;
  response?: HttpResponse<T>;
  error?: Error;
}

/**
 * 缓存选项
 */
export interface CacheOptions {
  maxAge?: number; // 缓存时间（毫秒）
  maxSize?: number; // 最大缓存条目数
  strategy?: 'memory' | 'localStorage' | 'sessionStorage';
}

/**
 * 请求重试策略
 */
export interface RetryStrategy {
  maxRetries: number;
  baseDelay: number;
  maxDelay: number;
  backoffFactor: number;
  retryCondition?: (error: Error, attempt: number) => boolean;
}

/**
 * 请求队列接口
 */
export interface IRequestQueue {
  enqueue(request: QueuedRequest): Promise<string>;
  dequeue(): Promise<QueuedRequest | null>;
  peek(): Promise<QueuedRequest | null>;
  size(): number;
  clear(): void;
  process(): Promise<void>;
}

/**
 * 队列中的请求
 */
export interface QueuedRequest {
  id: string;
  url: string;
  method: string;
  data?: any;
  options?: RequestOptions;
  priority?: number;
  timestamp: Date;
  retryCount?: number;
}

/**
 * 请求优先级
 */
export enum RequestPriority {
  LOW = 1,
  NORMAL = 2,
  HIGH = 3,
  URGENT = 4
}

/**
 * 网络适配器接口
 */
export interface INetworkAdapter {
  request<T>(config: RequestOptions & { url: string; method: string; data?: any }): Promise<HttpResponse<T>>;
  isSupported(): boolean;
  getName(): string;
}

/**
 * 请求上下文
 */
export interface RequestContext {
  requestId: string;
  startTime: Date;
  url: string;
  method: string;
  headers: Record<string, string>;
  metadata?: Record<string, any>;
}

/**
 * 响应上下文
 */
export interface ResponseContext extends RequestContext {
  endTime: Date;
  duration: number;
  status: number;
  statusText: string;
  responseHeaders: Record<string, string>;
  success: boolean;
  error?: Error;
}

/**
 * 请求生命周期钩子
 */
export interface RequestHooks {
  onRequest?: (context: RequestContext) => void | Promise<void>;
  onResponse?: (context: ResponseContext) => void | Promise<void>;
  onError?: (context: ResponseContext) => void | Promise<void>;
  onRetry?: (context: RequestContext, attempt: number) => void | Promise<void>;
}

/**
 * HTTP客户端工厂
 */
export interface IHttpClientFactory {
  create(config?: Partial<RequestOptions>): IHttpClient;
  createAdvanced(config?: Partial<RequestOptions>): IAdvancedHttpClient;
  createWithAdapter(adapter: INetworkAdapter, config?: Partial<RequestOptions>): IHttpClient;
}

/**
 * 请求中间件
 */
export type RequestMiddleware = (
  context: RequestContext,
  next: () => Promise<HttpResponse<any>>
) => Promise<HttpResponse<any>>;

/**
 * 中间件管理器接口
 */
export interface IMiddlewareManager {
  use(middleware: RequestMiddleware): void;
  remove(middleware: RequestMiddleware): void;
  clear(): void;
  execute<T>(context: RequestContext, finalHandler: () => Promise<HttpResponse<T>>): Promise<HttpResponse<T>>;
}
