import { RequestInterceptor, ResponseInterceptor } from '../interfaces/IHttpClient';

/**
 * 认证拦截器
 */
export const createAuthInterceptor = (getToken: () => string | null): RequestInterceptor => {
  return (config) => {
    const token = getToken();
    if (token) {
      config.headers = {
        ...config.headers,
        'Authorization': `Bearer ${token}`
      };
    }
    return config;
  };
};

/**
 * 日志拦截器
 */
export const createLoggingInterceptor = (enableDebug = false): RequestInterceptor => {
  return (config) => {
    if (enableDebug) {
      console.log(`[HTTP] ${config.method} ${config.url}`, {
        headers: config.headers,
        data: config.data
      });
    }
    return config;
  };
};

/**
 * 用户代理拦截器
 */
export const createUserAgentInterceptor = (userAgent: string): RequestInterceptor => {
  return (config) => {
    config.headers = {
      ...config.headers,
      'User-Agent': userAgent
    };
    return config;
  };
};

/**
 * 内容类型拦截器
 */
export const createContentTypeInterceptor = (contentType: string): RequestInterceptor => {
  return (config) => {
    if (config.data && !config.headers?.['Content-Type']) {
      config.headers = {
        ...config.headers,
        'Content-Type': contentType
      };
    }
    return config;
  };
};

/**
 * 请求ID拦截器
 */
export const createRequestIdInterceptor = (): RequestInterceptor => {
  return (config) => {
    const requestId = `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    config.headers = {
      ...config.headers,
      'X-Request-ID': requestId
    };
    return config;
  };
};

/**
 * 时间戳拦截器
 */
export const createTimestampInterceptor = (): RequestInterceptor => {
  return (config) => {
    config.headers = {
      ...config.headers,
      'X-Timestamp': new Date().toISOString()
    };
    return config;
  };
};

/**
 * API版本拦截器
 */
export const createApiVersionInterceptor = (version: string): RequestInterceptor => {
  return (config) => {
    config.headers = {
      ...config.headers,
      'X-API-Version': version
    };
    return config;
  };
};

/**
 * 语言拦截器
 */
export const createLanguageInterceptor = (language: string): RequestInterceptor => {
  return (config) => {
    config.headers = {
      ...config.headers,
      'Accept-Language': language
    };
    return config;
  };
};

/**
 * 缓存控制拦截器
 */
export const createCacheControlInterceptor = (cacheControl: string): RequestInterceptor => {
  return (config) => {
    config.headers = {
      ...config.headers,
      'Cache-Control': cacheControl
    };
    return config;
  };
};

/**
 * 自定义头部拦截器
 */
export const createCustomHeadersInterceptor = (headers: Record<string, string>): RequestInterceptor => {
  return (config) => {
    config.headers = {
      ...config.headers,
      ...headers
    };
    return config;
  };
};

/**
 * 响应时间拦截器
 */
export const createResponseTimeInterceptor = (): {
  requestInterceptor: RequestInterceptor;
  responseInterceptor: ResponseInterceptor;
} => {
  const startTimes = new Map<string, number>();

  const requestInterceptor: RequestInterceptor = (config) => {
    const requestId = config.headers?.['X-Request-ID'] || `req_${Date.now()}`;
    startTimes.set(requestId, Date.now());
    
    if (!config.headers?.['X-Request-ID']) {
      config.headers = {
        ...config.headers,
        'X-Request-ID': requestId
      };
    }
    
    return config;
  };

  const responseInterceptor: ResponseInterceptor = (response) => {
    const requestId = response.config.headers?.['X-Request-ID'];
    if (requestId && startTimes.has(requestId)) {
      const startTime = startTimes.get(requestId)!;
      const duration = Date.now() - startTime;
      
      console.log(`[HTTP] ${response.config.headers?.['X-Method'] || 'GET'} ${response.config.headers?.['X-URL'] || 'unknown'} - ${duration}ms`);
      
      startTimes.delete(requestId);
    }
    
    return response;
  };

  return { requestInterceptor, responseInterceptor };
};

/**
 * 错误重试拦截器
 */
export const createRetryInterceptor = (
  maxRetries: number = 3,
  retryDelay: number = 1000,
  retryCondition?: (error: any) => boolean
): RequestInterceptor => {
  return (config) => {
    // 在配置中添加重试信息
    config.headers = {
      ...config.headers,
      'X-Max-Retries': maxRetries.toString(),
      'X-Retry-Delay': retryDelay.toString()
    };
    
    return config;
  };
};

/**
 * 请求限流拦截器
 */
export const createRateLimitInterceptor = (
  maxRequestsPerSecond: number
): RequestInterceptor => {
  let requestCount = 0;
  let lastResetTime = Date.now();

  return async (config) => {
    const now = Date.now();
    
    // 每秒重置计数器
    if (now - lastResetTime >= 1000) {
      requestCount = 0;
      lastResetTime = now;
    }
    
    // 检查是否超过限制
    if (requestCount >= maxRequestsPerSecond) {
      const waitTime = 1000 - (now - lastResetTime);
      await new Promise(resolve => setTimeout(resolve, waitTime));
      
      // 重置计数器
      requestCount = 0;
      lastResetTime = Date.now();
    }
    
    requestCount++;
    return config;
  };
};

/**
 * 请求去重拦截器
 */
export const createDeduplicationInterceptor = (): RequestInterceptor => {
  const pendingRequests = new Map<string, Promise<any>>();

  return async (config) => {
    // 生成请求唯一标识
    const requestKey = `${config.method}_${config.url}_${JSON.stringify(config.data || {})}`;
    
    // 如果相同请求正在进行中，等待其完成
    if (pendingRequests.has(requestKey)) {
      console.log(`[HTTP] 检测到重复请求，等待之前的请求完成: ${requestKey}`);
      await pendingRequests.get(requestKey);
    }
    
    // 标记请求开始
    const requestPromise = new Promise<void>((resolve) => {
      // 这里需要在实际请求完成后调用 resolve
      // 由于拦截器的限制，我们使用定时器来模拟
      setTimeout(() => {
        pendingRequests.delete(requestKey);
        resolve();
      }, 100);
    });
    
    pendingRequests.set(requestKey, requestPromise);
    
    return config;
  };
};

/**
 * 请求压缩拦截器
 */
export const createCompressionInterceptor = (): RequestInterceptor => {
  return (config) => {
    // 添加压缩相关头部
    config.headers = {
      ...config.headers,
      'Accept-Encoding': 'gzip, deflate, br'
    };
    
    // 如果请求体较大，可以考虑压缩
    if (config.data && typeof config.data === 'string' && config.data.length > 1024) {
      config.headers = {
        ...config.headers,
        'Content-Encoding': 'gzip'
      };
    }
    
    return config;
  };
};

/**
 * 安全头部拦截器
 */
export const createSecurityHeadersInterceptor = (): RequestInterceptor => {
  return (config) => {
    config.headers = {
      ...config.headers,
      'X-Content-Type-Options': 'nosniff',
      'X-Frame-Options': 'DENY',
      'X-XSS-Protection': '1; mode=block'
    };
    return config;
  };
};

/**
 * 拦截器工厂
 */
export class InterceptorFactory {
  /**
   * 创建标准拦截器集合
   */
  static createStandardInterceptors(options: {
    enableLogging?: boolean;
    userAgent?: string;
    apiVersion?: string;
    language?: string;
    enableSecurity?: boolean;
  } = {}): {
    requestInterceptors: RequestInterceptor[];
    responseInterceptors: ResponseInterceptor[];
  } {
    const requestInterceptors: RequestInterceptor[] = [];
    const responseInterceptors: ResponseInterceptor[] = [];

    // 添加请求ID
    requestInterceptors.push(createRequestIdInterceptor());

    // 添加时间戳
    requestInterceptors.push(createTimestampInterceptor());

    // 添加日志
    if (options.enableLogging) {
      requestInterceptors.push(createLoggingInterceptor(true));
    }

    // 添加用户代理
    if (options.userAgent) {
      requestInterceptors.push(createUserAgentInterceptor(options.userAgent));
    }

    // 添加API版本
    if (options.apiVersion) {
      requestInterceptors.push(createApiVersionInterceptor(options.apiVersion));
    }

    // 添加语言
    if (options.language) {
      requestInterceptors.push(createLanguageInterceptor(options.language));
    }

    // 添加安全头部
    if (options.enableSecurity) {
      requestInterceptors.push(createSecurityHeadersInterceptor());
    }

    // 添加响应时间监控
    const { requestInterceptor, responseInterceptor } = createResponseTimeInterceptor();
    requestInterceptors.push(requestInterceptor);
    responseInterceptors.push(responseInterceptor);

    return { requestInterceptors, responseInterceptors };
  }

  /**
   * 创建性能优化拦截器集合
   */
  static createPerformanceInterceptors(options: {
    maxRequestsPerSecond?: number;
    enableDeduplication?: boolean;
    enableCompression?: boolean;
  } = {}): RequestInterceptor[] {
    const interceptors: RequestInterceptor[] = [];

    // 添加限流
    if (options.maxRequestsPerSecond) {
      interceptors.push(createRateLimitInterceptor(options.maxRequestsPerSecond));
    }

    // 添加去重
    if (options.enableDeduplication) {
      interceptors.push(createDeduplicationInterceptor());
    }

    // 添加压缩
    if (options.enableCompression) {
      interceptors.push(createCompressionInterceptor());
    }

    return interceptors;
  }
}
