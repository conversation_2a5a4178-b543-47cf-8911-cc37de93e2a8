import {
  IHttpClient,
  RequestOptions,
  HttpResponse,
  RequestInterceptor,
  ResponseInterceptor,
  HttpError,
  RequestConfig
} from '../interfaces/IHttpClient';

/**
 * 基于fetch的HTTP客户端实现
 */
export class HttpClient implements IHttpClient {
  private defaultConfig: RequestOptions = {
    timeout: 30000,
    retryCount: 3,
    retryDelay: 1000,
    responseType: 'json'
  };
  
  private requestInterceptors: Map<string, RequestInterceptor> = new Map();
  private responseInterceptors: Map<string, ResponseInterceptor> = new Map();
  private interceptorIdCounter = 0;

  /**
   * GET请求
   */
  async get<T>(url: string, options?: RequestOptions): Promise<HttpResponse<T>> {
    return this.request<T>('GET', url, undefined, options);
  }

  /**
   * POST请求
   */
  async post<T>(url: string, data?: any, options?: RequestOptions): Promise<HttpResponse<T>> {
    return this.request<T>('POST', url, data, options);
  }

  /**
   * PUT请求
   */
  async put<T>(url: string, data?: any, options?: RequestOptions): Promise<HttpResponse<T>> {
    return this.request<T>('PUT', url, data, options);
  }

  /**
   * DELETE请求
   */
  async delete<T>(url: string, options?: RequestOptions): Promise<HttpResponse<T>> {
    return this.request<T>('DELETE', url, undefined, options);
  }

  /**
   * PATCH请求
   */
  async patch<T>(url: string, data?: any, options?: RequestOptions): Promise<HttpResponse<T>> {
    return this.request<T>('PATCH', url, data, options);
  }

  /**
   * HEAD请求
   */
  async head<T>(url: string, options?: RequestOptions): Promise<HttpResponse<T>> {
    return this.request<T>('HEAD', url, undefined, options);
  }

  /**
   * 获取流
   */
  async getStream(url: string, options?: RequestOptions): Promise<ReadableStream> {
    const config = this.mergeConfig(options);
    const response = await this.fetchWithRetry(url, {
      method: 'GET',
      headers: config.headers,
      signal: config.signal
    }, config);

    if (!response.ok) {
      throw new HttpError(
        `HTTP ${response.status}: ${response.statusText}`,
        response.status,
        response,
        config
      );
    }

    if (!response.body) {
      throw new Error('响应体为空');
    }

    return response.body;
  }

  /**
   * 上传流
   */
  async postStream(url: string, stream: ReadableStream, options?: RequestOptions): Promise<HttpResponse<void>> {
    const config = this.mergeConfig(options);
    
    const response = await this.fetchWithRetry(url, {
      method: 'POST',
      headers: config.headers,
      body: stream,
      signal: config.signal
    }, config);

    return this.processResponse<void>(response, config);
  }

  /**
   * 添加请求拦截器
   */
  addRequestInterceptor(interceptor: RequestInterceptor): string {
    const id = `req_${++this.interceptorIdCounter}`;
    this.requestInterceptors.set(id, interceptor);
    return id;
  }

  /**
   * 添加响应拦截器
   */
  addResponseInterceptor(interceptor: ResponseInterceptor): string {
    const id = `res_${++this.interceptorIdCounter}`;
    this.responseInterceptors.set(id, interceptor);
    return id;
  }

  /**
   * 移除请求拦截器
   */
  removeRequestInterceptor(id: string): void {
    this.requestInterceptors.delete(id);
  }

  /**
   * 移除响应拦截器
   */
  removeResponseInterceptor(id: string): void {
    this.responseInterceptors.delete(id);
  }

  /**
   * 设置默认配置
   */
  setDefaultConfig(config: Partial<RequestOptions>): void {
    this.defaultConfig = { ...this.defaultConfig, ...config };
  }

  /**
   * 获取默认配置
   */
  getDefaultConfig(): RequestOptions {
    return { ...this.defaultConfig };
  }

  /**
   * 核心请求方法
   */
  private async request<T>(
    method: string, 
    url: string, 
    data?: any, 
    options?: RequestOptions
  ): Promise<HttpResponse<T>> {
    let config = this.mergeConfig(options);
    
    // 应用请求拦截器
    let requestConfig: RequestConfig = { ...config, url, method, data };
    const requestInterceptors = Array.from(this.requestInterceptors.values());
    for (const interceptor of requestInterceptors) {
      requestConfig = await interceptor(requestConfig);
    }

    // 准备请求体
    let body: any = undefined;
    if (requestConfig.data !== undefined) {
      if (typeof requestConfig.data === 'string' || 
          requestConfig.data instanceof FormData ||
          requestConfig.data instanceof ArrayBuffer ||
          requestConfig.data instanceof ReadableStream) {
        body = requestConfig.data;
      } else {
        body = JSON.stringify(requestConfig.data);
        requestConfig.headers = {
          'Content-Type': 'application/json',
          ...requestConfig.headers
        };
      }
    }

    // 发送请求
    const response = await this.fetchWithRetry(requestConfig.url, {
      method: requestConfig.method,
      headers: requestConfig.headers,
      body,
      signal: requestConfig.signal
    }, requestConfig);

    // 处理响应
    let httpResponse = await this.processResponse<T>(response, requestConfig);

    // 应用响应拦截器
    const responseInterceptors = Array.from(this.responseInterceptors.values());
    for (const interceptor of responseInterceptors) {
      httpResponse = await interceptor(httpResponse);
    }

    return httpResponse;
  }

  /**
   * 带重试的fetch
   */
  private async fetchWithRetry(
    url: string, 
    init: RequestInit, 
    config: RequestOptions
  ): Promise<Response> {
    let lastError: Error;
    
    for (let attempt = 0; attempt <= (config.retryCount || 0); attempt++) {
      try {
        // 设置超时
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), config.timeout);
        
        const response = await fetch(url, {
          ...init,
          signal: config.signal || controller.signal
        });
        
        clearTimeout(timeoutId);
        
        // 如果是第一次尝试或者响应成功，直接返回
        if (attempt === 0 || response.ok) {
          return response;
        }
        
        // 如果是客户端错误（4xx），不重试
        if (response.status >= 400 && response.status < 500) {
          return response;
        }
        
        lastError = new Error(`HTTP ${response.status}: ${response.statusText}`);
      } catch (error) {
        lastError = error as Error;
        
        // 如果是最后一次尝试，抛出错误
        if (attempt === (config.retryCount || 0)) {
          throw lastError;
        }
        
        // 等待后重试
        if (config.retryDelay && config.retryDelay > 0) {
          await this.delay(config.retryDelay * Math.pow(2, attempt));
        }
      }
    }
    
    throw lastError!;
  }

  /**
   * 处理响应
   */
  private async processResponse<T>(response: Response, config: RequestOptions): Promise<HttpResponse<T>> {
    if (!response.ok) {
      throw new HttpError(
        `HTTP ${response.status}: ${response.statusText}`,
        response.status,
        response,
        config
      );
    }

    // 解析响应头
    const headers: Record<string, string> = {};
    response.headers.forEach((value, key) => {
      headers[key] = value;
    });

    // 解析响应体
    let data: any;
    const responseType = config.responseType || 'json';
    
    try {
      switch (responseType) {
        case 'json':
          data = await response.json();
          break;
        case 'text':
          data = await response.text();
          break;
        case 'blob':
          data = await response.blob();
          break;
        case 'arraybuffer':
          data = await response.arrayBuffer();
          break;
        default:
          data = await response.text();
      }
    } catch (error) {
      throw new Error(`解析响应失败: ${(error as Error).message}`);
    }

    return {
      data,
      status: response.status,
      statusText: response.statusText,
      headers,
      config
    };
  }

  /**
   * 合并配置
   */
  private mergeConfig(options?: RequestOptions): RequestOptions {
    return {
      ...this.defaultConfig,
      ...options,
      headers: {
        ...this.defaultConfig.headers,
        ...options?.headers
      }
    };
  }

  /**
   * 延迟函数
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
