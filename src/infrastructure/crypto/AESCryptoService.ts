import { 
  ICryptoService, 
  EncryptResult, 
  DecryptResult, 
  HashAlgorithm,
  EncryptionAlgorithm,
  KeyDerivationAlgorithm,
  CryptoUtils
} from '../interfaces/ICryptoService';

/**
 * 基于Web Crypto API的AES加密服务
 */
export class AESCryptoService implements ICryptoService {
  private defaultAlgorithm: EncryptionAlgorithm = EncryptionAlgorithm.AES_GCM;
  private defaultKeyLength = 256;
  private defaultIterations = 100000;

  /**
   * 加密数据
   */
  async encrypt(data: string | ArrayBuffer, key?: string): Promise<EncryptResult> {
    try {
      // 转换数据为ArrayBuffer
      const dataBuffer = this.toArrayBuffer(data);
      
      // 生成或使用提供的密钥
      const cryptoKey = key ? await this.importKey(key) : await this.generateCryptoKey();
      
      // 生成随机IV
      const iv = CryptoUtils.generateIV(12); // GCM模式使用12字节IV
      
      // 加密数据
      const encryptedBuffer = await crypto.subtle.encrypt(
        {
          name: this.defaultAlgorithm,
          iv: iv
        },
        cryptoKey,
        dataBuffer
      );
      
      // 导出密钥（如果是新生成的）
      const exportedKey = key || await this.exportKey(cryptoKey);
      
      return {
        encryptedData: this.arrayBufferToBase64(encryptedBuffer),
        iv: this.arrayBufferToBase64(iv),
        algorithm: this.defaultAlgorithm
      };
    } catch (error) {
      throw new Error(`加密失败: ${(error as Error).message}`);
    }
  }

  /**
   * 解密数据
   */
  async decrypt(encryptedData: string | ArrayBuffer, key?: string): Promise<DecryptResult> {
    try {
      if (!key) {
        throw new Error('解密需要提供密钥');
      }

      // 解析加密数据
      let encryptedBuffer: ArrayBuffer;
      let iv: ArrayBuffer;
      
      if (typeof encryptedData === 'string') {
        // 尝试解析格式化的加密数据
        if (encryptedData.includes(':')) {
          const parsed = CryptoUtils.parseEncryptedData(encryptedData);
          iv = this.base64ToArrayBuffer(parsed.iv);
          encryptedBuffer = this.base64ToArrayBuffer(parsed.encryptedData);
        } else {
          throw new Error('加密数据格式错误，缺少IV信息');
        }
      } else {
        throw new Error('不支持的加密数据格式');
      }
      
      // 导入密钥
      const cryptoKey = await this.importKey(key);
      
      // 解密数据
      const decryptedBuffer = await crypto.subtle.decrypt(
        {
          name: this.defaultAlgorithm,
          iv: iv
        },
        cryptoKey,
        encryptedBuffer
      );
      
      return {
        decryptedData: decryptedBuffer,
        algorithm: this.defaultAlgorithm
      };
    } catch (error) {
      throw new Error(`解密失败: ${(error as Error).message}`);
    }
  }

  /**
   * 生成密钥
   */
  async generateKey(): Promise<string> {
    const cryptoKey = await this.generateCryptoKey();
    return await this.exportKey(cryptoKey);
  }

  /**
   * 派生密钥
   */
  async deriveKey(password: string, salt: string): Promise<string> {
    try {
      // 导入密码作为密钥材料
      const passwordKey = await crypto.subtle.importKey(
        'raw',
        new TextEncoder().encode(password),
        'PBKDF2',
        false,
        ['deriveBits', 'deriveKey']
      );
      
      // 派生密钥
      const derivedKey = await crypto.subtle.deriveKey(
        {
          name: KeyDerivationAlgorithm.PBKDF2,
          salt: new TextEncoder().encode(salt),
          iterations: this.defaultIterations,
          hash: HashAlgorithm.SHA256
        },
        passwordKey,
        {
          name: this.defaultAlgorithm,
          length: this.defaultKeyLength
        },
        true,
        ['encrypt', 'decrypt']
      );
      
      return await this.exportKey(derivedKey);
    } catch (error) {
      throw new Error(`密钥派生失败: ${(error as Error).message}`);
    }
  }

  /**
   * 计算哈希
   */
  async hash(data: string | ArrayBuffer, algorithm: HashAlgorithm = HashAlgorithm.SHA256): Promise<string> {
    try {
      const dataBuffer = this.toArrayBuffer(data);
      const hashBuffer = await crypto.subtle.digest(algorithm, dataBuffer);
      return this.arrayBufferToHex(hashBuffer);
    } catch (error) {
      throw new Error(`哈希计算失败: ${(error as Error).message}`);
    }
  }

  /**
   * 创建加密流
   */
  createEncryptStream(key: string): TransformStream {
    return new TransformStream({
      transform: async (chunk, controller) => {
        try {
          const result = await this.encrypt(chunk, key);
          // 使用格式化的加密数据
          const formattedData = CryptoUtils.formatEncryptedData(result);
          controller.enqueue(formattedData);
        } catch (error) {
          controller.error(error);
        }
      }
    });
  }

  /**
   * 创建解密流
   */
  createDecryptStream(key: string): TransformStream {
    return new TransformStream({
      transform: async (chunk, controller) => {
        try {
          const result = await this.decrypt(chunk, key);
          controller.enqueue(result.decryptedData);
        } catch (error) {
          controller.error(error);
        }
      }
    });
  }

  /**
   * 数字签名（简化实现）
   */
  async sign(data: string | ArrayBuffer, privateKey: string): Promise<string> {
    // 简化实现，使用HMAC-SHA256
    const dataBuffer = this.toArrayBuffer(data);
    const keyBuffer = this.toArrayBuffer(privateKey);
    
    // 导入密钥用于HMAC
    const hmacKey = await crypto.subtle.importKey(
      'raw',
      keyBuffer,
      {
        name: 'HMAC',
        hash: HashAlgorithm.SHA256
      },
      false,
      ['sign']
    );
    
    // 生成签名
    const signature = await crypto.subtle.sign('HMAC', hmacKey, dataBuffer);
    return this.arrayBufferToBase64(signature);
  }

  /**
   * 验证签名（简化实现）
   */
  async verify(data: string | ArrayBuffer, signature: string, publicKey: string): Promise<boolean> {
    try {
      // 简化实现，使用HMAC-SHA256验证
      const dataBuffer = this.toArrayBuffer(data);
      const keyBuffer = this.toArrayBuffer(publicKey);
      const signatureBuffer = this.base64ToArrayBuffer(signature);
      
      // 导入密钥用于HMAC
      const hmacKey = await crypto.subtle.importKey(
        'raw',
        keyBuffer,
        {
          name: 'HMAC',
          hash: HashAlgorithm.SHA256
        },
        false,
        ['verify']
      );
      
      // 验证签名
      return await crypto.subtle.verify('HMAC', hmacKey, signatureBuffer, dataBuffer);
    } catch (error) {
      console.error('签名验证失败:', error);
      return false;
    }
  }

  /**
   * 生成CryptoKey
   */
  private async generateCryptoKey(): Promise<CryptoKey> {
    return await crypto.subtle.generateKey(
      {
        name: this.defaultAlgorithm,
        length: this.defaultKeyLength
      },
      true,
      ['encrypt', 'decrypt']
    );
  }

  /**
   * 导出密钥
   */
  private async exportKey(cryptoKey: CryptoKey): Promise<string> {
    const exported = await crypto.subtle.exportKey('raw', cryptoKey);
    return this.arrayBufferToBase64(exported);
  }

  /**
   * 导入密钥
   */
  private async importKey(key: string): Promise<CryptoKey> {
    const keyBuffer = this.base64ToArrayBuffer(key);
    return await crypto.subtle.importKey(
      'raw',
      keyBuffer,
      {
        name: this.defaultAlgorithm,
        length: this.defaultKeyLength
      },
      false,
      ['encrypt', 'decrypt']
    );
  }

  /**
   * 转换为ArrayBuffer
   */
  private toArrayBuffer(data: string | ArrayBuffer): ArrayBuffer {
    if (typeof data === 'string') {
      return new TextEncoder().encode(data).buffer;
    }
    return data;
  }

  /**
   * ArrayBuffer转Base64
   */
  private arrayBufferToBase64(buffer: ArrayBuffer): string {
    const bytes = new Uint8Array(buffer);
    let binary = '';
    for (let i = 0; i < bytes.byteLength; i++) {
      binary += String.fromCharCode(bytes[i]);
    }
    return btoa(binary);
  }

  /**
   * Base64转ArrayBuffer
   */
  private base64ToArrayBuffer(base64: string): ArrayBuffer {
    const binary = atob(base64);
    const bytes = new Uint8Array(binary.length);
    for (let i = 0; i < binary.length; i++) {
      bytes[i] = binary.charCodeAt(i);
    }
    return bytes.buffer;
  }

  /**
   * ArrayBuffer转十六进制字符串
   */
  private arrayBufferToHex(buffer: ArrayBuffer): string {
    const bytes = new Uint8Array(buffer);
    return Array.from(bytes)
      .map(byte => byte.toString(16).padStart(2, '0'))
      .join('');
  }
}
