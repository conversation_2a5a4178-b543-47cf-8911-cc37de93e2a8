import { ICryptoService } from '../interfaces/ICryptoService';
import { AESCryptoService } from './AESCryptoService';

/**
 * 加密服务类型
 */
export enum CryptoServiceType {
  AES = 'aes',
  RSA = 'rsa'
}

/**
 * 加密服务工厂
 */
export class CryptoServiceFactory {
  private static instances: Map<CryptoServiceType, ICryptoService> = new Map();

  /**
   * 创建加密服务实例
   */
  static create(type: CryptoServiceType = CryptoServiceType.AES): ICryptoService {
    if (!this.instances.has(type)) {
      switch (type) {
        case CryptoServiceType.AES:
          this.instances.set(type, new AESCryptoService());
          break;
        case CryptoServiceType.RSA:
          // TODO: 实现RSA加密服务
          throw new Error('RSA加密服务尚未实现');
        default:
          throw new Error(`不支持的加密服务类型: ${type}`);
      }
    }

    return this.instances.get(type)!;
  }

  /**
   * 获取默认加密服务
   */
  static getDefault(): ICryptoService {
    return this.create(CryptoServiceType.AES);
  }

  /**
   * 清除所有实例
   */
  static clear(): void {
    this.instances.clear();
  }

  /**
   * 检查加密服务是否可用
   */
  static isAvailable(type: CryptoServiceType): boolean {
    try {
      // 检查Web Crypto API是否可用
      if (!crypto || !crypto.subtle) {
        return false;
      }

      switch (type) {
        case CryptoServiceType.AES:
          return true;
        case CryptoServiceType.RSA:
          return false; // 尚未实现
        default:
          return false;
      }
    } catch (error) {
      return false;
    }
  }

  /**
   * 获取支持的加密服务类型列表
   */
  static getSupportedTypes(): CryptoServiceType[] {
    const supportedTypes: CryptoServiceType[] = [];
    
    for (const type of Object.values(CryptoServiceType)) {
      if (this.isAvailable(type)) {
        supportedTypes.push(type);
      }
    }
    
    return supportedTypes;
  }

  /**
   * 创建带配置的加密服务
   */
  static createWithConfig(type: CryptoServiceType, config: CryptoServiceConfig): ICryptoService {
    const service = this.create(type);
    
    // 如果服务支持配置，应用配置
    if ('configure' in service && typeof service.configure === 'function') {
      (service as any).configure(config);
    }
    
    return service;
  }

  /**
   * 验证加密服务功能
   */
  static async validateService(type: CryptoServiceType): Promise<ValidationResult> {
    const result: ValidationResult = {
      isValid: false,
      errors: [],
      warnings: []
    };

    try {
      if (!this.isAvailable(type)) {
        result.errors.push(`加密服务类型 ${type} 不可用`);
        return result;
      }

      const service = this.create(type);
      
      // 测试基本功能
      try {
        const testData = 'test-data-for-validation';
        const key = await service.generateKey();
        const encrypted = await service.encrypt(testData, key);
        const decrypted = await service.decrypt(
          `${encrypted.algorithm}:${encrypted.iv}:${encrypted.encryptedData}`,
          key
        );
        
        const decryptedText = new TextDecoder().decode(decrypted.decryptedData as ArrayBuffer);
        
        if (decryptedText !== testData) {
          result.errors.push('加密解密测试失败：数据不匹配');
        } else {
          result.isValid = true;
        }
      } catch (error) {
        result.errors.push(`加密解密测试失败：${(error as Error).message}`);
      }

      // 测试哈希功能
      try {
        const hash = await service.hash('test-hash-data');
        if (!hash || hash.length === 0) {
          result.warnings.push('哈希功能可能存在问题');
        }
      } catch (error) {
        result.warnings.push(`哈希功能测试失败：${(error as Error).message}`);
      }

      // 测试签名功能
      try {
        const testData = 'test-signature-data';
        const key = await service.generateKey();
        const signature = await service.sign(testData, key);
        const isValid = await service.verify(testData, signature, key);
        
        if (!isValid) {
          result.warnings.push('数字签名功能可能存在问题');
        }
      } catch (error) {
        result.warnings.push(`数字签名功能测试失败：${(error as Error).message}`);
      }

    } catch (error) {
      result.errors.push(`服务验证失败：${(error as Error).message}`);
    }

    return result;
  }

  /**
   * 获取服务信息
   */
  static getServiceInfo(type: CryptoServiceType): ServiceInfo {
    const info: ServiceInfo = {
      type,
      name: '',
      description: '',
      algorithms: [],
      features: [],
      isAvailable: this.isAvailable(type)
    };

    switch (type) {
      case CryptoServiceType.AES:
        info.name = 'AES加密服务';
        info.description = '基于Web Crypto API的AES对称加密服务';
        info.algorithms = ['AES-GCM', 'AES-CBC', 'AES-CTR'];
        info.features = ['对称加密', '流式加密', 'HMAC签名', '密钥派生'];
        break;
      case CryptoServiceType.RSA:
        info.name = 'RSA加密服务';
        info.description = 'RSA非对称加密服务（尚未实现）';
        info.algorithms = ['RSA-OAEP', 'RSASSA-PKCS1-v1_5'];
        info.features = ['非对称加密', '数字签名', '密钥交换'];
        break;
    }

    return info;
  }
}

/**
 * 加密服务配置
 */
export interface CryptoServiceConfig {
  keyLength?: number;
  iterations?: number;
  algorithm?: string;
  [key: string]: any;
}

/**
 * 验证结果
 */
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

/**
 * 服务信息
 */
export interface ServiceInfo {
  type: CryptoServiceType;
  name: string;
  description: string;
  algorithms: string[];
  features: string[];
  isAvailable: boolean;
}

/**
 * 加密服务管理器
 */
export class CryptoServiceManager {
  private static defaultType: CryptoServiceType = CryptoServiceType.AES;
  private static globalConfig: CryptoServiceConfig = {};

  /**
   * 设置默认加密服务类型
   */
  static setDefaultType(type: CryptoServiceType): void {
    if (!CryptoServiceFactory.isAvailable(type)) {
      throw new Error(`加密服务类型 ${type} 不可用`);
    }
    this.defaultType = type;
  }

  /**
   * 获取默认加密服务类型
   */
  static getDefaultType(): CryptoServiceType {
    return this.defaultType;
  }

  /**
   * 设置全局配置
   */
  static setGlobalConfig(config: CryptoServiceConfig): void {
    this.globalConfig = { ...this.globalConfig, ...config };
  }

  /**
   * 获取全局配置
   */
  static getGlobalConfig(): CryptoServiceConfig {
    return { ...this.globalConfig };
  }

  /**
   * 创建默认加密服务
   */
  static createDefault(): ICryptoService {
    return CryptoServiceFactory.createWithConfig(this.defaultType, this.globalConfig);
  }

  /**
   * 初始化加密服务
   */
  static async initialize(): Promise<void> {
    // 检查Web Crypto API支持
    if (!crypto || !crypto.subtle) {
      throw new Error('当前环境不支持Web Crypto API');
    }

    // 验证默认服务
    const validation = await CryptoServiceFactory.validateService(this.defaultType);
    if (!validation.isValid) {
      throw new Error(`默认加密服务验证失败：${validation.errors.join(', ')}`);
    }

    console.log('加密服务初始化成功');
  }

  /**
   * 获取系统状态
   */
  static getSystemStatus(): {
    isSupported: boolean;
    availableServices: CryptoServiceType[];
    defaultService: CryptoServiceType;
    globalConfig: CryptoServiceConfig;
  } {
    return {
      isSupported: !!(crypto && crypto.subtle),
      availableServices: CryptoServiceFactory.getSupportedTypes(),
      defaultService: this.defaultType,
      globalConfig: this.getGlobalConfig()
    };
  }
}
