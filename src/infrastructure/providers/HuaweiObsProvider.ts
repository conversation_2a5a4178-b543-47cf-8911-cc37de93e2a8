import ObsClient from 'esdk-obs-browserjs';
import '../types/obs-types';
import { BaseStorageProvider } from '../interfaces/IStorageProvider';
import { StorageType } from '../enums/StorageType';
import { IStorageConfig, ICloudStorageConfig } from '../types/StorageConfig';
import { 
  StorageResult, 
  StorageResultFactory,
  ObjectMetadata, 
  GetOptions, 
  PutOptions, 
  ListOptions, 
  SignedUrlOptions,
  BatchOptions,
  StreamOptions,
  StorageStats
} from '../types/StorageResult';

/**
 * 华为云OBS存储提供者
 * 实现IStorageProvider接口，提供华为云对象存储服务
 */
export class HuaweiObsProvider extends BaseStorageProvider {
  private obsClient: ObsClient | null = null;
  private bucketName: string = '';
  
  constructor() {
    super('华为云OBS', StorageType.HUAWEI_OBS);
  }
  
  /**
   * 初始化华为云OBS客户端
   */
  async initialize(config: IStorageConfig): Promise<void> {
    try {
      const obsConfig = config as ICloudStorageConfig;
      
      // 验证配置参数
      this.validateConfig(obsConfig);
      
      // 创建OBS客户端
      this.obsClient = new ObsClient({
        access_key_id: obsConfig.accessKey,
        secret_access_key: obsConfig.secretKey,
        server: obsConfig.endpoint,
        timeout: obsConfig.timeout || 30000,
        max_retry_count: obsConfig.retryCount || 3
      });
      
      this.bucketName = obsConfig.bucketName;
      this.config = config;
      this.initialized = true;
      
      console.log(`华为云OBS提供者初始化成功: ${this.bucketName}`);
    } catch (error) {
      this.initialized = false;
      throw new Error(`华为云OBS初始化失败: ${(error as Error).message}`);
    }
  }
  
  /**
   * 释放资源
   */
  async dispose(): Promise<void> {
    if (this.obsClient) {
      this.obsClient = null;
    }
    this.initialized = false;
    console.log('华为云OBS提供者已释放');
  }
  
  /**
   * 测试连接
   */
  async testConnection(): Promise<boolean> {
    if (!this.obsClient) {
      return false;
    }
    
    try {
      const result = await this.obsClient.headBucket({
        Bucket: this.bucketName
      });
      return result.CommonMsg.Status === 200;
    } catch (error) {
      console.error('华为云OBS连接测试失败:', error);
      return false;
    }
  }
  
  /**
   * 获取对象
   */
  async get(key: string, options?: GetOptions): Promise<StorageResult<any>> {
    this.ensureInitialized();
    
    if (!this.obsClient) {
      return StorageResultFactory.failure(new Error('OBS客户端未初始化'));
    }
    
    try {
      const params: any = {
        Bucket: this.bucketName,
        Key: key
      };
      
      // 处理范围请求
      if (options?.range) {
        params.Range = `bytes=${options.range.start}-${options.range.end}`;
      }
      
      // 设置响应类型
      if (options?.saveByType) {
        params.SaveByType = options.saveByType;
      }
      
      const result = await this.withTimeout(
        this.withRetry(() => this.obsClient!.getObject(params))
      ) as any;

      if (result.CommonMsg.Status === 200) {
        return StorageResultFactory.success(result.InterfaceResult.Content, {
          contentType: result.InterfaceResult.ContentType,
          lastModified: result.InterfaceResult.LastModified,
          etag: result.InterfaceResult.ETag
        });
      } else {
        return StorageResultFactory.failure(new Error(`获取对象失败: ${result.CommonMsg.Code}`));
      }
    } catch (error) {
      return StorageResultFactory.failure(error as Error);
    }
  }
  
  /**
   * 上传对象
   */
  async put(key: string, data: any, options?: PutOptions): Promise<StorageResult<void>> {
    this.ensureInitialized();
    
    if (!this.obsClient) {
      return StorageResultFactory.failure(new Error('OBS客户端未初始化'));
    }
    
    try {
      const params: any = {
        Bucket: this.bucketName,
        Key: key,
        Body: data
      };
      
      // 设置内容类型
      if (options?.contentType) {
        params.ContentType = options.contentType;
      }
      
      // 设置元数据
      if (options?.metadata) {
        params.Metadata = options.metadata;
      }
      
      const result = await this.withTimeout(
        this.withRetry(() => this.obsClient!.putObject(params))
      ) as any;

      if (result.CommonMsg.Status === 200) {
        return StorageResultFactory.success(undefined, {
          etag: result.InterfaceResult.ETag
        });
      } else {
        return StorageResultFactory.failure(new Error(`上传对象失败: ${result.CommonMsg.Code}`));
      }
    } catch (error) {
      return StorageResultFactory.failure(error as Error);
    }
  }
  
  /**
   * 删除对象
   */
  async delete(key: string): Promise<StorageResult<void>> {
    this.ensureInitialized();
    
    if (!this.obsClient) {
      return StorageResultFactory.failure(new Error('OBS客户端未初始化'));
    }
    
    try {
      const result = await this.withTimeout(
        this.withRetry(() => this.obsClient!.deleteObject({
          Bucket: this.bucketName,
          Key: key
        }))
      ) as any;

      if (result.CommonMsg.Status === 204) {
        return StorageResultFactory.success();
      } else {
        return StorageResultFactory.failure(new Error(`删除对象失败: ${result.CommonMsg.Code}`));
      }
    } catch (error) {
      return StorageResultFactory.failure(error as Error);
    }
  }
  
  /**
   * 列出对象
   */
  async list(prefix?: string, options?: ListOptions): Promise<StorageResult<string[]>> {
    this.ensureInitialized();
    
    if (!this.obsClient) {
      return StorageResultFactory.failure(new Error('OBS客户端未初始化'));
    }
    
    try {
      const params: any = {
        Bucket: this.bucketName,
        MaxKeys: options?.maxKeys || 1000
      };
      
      if (prefix) {
        params.Prefix = prefix;
      }
      
      if (options?.marker) {
        params.Marker = options.marker;
      }
      
      if (options?.delimiter) {
        params.Delimiter = options.delimiter;
      }
      
      const result = await this.withTimeout(
        this.withRetry(() => this.obsClient!.listObjects(params))
      );
      
      if (result.CommonMsg.Status === 200) {
        const keys = result.InterfaceResult.Contents?.map((obj: any) => obj.Key) || [];
        return StorageResultFactory.success(keys);
      } else {
        return StorageResultFactory.failure(new Error(`列出对象失败: ${result.CommonMsg.Code}`));
      }
    } catch (error) {
      return StorageResultFactory.failure(error as Error);
    }
  }
  
  /**
   * 批量获取对象
   */
  async getBatch(keys: string[], options?: BatchOptions): Promise<StorageResult<Record<string, any>>> {
    const results: Record<string, any> = {};
    const errors: string[] = [];
    const concurrency = options?.concurrency || 5;
    
    // 分批处理以控制并发
    for (let i = 0; i < keys.length; i += concurrency) {
      const batch = keys.slice(i, i + concurrency);
      const promises = batch.map(async (key) => {
        try {
          const result = await this.get(key);
          if (result.success) {
            results[key] = result.data;
          } else {
            errors.push(`${key}: ${result.error?.message}`);
            if (!options?.continueOnError) {
              throw new Error(`批量获取在 ${key} 处停止`);
            }
          }
        } catch (error) {
          errors.push(`${key}: ${(error as Error).message}`);
          if (!options?.continueOnError) {
            throw error;
          }
        }
      });
      
      await Promise.all(promises);
    }

    if (errors.length > 0 && !options?.continueOnError) {
      return StorageResultFactory.failure(new Error(`批量获取失败: ${errors.join(', ')}`), { results });
    }

    return StorageResultFactory.success(results, { errors });
  }

  /**
   * 批量上传对象
   */
  async putBatch(items: Record<string, any>, options?: BatchOptions): Promise<StorageResult<void>> {
    const errors: string[] = [];
    const keys = Object.keys(items);
    const concurrency = options?.concurrency || 5;

    // 分批处理以控制并发
    for (let i = 0; i < keys.length; i += concurrency) {
      const batch = keys.slice(i, i + concurrency);
      const promises = batch.map(async (key) => {
        try {
          const result = await this.put(key, items[key]);
          if (!result.success) {
            errors.push(`${key}: ${result.error?.message}`);
            if (!options?.continueOnError) {
              throw new Error(`批量上传在 ${key} 处停止`);
            }
          }
        } catch (error) {
          errors.push(`${key}: ${(error as Error).message}`);
          if (!options?.continueOnError) {
            throw error;
          }
        }
      });
      
      await Promise.all(promises);
    }

    if (errors.length > 0 && !options?.continueOnError) {
      return StorageResultFactory.failure(new Error(`批量上传失败: ${errors.join(', ')}`));
    }

    return StorageResultFactory.success(undefined, { errors });
  }

  /**
   * 批量删除对象
   */
  async deleteBatch(keys: string[], options?: BatchOptions): Promise<StorageResult<void>> {
    this.ensureInitialized();

    if (!this.obsClient) {
      return StorageResultFactory.failure(new Error('OBS客户端未初始化'));
    }

    try {
      const deleteObjects = keys.map(key => ({ Key: key }));

      const result = await this.withTimeout(
        this.withRetry(() => this.obsClient!.deleteObjects({
          Bucket: this.bucketName,
          Delete: {
            Objects: deleteObjects
          }
        }))
      );

      if (result.CommonMsg.Status === 200) {
        return StorageResultFactory.success();
      } else {
        return StorageResultFactory.failure(new Error(`批量删除失败: ${result.CommonMsg.Code}`));
      }
    } catch (error) {
      return StorageResultFactory.failure(error as Error);
    }
  }

  /**
   * 获取对象元数据
   */
  async getMetadata(key: string): Promise<StorageResult<ObjectMetadata>> {
    this.ensureInitialized();

    if (!this.obsClient) {
      return StorageResultFactory.failure(new Error('OBS客户端未初始化'));
    }

    try {
      const result = await this.withTimeout(
        this.withRetry(() => this.obsClient!.headObject({
          Bucket: this.bucketName,
          Key: key
        }))
      );

      if (result.CommonMsg.Status === 200) {
        const metadata: ObjectMetadata = {
          size: parseInt(result.InterfaceResult.ContentLength),
          lastModified: new Date(result.InterfaceResult.LastModified),
          etag: result.InterfaceResult.ETag,
          contentType: result.InterfaceResult.ContentType,
          customMetadata: result.InterfaceResult.Metadata
        };

        return StorageResultFactory.success(metadata);
      } else {
        return StorageResultFactory.failure(new Error(`获取元数据失败: ${result.CommonMsg.Code}`));
      }
    } catch (error) {
      return StorageResultFactory.failure(error as Error);
    }
  }

  /**
   * 获取对象流
   */
  async getStream(key: string, options?: StreamOptions): Promise<ReadableStream> {
    throw new Error('华为云OBS暂不支持流式读取');
  }

  /**
   * 上传对象流
   */
  async putStream(key: string, stream: ReadableStream, options?: PutOptions & StreamOptions): Promise<StorageResult<void>> {
    try {
      const reader = stream.getReader();
      const chunks: Uint8Array[] = [];

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;
        chunks.push(value);
      }

      // 合并所有块
      const totalLength = chunks.reduce((sum, chunk) => sum + chunk.length, 0);
      const result = new Uint8Array(totalLength);
      let offset = 0;

      for (const chunk of chunks) {
        result.set(chunk, offset);
        offset += chunk.length;
      }

      return await this.put(key, result, options);
    } catch (error) {
      return StorageResultFactory.failure(error as Error);
    }
  }

  /**
   * 生成签名URL
   */
  async getSignedUrl(key: string, options?: SignedUrlOptions): Promise<string> {
    this.ensureInitialized();

    if (!this.obsClient) {
      throw new Error('OBS客户端未初始化');
    }

    const params: any = {
      Bucket: this.bucketName,
      Key: key,
      Expires: options?.expires || 3600,
      Method: options?.method || 'GET'
    };

    return this.obsClient.createSignedUrlSync(params);
  }

  /**
   * 获取存储统计信息
   */
  async getStats(): Promise<StorageResult<StorageStats>> {
    this.ensureInitialized();

    try {
      const listResult = await this.list();
      if (!listResult.success) {
        return StorageResultFactory.failure(new Error('无法获取对象列表'));
      }

      const keys = listResult.data || [];
      let totalSize = 0;
      let lastModified = new Date(0);

      // 获取每个对象的元数据来计算总大小
      for (const key of keys.slice(0, 100)) { // 限制为前100个对象以避免过多请求
        const metadataResult = await this.getMetadata(key);
        if (metadataResult.success && metadataResult.data) {
          totalSize += metadataResult.data.size;
          if (metadataResult.data.lastModified > lastModified) {
            lastModified = metadataResult.data.lastModified;
          }
        }
      }

      const stats: StorageStats = {
        totalObjects: keys.length,
        totalSize,
        lastModified,
        provider: this.name
      };

      return StorageResultFactory.success(stats);
    } catch (error) {
      return StorageResultFactory.failure(error as Error);
    }
  }

  /**
   * 更新配置
   */
  async updateConfig(config: Partial<IStorageConfig>): Promise<void> {
    const newConfig = { ...this.config, ...config };
    await this.dispose();
    await this.initialize(newConfig);
  }

  /**
   * 验证配置参数
   */
  private validateConfig(config: ICloudStorageConfig): void {
    if (!config.accessKey) {
      throw new Error('accessKey不能为空');
    }
    if (!config.secretKey) {
      throw new Error('secretKey不能为空');
    }
    if (!config.endpoint) {
      throw new Error('endpoint不能为空');
    }
    if (!config.bucketName) {
      throw new Error('bucketName不能为空');
    }
  }
}
