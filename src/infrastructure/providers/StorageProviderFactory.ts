import { IStorageProvider } from '../interfaces/IStorageProvider';
import { StorageType } from '../enums/StorageType';
import { IStorageConfig } from '../types/StorageConfig';
import { HuaweiObsProvider } from './HuaweiObsProvider';
import { MinioProvider } from './MinioProvider';

/**
 * 存储提供者工厂
 * 负责创建和管理不同类型的存储提供者实例
 */
export class StorageProviderFactory {
  private static instances: Map<string, IStorageProvider> = new Map();

  /**
   * 创建存储提供者实例
   */
  static create(type: StorageType, config?: IStorageConfig): IStorageProvider {
    const key = `${type}_${config ? JSON.stringify(config) : 'default'}`;
    
    if (!this.instances.has(key)) {
      let provider: IStorageProvider;
      
      switch (type) {
        case StorageType.HUAWEI_OBS:
          provider = new HuaweiObsProvider();
          break;
        case StorageType.MINIO:
          provider = new MinioProvider();
          break;
        default:
          throw new Error(`不支持的存储类型: ${type}`);
      }
      
      this.instances.set(key, provider);
    }
    
    return this.instances.get(key)!;
  }

  /**
   * 创建并初始化存储提供者
   */
  static async createAndInitialize(type: StorageType, config: IStorageConfig): Promise<IStorageProvider> {
    const provider = this.create(type, config);
    
    if (!provider.isInitialized) {
      await provider.initialize(config);
    }
    
    return provider;
  }

  /**
   * 获取所有支持的存储类型
   */
  static getSupportedTypes(): StorageType[] {
    return [
      StorageType.HUAWEI_OBS,
      StorageType.MINIO
    ];
  }

  /**
   * 检查存储类型是否支持
   */
  static isSupported(type: StorageType): boolean {
    return this.getSupportedTypes().includes(type);
  }

  /**
   * 清除所有实例
   */
  static async clearAll(): Promise<void> {
    const providers = Array.from(this.instances.values());
    
    // 释放所有提供者资源
    await Promise.all(providers.map(provider => {
      if (provider.isInitialized) {
        return provider.dispose();
      }
      return Promise.resolve();
    }));
    
    this.instances.clear();
  }

  /**
   * 清除指定实例
   */
  static async clear(type: StorageType, config?: IStorageConfig): Promise<void> {
    const key = `${type}_${config ? JSON.stringify(config) : 'default'}`;
    const provider = this.instances.get(key);
    
    if (provider && provider.isInitialized) {
      await provider.dispose();
    }
    
    this.instances.delete(key);
  }

  /**
   * 获取实例数量
   */
  static getInstanceCount(): number {
    return this.instances.size;
  }

  /**
   * 获取所有实例信息
   */
  static getInstancesInfo(): Array<{
    type: StorageType;
    name: string;
    isInitialized: boolean;
  }> {
    return Array.from(this.instances.values()).map(provider => ({
      type: provider.type,
      name: provider.name,
      isInitialized: provider.isInitialized
    }));
  }

  /**
   * 创建测试用的模拟存储提供者
   */
  static createMockProvider(): IStorageProvider {
    return new MockStorageProvider();
  }
}

/**
 * 模拟存储提供者（用于测试）
 */
class MockStorageProvider implements IStorageProvider {
  private mockData: Map<string, any> = new Map();
  private _initialized = false;
  private _config: IStorageConfig = {
    type: StorageType.MEMORY_STORAGE,
    name: 'mock-storage'
  };

  get name(): string {
    return '模拟存储提供者';
  }

  get type(): StorageType {
    return StorageType.MEMORY_STORAGE; // 使用MEMORY_STORAGE作为模拟类型
  }

  get isInitialized(): boolean {
    return this._initialized;
  }

  async initialize(config: IStorageConfig): Promise<void> {
    this._config = config;
    this._initialized = true;
    console.log('模拟存储提供者初始化成功');
  }

  async dispose(): Promise<void> {
    this.mockData.clear();
    this._initialized = false;
    console.log('模拟存储提供者已释放');
  }

  async testConnection(): Promise<boolean> {
    return this._initialized;
  }

  async get(key: string): Promise<any> {
    if (!this._initialized) {
      throw new Error('提供者未初始化');
    }
    
    const data = this.mockData.get(key);
    return {
      success: data !== undefined,
      data: data || null,
      error: data === undefined ? new Error('对象不存在') : undefined
    };
  }

  async put(key: string, data: any): Promise<any> {
    if (!this._initialized) {
      throw new Error('提供者未初始化');
    }
    
    this.mockData.set(key, data);
    return {
      success: true,
      data: undefined,
      metadata: { etag: `mock-etag-${Date.now()}` }
    };
  }

  async delete(key: string): Promise<any> {
    if (!this._initialized) {
      throw new Error('提供者未初始化');
    }
    
    const existed = this.mockData.has(key);
    this.mockData.delete(key);
    return {
      success: true,
      data: undefined
    };
  }

  async list(prefix?: string): Promise<any> {
    if (!this._initialized) {
      throw new Error('提供者未初始化');
    }
    
    const keys = Array.from(this.mockData.keys());
    const filteredKeys = prefix ? keys.filter(key => key.startsWith(prefix)) : keys;
    
    return {
      success: true,
      data: filteredKeys
    };
  }

  async getBatch(keys: string[]): Promise<any> {
    const results: Record<string, any> = {};
    
    for (const key of keys) {
      const result = await this.get(key);
      if (result.success) {
        results[key] = result.data;
      }
    }
    
    return {
      success: true,
      data: results
    };
  }

  async putBatch(items: Record<string, any>): Promise<any> {
    for (const [key, data] of Object.entries(items)) {
      await this.put(key, data);
    }
    
    return {
      success: true,
      data: undefined
    };
  }

  async deleteBatch(keys: string[]): Promise<any> {
    for (const key of keys) {
      await this.delete(key);
    }
    
    return {
      success: true,
      data: undefined
    };
  }

  async getMetadata(key: string): Promise<any> {
    const data = this.mockData.get(key);
    
    if (!data) {
      return {
        success: false,
        error: new Error('对象不存在')
      };
    }
    
    return {
      success: true,
      data: {
        size: JSON.stringify(data).length,
        lastModified: new Date(),
        etag: `mock-etag-${key}`,
        contentType: 'application/json'
      }
    };
  }

  async getStream(): Promise<ReadableStream> {
    throw new Error('模拟提供者不支持流操作');
  }

  async putStream(): Promise<any> {
    throw new Error('模拟提供者不支持流操作');
  }

  async getSignedUrl(key: string): Promise<string> {
    return `https://mock-storage.example.com/${key}?signature=mock-signature`;
  }

  async getStats(): Promise<any> {
    return {
      success: true,
      data: {
        totalObjects: this.mockData.size,
        totalSize: Array.from(this.mockData.values())
          .reduce((sum, data) => sum + JSON.stringify(data).length, 0),
        lastModified: new Date(),
        provider: this.name
      }
    };
  }

  getConfig(): IStorageConfig {
    return { ...this._config };
  }

  async updateConfig(config: Partial<IStorageConfig>): Promise<void> {
    this._config = { ...this._config, ...config };
  }
}
