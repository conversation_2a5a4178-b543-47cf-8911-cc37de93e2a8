import { StorageType } from '../enums/StorageType';
import { IStorageConfig, ICloudStorageConfig } from '../types/StorageConfig';

/**
 * 存储配置管理器
 * 负责管理不同存储提供者的配置
 */
export class StorageConfigManager {
  private static configs: Map<string, IStorageConfig> = new Map();

  /**
   * 保存配置
   */
  static saveConfig(name: string, config: IStorageConfig): void {
    this.configs.set(name, { ...config });
  }

  /**
   * 获取配置
   */
  static getConfig(name: string): IStorageConfig | null {
    const config = this.configs.get(name);
    return config ? { ...config } : null;
  }

  /**
   * 删除配置
   */
  static deleteConfig(name: string): boolean {
    return this.configs.delete(name);
  }

  /**
   * 获取所有配置名称
   */
  static getConfigNames(): string[] {
    return Array.from(this.configs.keys());
  }

  /**
   * 清除所有配置
   */
  static clearAll(): void {
    this.configs.clear();
  }

  /**
   * 验证配置
   */
  static validateConfig(type: StorageType, config: IStorageConfig): {
    valid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    switch (type) {
      case StorageType.HUAWEI_OBS:
      case StorageType.MINIO:
        const cloudConfig = config as ICloudStorageConfig;
        
        if (!cloudConfig.accessKey) {
          errors.push('accessKey 是必需的');
        }
        
        if (!cloudConfig.secretKey) {
          errors.push('secretKey 是必需的');
        }
        
        if (!cloudConfig.endpoint) {
          errors.push('endpoint 是必需的');
        }
        
        if (!cloudConfig.bucketName) {
          errors.push('bucketName 是必需的');
        }
        
        if (cloudConfig.endpoint && !this.isValidUrl(cloudConfig.endpoint)) {
          errors.push('endpoint 必须是有效的URL');
        }
        
        break;
        
      default:
        errors.push(`不支持的存储类型: ${type}`);
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * 创建默认配置
   */
  static createDefaultConfig(type: StorageType): IStorageConfig {
    const baseConfig = {
      timeout: 30000,
      retryCount: 3,
      retryDelay: 1000
    };

    switch (type) {
      case StorageType.HUAWEI_OBS:
        return {
          ...baseConfig,
          type: type,
          name: 'huawei-obs-storage',
          accessKey: '',
          secretKey: '',
          endpoint: 'https://obs.cn-north-4.myhuaweicloud.com',
          bucketName: '',
          region: 'cn-north-4'
        } as ICloudStorageConfig;

      case StorageType.MINIO:
        return {
          ...baseConfig,
          type: type,
          name: 'minio-storage',
          accessKey: '',
          secretKey: '',
          endpoint: 'http://localhost:9000',
          bucketName: '',
          region: 'us-east-1',
          useSSL: false,
          port: 9000
        } as ICloudStorageConfig;

      default:
        return {
          ...baseConfig,
          type: type,
          name: `${type}-storage`
        };
    }
  }

  /**
   * 从环境变量加载配置
   */
  static loadFromEnvironment(type: StorageType): IStorageConfig | null {
    try {
      const baseConfig = this.createDefaultConfig(type);

      switch (type) {
        case StorageType.HUAWEI_OBS:
          return {
            ...baseConfig,
            accessKey: process.env.HUAWEI_OBS_ACCESS_KEY || '',
            secretKey: process.env.HUAWEI_OBS_SECRET_KEY || '',
            endpoint: process.env.HUAWEI_OBS_ENDPOINT || (baseConfig as ICloudStorageConfig).endpoint,
            bucketName: process.env.HUAWEI_OBS_BUCKET || '',
            region: process.env.HUAWEI_OBS_REGION || (baseConfig as ICloudStorageConfig).region
          } as ICloudStorageConfig;

        case StorageType.MINIO:
          return {
            ...baseConfig,
            accessKey: process.env.MINIO_ACCESS_KEY || '',
            secretKey: process.env.MINIO_SECRET_KEY || '',
            endpoint: process.env.MINIO_ENDPOINT || (baseConfig as ICloudStorageConfig).endpoint,
            bucketName: process.env.MINIO_BUCKET || '',
            region: process.env.MINIO_REGION || (baseConfig as ICloudStorageConfig).region,
            useSSL: process.env.MINIO_USE_SSL === 'true',
            port: parseInt(process.env.MINIO_PORT || '9000')
          } as ICloudStorageConfig;

        default:
          return null;
      }
    } catch (error) {
      console.warn('从环境变量加载配置失败:', error);
      return null;
    }
  }

  /**
   * 导出配置到JSON
   */
  static exportConfigs(): string {
    const configsObject: Record<string, IStorageConfig> = {};
    
    const entries = Array.from(this.configs.entries());
    for (const [name, config] of entries) {
      // 移除敏感信息
      const safeConfig = { ...config };
      if ('accessKey' in safeConfig) {
        (safeConfig as any).accessKey = '***';
      }
      if ('secretKey' in safeConfig) {
        (safeConfig as any).secretKey = '***';
      }
      
      configsObject[name] = safeConfig;
    }
    
    return JSON.stringify(configsObject, null, 2);
  }

  /**
   * 从JSON导入配置
   */
  static importConfigs(jsonString: string): {
    success: boolean;
    imported: number;
    errors: string[];
  } {
    const errors: string[] = [];
    let imported = 0;

    try {
      const configsObject = JSON.parse(jsonString);
      
      for (const [name, config] of Object.entries(configsObject)) {
        try {
          this.saveConfig(name, config as IStorageConfig);
          imported++;
        } catch (error) {
          errors.push(`导入配置 "${name}" 失败: ${(error as Error).message}`);
        }
      }
    } catch (error) {
      errors.push(`解析JSON失败: ${(error as Error).message}`);
    }

    return {
      success: errors.length === 0,
      imported,
      errors
    };
  }

  /**
   * 测试配置连接
   */
  static async testConfig(type: StorageType, config: IStorageConfig): Promise<{
    success: boolean;
    message: string;
    duration: number;
  }> {
    const startTime = Date.now();
    
    try {
      // 验证配置
      const validation = this.validateConfig(type, config);
      if (!validation.valid) {
        return {
          success: false,
          message: `配置验证失败: ${validation.errors.join(', ')}`,
          duration: Date.now() - startTime
        };
      }

      // 这里应该创建实际的存储提供者进行测试
      // 为了简化，我们只做基本验证
      return {
        success: true,
        message: '配置验证通过',
        duration: Date.now() - startTime
      };
    } catch (error) {
      return {
        success: false,
        message: `测试失败: ${(error as Error).message}`,
        duration: Date.now() - startTime
      };
    }
  }

  /**
   * 获取配置模板
   */
  static getConfigTemplate(type: StorageType): {
    name: string;
    fields: Array<{
      key: string;
      label: string;
      type: 'text' | 'password' | 'number' | 'boolean' | 'url';
      required: boolean;
      placeholder?: string;
      description?: string;
    }>;
  } {
    switch (type) {
      case StorageType.HUAWEI_OBS:
        return {
          name: '华为云OBS配置',
          fields: [
            {
              key: 'accessKey',
              label: 'Access Key',
              type: 'password',
              required: true,
              placeholder: '输入华为云Access Key',
              description: '华为云账户的Access Key ID'
            },
            {
              key: 'secretKey',
              label: 'Secret Key',
              type: 'password',
              required: true,
              placeholder: '输入华为云Secret Key',
              description: '华为云账户的Secret Access Key'
            },
            {
              key: 'endpoint',
              label: 'Endpoint',
              type: 'url',
              required: true,
              placeholder: 'https://obs.cn-north-4.myhuaweicloud.com',
              description: 'OBS服务的终端节点URL'
            },
            {
              key: 'bucketName',
              label: 'Bucket名称',
              type: 'text',
              required: true,
              placeholder: '输入Bucket名称',
              description: '存储桶的名称'
            },
            {
              key: 'region',
              label: '区域',
              type: 'text',
              required: false,
              placeholder: 'cn-north-4',
              description: '服务区域'
            }
          ]
        };

      case StorageType.MINIO:
        return {
          name: 'MinIO配置',
          fields: [
            {
              key: 'accessKey',
              label: 'Access Key',
              type: 'password',
              required: true,
              placeholder: '输入MinIO Access Key',
              description: 'MinIO的Access Key'
            },
            {
              key: 'secretKey',
              label: 'Secret Key',
              type: 'password',
              required: true,
              placeholder: '输入MinIO Secret Key',
              description: 'MinIO的Secret Key'
            },
            {
              key: 'endpoint',
              label: 'Endpoint',
              type: 'url',
              required: true,
              placeholder: 'http://localhost:9000',
              description: 'MinIO服务器地址'
            },
            {
              key: 'bucketName',
              label: 'Bucket名称',
              type: 'text',
              required: true,
              placeholder: '输入Bucket名称',
              description: '存储桶的名称'
            },
            {
              key: 'port',
              label: '端口',
              type: 'number',
              required: false,
              placeholder: '9000',
              description: 'MinIO服务端口'
            },
            {
              key: 'useSSL',
              label: '使用SSL',
              type: 'boolean',
              required: false,
              description: '是否使用HTTPS连接'
            }
          ]
        };

      default:
        return {
          name: '未知配置',
          fields: []
        };
    }
  }

  /**
   * 验证URL格式
   */
  private static isValidUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }
}
