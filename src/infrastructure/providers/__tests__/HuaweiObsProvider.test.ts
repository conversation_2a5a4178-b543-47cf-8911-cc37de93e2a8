import { HuaweiObsProvider } from '../HuaweiObsProvider';
import { StorageConfigFactory } from '../../types/StorageConfig';
import { StorageType } from '../../enums/StorageType';

// Mock华为云OBS SDK
jest.mock('esdk-obs-browserjs', () => {
  return jest.fn().mockImplementation(() => ({
    headBucket: jest.fn(),
    getObject: jest.fn(),
    putObject: jest.fn(),
    deleteObject: jest.fn(),
    deleteObjects: jest.fn(),
    listObjects: jest.fn(),
    headObject: jest.fn(),
    createSignedUrlSync: jest.fn()
  }));
});

describe('HuaweiObsProvider', () => {
  let provider: HuaweiObsProvider;
  let mockConfig: any;
  let mockObsClient: any;

  beforeEach(() => {
    provider = new HuaweiObsProvider();
    mockConfig = StorageConfigFactory.createHuaweiObsConfig({
      name: 'test-obs',
      endpoint: 'https://obs.cn-north-4.myhuaweicloud.com',
      region: 'cn-north-4',
      accessKey: 'test-access-key',
      secretKey: 'test-secret-key',
      bucketName: 'test-bucket'
    });

    // 获取mock的OBS客户端实例
    const ObsClient = require('esdk-obs-browserjs');
    mockObsClient = new ObsClient();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('基本属性', () => {
    test('应该有正确的名称和类型', () => {
      expect(provider.name).toBe('华为云OBS');
      expect(provider.type).toBe(StorageType.HUAWEI_OBS);
      expect(provider.isInitialized).toBe(false);
    });
  });

  describe('初始化', () => {
    test('应该成功初始化', async () => {
      await provider.initialize(mockConfig);
      expect(provider.isInitialized).toBe(true);
    });

    test('配置参数缺失时应该抛出错误', async () => {
      const invalidConfig = { ...mockConfig, accessKey: '' };
      await expect(provider.initialize(invalidConfig)).rejects.toThrow('accessKey不能为空');
    });

    test('endpoint缺失时应该抛出错误', async () => {
      const invalidConfig = { ...mockConfig, endpoint: '' };
      await expect(provider.initialize(invalidConfig)).rejects.toThrow('endpoint不能为空');
    });

    test('secretKey缺失时应该抛出错误', async () => {
      const invalidConfig = { ...mockConfig, secretKey: '' };
      await expect(provider.initialize(invalidConfig)).rejects.toThrow('secretKey不能为空');
    });

    test('bucketName缺失时应该抛出错误', async () => {
      const invalidConfig = { ...mockConfig, bucketName: '' };
      await expect(provider.initialize(invalidConfig)).rejects.toThrow('bucketName不能为空');
    });
  });

  describe('连接测试', () => {
    test('未初始化时应该返回false', async () => {
      const result = await provider.testConnection();
      expect(result).toBe(false);
    });

    test('初始化后连接成功应该返回true', async () => {
      await provider.initialize(mockConfig);
      
      mockObsClient.headBucket.mockResolvedValue({
        CommonMsg: { Status: 200 }
      });

      const result = await provider.testConnection();
      expect(result).toBe(true);
      expect(mockObsClient.headBucket).toHaveBeenCalledWith({
        Bucket: 'test-bucket'
      });
    });

    test('连接失败时应该返回false', async () => {
      await provider.initialize(mockConfig);
      
      mockObsClient.headBucket.mockRejectedValue(new Error('连接失败'));

      const result = await provider.testConnection();
      expect(result).toBe(false);
    });
  });

  describe('获取对象', () => {
    beforeEach(async () => {
      await provider.initialize(mockConfig);
    });

    test('应该成功获取对象', async () => {
      const mockData = 'test data';
      mockObsClient.getObject.mockResolvedValue({
        CommonMsg: { Status: 200 },
        InterfaceResult: {
          Content: mockData,
          ContentType: 'text/plain',
          LastModified: '2023-01-01T00:00:00Z',
          ETag: 'test-etag'
        }
      });

      const result = await provider.get('test-key');
      
      expect(result.success).toBe(true);
      expect(result.data).toBe(mockData);
      expect(result.metadata).toEqual({
        contentType: 'text/plain',
        lastModified: '2023-01-01T00:00:00Z',
        etag: 'test-etag'
      });
    });

    test('应该处理范围请求', async () => {
      mockObsClient.getObject.mockResolvedValue({
        CommonMsg: { Status: 200 },
        InterfaceResult: { Content: 'partial data' }
      });

      await provider.get('test-key', { range: { start: 0, end: 100 } });
      
      expect(mockObsClient.getObject).toHaveBeenCalledWith({
        Bucket: 'test-bucket',
        Key: 'test-key',
        Range: 'bytes=0-100'
      });
    });

    test('获取失败时应该返回错误', async () => {
      mockObsClient.getObject.mockResolvedValue({
        CommonMsg: { Status: 404, Code: 'NoSuchKey' }
      });

      const result = await provider.get('non-existent-key');
      
      expect(result.success).toBe(false);
      expect(result.error?.message).toContain('获取对象失败');
    });
  });

  describe('上传对象', () => {
    beforeEach(async () => {
      await provider.initialize(mockConfig);
    });

    test('应该成功上传对象', async () => {
      mockObsClient.putObject.mockResolvedValue({
        CommonMsg: { Status: 200 },
        InterfaceResult: { ETag: 'test-etag' }
      });

      const result = await provider.put('test-key', 'test data');
      
      expect(result.success).toBe(true);
      expect(result.metadata?.etag).toBe('test-etag');
      expect(mockObsClient.putObject).toHaveBeenCalledWith({
        Bucket: 'test-bucket',
        Key: 'test-key',
        Body: 'test data'
      });
    });

    test('应该设置内容类型和元数据', async () => {
      mockObsClient.putObject.mockResolvedValue({
        CommonMsg: { Status: 200 },
        InterfaceResult: { ETag: 'test-etag' }
      });

      await provider.put('test-key', 'test data', {
        contentType: 'text/plain',
        metadata: { author: 'test' }
      });
      
      expect(mockObsClient.putObject).toHaveBeenCalledWith({
        Bucket: 'test-bucket',
        Key: 'test-key',
        Body: 'test data',
        ContentType: 'text/plain',
        Metadata: { author: 'test' }
      });
    });
  });

  describe('删除对象', () => {
    beforeEach(async () => {
      await provider.initialize(mockConfig);
    });

    test('应该成功删除对象', async () => {
      mockObsClient.deleteObject.mockResolvedValue({
        CommonMsg: { Status: 204 }
      });

      const result = await provider.delete('test-key');
      
      expect(result.success).toBe(true);
      expect(mockObsClient.deleteObject).toHaveBeenCalledWith({
        Bucket: 'test-bucket',
        Key: 'test-key'
      });
    });
  });

  describe('列出对象', () => {
    beforeEach(async () => {
      await provider.initialize(mockConfig);
    });

    test('应该成功列出对象', async () => {
      const mockObjects = [
        { Key: 'file1.txt' },
        { Key: 'file2.txt' }
      ];

      mockObsClient.listObjects.mockResolvedValue({
        CommonMsg: { Status: 200 },
        InterfaceResult: { Contents: mockObjects }
      });

      const result = await provider.list();
      
      expect(result.success).toBe(true);
      expect(result.data).toEqual(['file1.txt', 'file2.txt']);
    });

    test('应该处理前缀过滤', async () => {
      mockObsClient.listObjects.mockResolvedValue({
        CommonMsg: { Status: 200 },
        InterfaceResult: { Contents: [] }
      });

      await provider.list('prefix/', { maxKeys: 100 });
      
      expect(mockObsClient.listObjects).toHaveBeenCalledWith({
        Bucket: 'test-bucket',
        MaxKeys: 100,
        Prefix: 'prefix/'
      });
    });
  });

  describe('获取元数据', () => {
    beforeEach(async () => {
      await provider.initialize(mockConfig);
    });

    test('应该成功获取对象元数据', async () => {
      mockObsClient.headObject.mockResolvedValue({
        CommonMsg: { Status: 200 },
        InterfaceResult: {
          ContentLength: '1024',
          LastModified: '2023-01-01T00:00:00Z',
          ETag: 'test-etag',
          ContentType: 'text/plain',
          Metadata: { author: 'test' }
        }
      });

      const result = await provider.getMetadata('test-key');
      
      expect(result.success).toBe(true);
      expect(result.data).toEqual({
        size: 1024,
        lastModified: new Date('2023-01-01T00:00:00Z'),
        etag: 'test-etag',
        contentType: 'text/plain',
        customMetadata: { author: 'test' }
      });
    });
  });

  describe('生成签名URL', () => {
    beforeEach(async () => {
      await provider.initialize(mockConfig);
    });

    test('应该生成签名URL', async () => {
      const mockUrl = 'https://test-bucket.obs.cn-north-4.myhuaweicloud.com/test-key?signature=xxx';
      mockObsClient.createSignedUrlSync.mockReturnValue(mockUrl);

      const url = await provider.getSignedUrl('test-key');
      
      expect(url).toBe(mockUrl);
      expect(mockObsClient.createSignedUrlSync).toHaveBeenCalledWith({
        Bucket: 'test-bucket',
        Key: 'test-key',
        Expires: 3600,
        Method: 'GET'
      });
    });

    test('应该支持自定义选项', async () => {
      const mockUrl = 'https://test-bucket.obs.cn-north-4.myhuaweicloud.com/test-key?signature=xxx';
      mockObsClient.createSignedUrlSync.mockReturnValue(mockUrl);

      await provider.getSignedUrl('test-key', { expires: 7200, method: 'PUT' });
      
      expect(mockObsClient.createSignedUrlSync).toHaveBeenCalledWith({
        Bucket: 'test-bucket',
        Key: 'test-key',
        Expires: 7200,
        Method: 'PUT'
      });
    });
  });

  describe('资源释放', () => {
    test('应该正确释放资源', async () => {
      await provider.initialize(mockConfig);
      expect(provider.isInitialized).toBe(true);

      await provider.dispose();
      expect(provider.isInitialized).toBe(false);
    });
  });
});
