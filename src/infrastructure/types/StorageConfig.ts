import { StorageType } from '../enums/StorageType';

/**
 * 存储配置基础接口
 */
export interface IStorageConfig {
  readonly type: StorageType;
  readonly name: string;
  readonly timeout?: number;
  readonly retryCount?: number;
  readonly retryDelay?: number;
}

/**
 * 云存储配置接口
 */
export interface ICloudStorageConfig extends IStorageConfig {
  readonly endpoint: string;
  readonly region?: string;
  readonly accessKey: string;
  readonly secretKey: string;
  readonly bucketName: string;
  readonly useSSL?: boolean;
  readonly port?: number;
}

/**
 * 本地存储配置接口
 */
export interface ILocalStorageConfig extends IStorageConfig {
  readonly storageKey: string;
  readonly maxSize?: number;
  readonly compression?: boolean;
}

/**
 * 内存存储配置接口
 */
export interface IMemoryStorageConfig extends IStorageConfig {
  readonly storageKey: string;
  readonly maxSize?: number;
  readonly ttl?: number; // 生存时间（毫秒）
}

/**
 * 存储配置工厂
 */
export class StorageConfigFactory {
  /**
   * 创建华为云OBS配置
   */
  static createHuaweiObsConfig(config: {
    name: string;
    endpoint: string;
    region: string;
    accessKey: string;
    secretKey: string;
    bucketName: string;
    timeout?: number;
    retryCount?: number;
    retryDelay?: number;
  }): ICloudStorageConfig {
    return {
      type: StorageType.HUAWEI_OBS,
      ...config,
      timeout: config.timeout || 30000,
      retryCount: config.retryCount || 3,
      retryDelay: config.retryDelay || 1000
    };
  }
  
  /**
   * 创建MinIO配置
   */
  static createMinioConfig(config: {
    name: string;
    endpoint: string;
    accessKey: string;
    secretKey: string;
    bucketName: string;
    useSSL?: boolean;
    port?: number;
    timeout?: number;
    retryCount?: number;
    retryDelay?: number;
  }): ICloudStorageConfig {
    return {
      type: StorageType.MINIO,
      ...config,
      timeout: config.timeout || 30000,
      retryCount: config.retryCount || 3,
      retryDelay: config.retryDelay || 1000
    };
  }
  
  /**
   * 创建本地存储配置
   */
  static createLocalStorageConfig(config: {
    name: string;
    storageKey: string;
    maxSize?: number;
    compression?: boolean;
    timeout?: number;
    retryCount?: number;
  }): ILocalStorageConfig {
    return {
      type: StorageType.LOCAL_STORAGE,
      ...config,
      timeout: config.timeout || 5000,
      retryCount: config.retryCount || 1
    };
  }
  
  /**
   * 创建IndexedDB配置
   */
  static createIndexedDBConfig(config: {
    name: string;
    storageKey: string;
    maxSize?: number;
    compression?: boolean;
    timeout?: number;
    retryCount?: number;
  }): ILocalStorageConfig {
    return {
      type: StorageType.INDEXED_DB,
      ...config,
      timeout: config.timeout || 10000,
      retryCount: config.retryCount || 2
    };
  }
  
  /**
   * 创建内存存储配置
   */
  static createMemoryStorageConfig(config: {
    name: string;
    maxSize?: number;
    ttl?: number;
    timeout?: number;
    retryCount?: number;
  }): IMemoryStorageConfig {
    return {
      type: StorageType.MEMORY_STORAGE,
      storageKey: 'memory',
      ...config,
      timeout: config.timeout || 1000,
      retryCount: config.retryCount || 1
    };
  }
  
  /**
   * 验证配置是否有效
   */
  static validateConfig(config: IStorageConfig): boolean {
    if (!config.type || !config.name) {
      return false;
    }
    
    // 根据类型进行特定验证
    if (config.type === StorageType.HUAWEI_OBS || config.type === StorageType.MINIO) {
      const cloudConfig = config as ICloudStorageConfig;
      return !!(cloudConfig.endpoint && cloudConfig.accessKey && 
               cloudConfig.secretKey && cloudConfig.bucketName);
    }
    
    if (config.type === StorageType.LOCAL_STORAGE || config.type === StorageType.INDEXED_DB) {
      const localConfig = config as ILocalStorageConfig;
      return !!localConfig.storageKey;
    }
    
    return true;
  }
}
