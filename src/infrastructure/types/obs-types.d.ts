/**
 * 华为云OBS SDK类型定义
 */
declare module 'esdk-obs-browserjs' {
  interface ObsConfig {
    access_key_id: string;
    secret_access_key: string;
    server: string;
    timeout?: number;
    max_retry_count?: number;
  }

  interface CommonMsg {
    Status: number;
    Code?: string;
    Message?: string;
  }

  interface ObsResponse<T = any> {
    CommonMsg: CommonMsg;
    InterfaceResult: T;
  }

  interface GetObjectResult {
    Content: any;
    ContentType: string;
    LastModified: string;
    ETag: string;
    ContentLength: string;
    Metadata?: Record<string, string>;
  }

  interface PutObjectResult {
    ETag: string;
  }

  interface ListObjectsResult {
    Contents?: Array<{
      Key: string;
      LastModified: string;
      ETag: string;
      Size: number;
    }>;
  }

  interface HeadObjectResult {
    ContentLength: string;
    LastModified: string;
    ETag: string;
    ContentType: string;
    Metadata?: Record<string, string>;
  }

  interface DeleteObjectsRequest {
    Bucket: string;
    Delete: {
      Objects: Array<{ Key: string }>;
    };
  }

  class ObsClient {
    constructor(config: ObsConfig);
    
    headBucket(params: { Bucket: string }): Promise<ObsResponse>;
    
    getObject(params: {
      Bucket: string;
      Key: string;
      Range?: string;
      SaveByType?: string;
    }): Promise<ObsResponse<GetObjectResult>>;
    
    putObject(params: {
      Bucket: string;
      Key: string;
      Body: any;
      ContentType?: string;
      Metadata?: Record<string, string>;
    }): Promise<ObsResponse<PutObjectResult>>;
    
    deleteObject(params: {
      Bucket: string;
      Key: string;
    }): Promise<ObsResponse>;
    
    deleteObjects(params: DeleteObjectsRequest): Promise<ObsResponse>;
    
    listObjects(params: {
      Bucket: string;
      MaxKeys?: number;
      Prefix?: string;
      Marker?: string;
      Delimiter?: string;
    }): Promise<ObsResponse<ListObjectsResult>>;
    
    headObject(params: {
      Bucket: string;
      Key: string;
    }): Promise<ObsResponse<HeadObjectResult>>;
    
    createSignedUrlSync(params: {
      Bucket: string;
      Key: string;
      Expires: number;
      Method: string;
    }): string;
  }

  export default ObsClient;
}
