/**
 * 统一操作结果类型
 * 用于所有层级的操作结果
 */
export interface OperationResult<T> {
  success: boolean;
  data?: T;
  error?: Error;
  metadata?: Record<string, any>;
}

/**
 * 存储操作结果 (向后兼容)
 * @deprecated 使用 OperationResult<T> 替代
 */
export interface StorageResult<T> extends OperationResult<T> {}

/**
 * 对象元数据
 */
export interface ObjectMetadata {
  size: number;
  lastModified: Date;
  etag: string;
  contentType: string;
  customMetadata?: Record<string, string>;
}

/**
 * 操作选项
 */
export interface GetOptions {
  range?: { start: number; end: number };
  saveByType?: 'text' | 'arraybuffer' | 'blob';
}

export interface PutOptions {
  contentType?: string;
  metadata?: Record<string, string>;
  overwrite?: boolean;
  chunkSize?: number; // 分块上传大小
}

export interface ListOptions {
  maxKeys?: number;
  marker?: string;
  delimiter?: string;
  prefix?: string;
}

export interface SignedUrlOptions {
  expires?: number; // 过期时间（秒）
  method?: 'GET' | 'PUT' | 'DELETE';
}

/**
 * 批量操作选项
 */
export interface BatchOptions {
  concurrency?: number; // 并发数
  continueOnError?: boolean; // 遇到错误是否继续
}

/**
 * 流式操作选项
 */
export interface StreamOptions {
  chunkSize?: number;
  highWaterMark?: number;
}

/**
 * 存储统计信息
 */
export interface StorageStats {
  totalObjects: number;
  totalSize: number;
  lastModified: Date;
  provider: string;
}

/**
 * 统一结果工厂
 */
export class OperationResultFactory {
  /**
   * 创建成功结果
   */
  static success<T>(data?: T, metadata?: Record<string, any>): OperationResult<T> {
    return {
      success: true,
      data,
      metadata
    };
  }

  /**
   * 创建失败结果
   */
  static failure<T>(error: Error, metadata?: Record<string, any>): OperationResult<T> {
    return {
      success: false,
      error,
      metadata
    };
  }

  /**
   * 从Promise创建结果
   */
  static async fromPromise<T>(
    promise: Promise<T>,
    metadata?: Record<string, any>
  ): Promise<OperationResult<T>> {
    try {
      const data = await promise;
      return OperationResultFactory.success(data, metadata);
    } catch (error) {
      return OperationResultFactory.failure(error as Error, metadata);
    }
  }
}

/**
 * 存储结果工厂 (向后兼容)
 * @deprecated 使用 OperationResultFactory 替代
 */
export class StorageResultFactory extends OperationResultFactory {
  /**
   * 创建成功结果
   */
  static success<T>(data?: T, metadata?: Record<string, any>): StorageResult<T> {
    return super.success(data, metadata);
  }

  /**
   * 创建失败结果
   */
  static failure<T>(error: Error, metadata?: Record<string, any>): StorageResult<T> {
    return super.failure(error, metadata);
  }

  /**
   * 从Promise创建结果 (向后兼容)
   */
  static async fromPromise<T>(
    promise: Promise<T>,
    metadata?: Record<string, any>
  ): Promise<StorageResult<T>> {
    return super.fromPromise(promise, metadata);
  }

  /**
   * 合并多个结果
   */
  static merge<T>(results: StorageResult<T>[]): StorageResult<T[]> {
    const successResults = results.filter(r => r.success);
    const failureResults = results.filter(r => !r.success);

    if (failureResults.length === 0) {
      return StorageResultFactory.success(
        successResults.map(r => r.data!),
        { totalCount: results.length, successCount: successResults.length }
      );
    } else {
      const firstError = failureResults[0].error!;
      return StorageResultFactory.failure(
        new Error(`批量操作失败: ${failureResults.length}/${results.length} 个操作失败. 首个错误: ${firstError.message}`),
        {
          totalCount: results.length,
          successCount: successResults.length,
          failureCount: failureResults.length,
          errors: failureResults.map(r => r.error?.message)
        }
      );
    }
  }

  /**
   * 检查结果是否成功
   */
  static isSuccess<T>(result: StorageResult<T>): result is StorageResult<T> & { success: true; data: T } {
    return result.success && result.data !== undefined;
  }

  /**
   * 检查结果是否失败
   */
  static isFailure<T>(result: StorageResult<T>): result is StorageResult<T> & { success: false; error: Error } {
    return !result.success && result.error !== undefined;
  }
}

/**
 * 存储错误类型
 */
export enum StorageErrorType {
  CONNECTION_ERROR = 'CONNECTION_ERROR',
  AUTHENTICATION_ERROR = 'AUTHENTICATION_ERROR',
  PERMISSION_ERROR = 'PERMISSION_ERROR',
  NOT_FOUND_ERROR = 'NOT_FOUND_ERROR',
  CONFLICT_ERROR = 'CONFLICT_ERROR',
  QUOTA_EXCEEDED_ERROR = 'QUOTA_EXCEEDED_ERROR',
  TIMEOUT_ERROR = 'TIMEOUT_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR'
}

/**
 * 存储错误类
 */
export class StorageError extends Error {
  public readonly type: StorageErrorType;
  public readonly code?: string;
  public readonly statusCode?: number;
  public readonly metadata?: Record<string, any>;

  constructor(
    message: string,
    type: StorageErrorType = StorageErrorType.UNKNOWN_ERROR,
    code?: string,
    statusCode?: number,
    metadata?: Record<string, any>
  ) {
    super(message);
    this.name = 'StorageError';
    this.type = type;
    this.code = code;
    this.statusCode = statusCode;
    this.metadata = metadata;
  }

  /**
   * 创建连接错误
   */
  static connectionError(message: string, metadata?: Record<string, any>): StorageError {
    return new StorageError(message, StorageErrorType.CONNECTION_ERROR, undefined, undefined, metadata);
  }

  /**
   * 创建认证错误
   */
  static authenticationError(message: string, metadata?: Record<string, any>): StorageError {
    return new StorageError(message, StorageErrorType.AUTHENTICATION_ERROR, undefined, 401, metadata);
  }

  /**
   * 创建权限错误
   */
  static permissionError(message: string, metadata?: Record<string, any>): StorageError {
    return new StorageError(message, StorageErrorType.PERMISSION_ERROR, undefined, 403, metadata);
  }

  /**
   * 创建未找到错误
   */
  static notFoundError(message: string, metadata?: Record<string, any>): StorageError {
    return new StorageError(message, StorageErrorType.NOT_FOUND_ERROR, undefined, 404, metadata);
  }
}
