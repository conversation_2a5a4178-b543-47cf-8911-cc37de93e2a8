<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>流式上传下载简化测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: black; }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌊 流式上传下载简化测试</h1>
        
        <div class="test-section">
            <h3>测试配置</h3>
            <p>使用模拟存储提供者进行流式操作测试</p>
            <button class="btn-primary" onclick="initMockProvider()">初始化模拟提供者</button>
            <div id="init-result" class="result info" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>流式上传测试</h3>
            <button class="btn-success" onclick="testStreamUpload()">测试流式上传</button>
            <div id="upload-result" class="result info" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>流式下载测试</h3>
            <button class="btn-success" onclick="testStreamDownload()">测试流式下载</button>
            <div id="download-result" class="result info" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>综合测试</h3>
            <button class="btn-warning" onclick="runAllTests()">运行所有测试</button>
            <div id="all-result" class="result info" style="display: none;"></div>
        </div>
    </div>

    <script>
        let mockProvider = null;

        // 显示结果
        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = `result ${type}`;
            element.textContent = message;
        }

        // 创建模拟存储提供者
        function createMockProvider() {
            return {
                name: '模拟存储提供者',
                isInitialized: false,
                _storage: new Map(),

                async initialize() {
                    this.isInitialized = true;
                    console.log('模拟存储提供者初始化完成');
                },

                async put(key, data) {
                    if (!this.isInitialized) {
                        throw new Error('存储提供者未初始化');
                    }
                    this._storage.set(key, data);
                    console.log(`PUT: ${key} (${data.length} 字节)`);
                    return {
                        success: true,
                        metadata: { etag: `mock-etag-${Date.now()}` }
                    };
                },

                async get(key) {
                    if (!this.isInitialized) {
                        throw new Error('存储提供者未初始化');
                    }
                    const data = this._storage.get(key);
                    if (data) {
                        console.log(`GET: ${key} (${data.length} 字节)`);
                        return {
                            success: true,
                            data: data
                        };
                    } else {
                        return {
                            success: false,
                            error: new Error('文件不存在')
                        };
                    }
                },

                async getMetadata(key) {
                    if (!this.isInitialized) {
                        throw new Error('存储提供者未初始化');
                    }
                    const data = this._storage.get(key);
                    if (data) {
                        return {
                            success: true,
                            data: {
                                size: data.length,
                                lastModified: new Date(),
                                etag: `mock-etag-${key}`,
                                contentType: 'application/json'
                            }
                        };
                    } else {
                        return {
                            success: false,
                            error: new Error('文件不存在')
                        };
                    }
                },

                // 流式上传
                async putStream(key, stream, options = {}) {
                    if (!this.isInitialized) {
                        throw new Error('存储提供者未初始化');
                    }

                    try {
                        console.log(`开始流式上传: ${key}`);
                        const reader = stream.getReader();
                        const chunks = [];
                        let totalSize = 0;

                        // 读取所有数据块
                        while (true) {
                            const { done, value } = await reader.read();
                            if (done) break;

                            chunks.push(value);
                            totalSize += value.length;
                            console.log(`读取数据块: ${value.length} 字节, 总计: ${totalSize} 字节`);
                        }

                        // 合并数据
                        const combinedData = new Uint8Array(totalSize);
                        let offset = 0;
                        for (const chunk of chunks) {
                            combinedData.set(chunk, offset);
                            offset += chunk.length;
                        }

                        // 转换为字符串存储
                        const decoder = new TextDecoder();
                        const dataString = decoder.decode(combinedData);
                        
                        // 存储数据
                        this._storage.set(key, dataString);
                        
                        console.log(`流式上传完成: ${key}, 总大小: ${totalSize} 字节`);
                        return {
                            success: true,
                            metadata: {
                                totalSize: totalSize,
                                chunks: chunks.length,
                                uploadType: 'stream'
                            }
                        };
                    } catch (error) {
                        console.error(`流式上传失败: ${key}`, error);
                        return {
                            success: false,
                            error: error
                        };
                    }
                },

                // 流式下载
                async getStream(key, options = {}) {
                    if (!this.isInitialized) {
                        throw new Error('存储提供者未初始化');
                    }

                    try {
                        console.log(`开始流式下载: ${key}`);
                        
                        // 获取数据
                        const data = this._storage.get(key);
                        if (!data) {
                            throw new Error(`文件不存在: ${key}`);
                        }

                        const encoder = new TextEncoder();
                        const fileData = encoder.encode(data);
                        const chunkSize = options.chunkSize || 1024;

                        console.log(`文件大小: ${fileData.length} 字节, 分块大小: ${chunkSize} 字节`);

                        // 创建可读流
                        const stream = new ReadableStream({
                            start(controller) {
                                let currentOffset = 0;
                                let chunkNumber = 1;

                                const pushChunk = () => {
                                    if (currentOffset >= fileData.length) {
                                        console.log(`流式下载完成: ${key}, 共 ${chunkNumber - 1} 个分块`);
                                        controller.close();
                                        return;
                                    }

                                    const endOffset = Math.min(currentOffset + chunkSize, fileData.length);
                                    const chunkData = fileData.slice(currentOffset, endOffset);

                                    console.log(`下载分块 ${chunkNumber}: ${currentOffset}-${endOffset - 1} (${chunkData.length} 字节)`);

                                    controller.enqueue(chunkData);
                                    currentOffset = endOffset;
                                    chunkNumber++;

                                    // 模拟异步下载
                                    setTimeout(pushChunk, 10);
                                };

                                pushChunk();
                            }
                        });

                        return stream;
                    } catch (error) {
                        console.error(`流式下载失败: ${key}`, error);
                        throw error;
                    }
                }
            };
        }

        // 初始化模拟提供者
        window.initMockProvider = async function() {
            try {
                showResult('init-result', '正在初始化模拟存储提供者...', 'info');
                
                mockProvider = createMockProvider();
                await mockProvider.initialize();
                
                showResult('init-result', '✅ 模拟存储提供者初始化成功！', 'success');
            } catch (error) {
                showResult('init-result', `❌ 初始化失败: ${error.message}`, 'error');
            }
        };

        // 测试流式上传
        window.testStreamUpload = async function() {
            if (!mockProvider || !mockProvider.isInitialized) {
                showResult('upload-result', '请先初始化模拟存储提供者', 'error');
                return;
            }

            try {
                showResult('upload-result', '正在测试流式上传...', 'info');

                const testKey = 'test/stream-upload.json';
                const testData = {
                    message: '流式上传测试数据',
                    timestamp: new Date().toISOString(),
                    data: Array.from({length: 50}, (_, i) => `数据项${i}: ${Math.random()}`)
                };

                // 创建测试数据流
                const dataString = JSON.stringify(testData, null, 2);
                const encoder = new TextEncoder();
                const dataBytes = encoder.encode(dataString);

                // 创建可读流
                const stream = new ReadableStream({
                    start(controller) {
                        const chunkSize = 512; // 512字节分块
                        let offset = 0;

                        const pushChunk = () => {
                            if (offset >= dataBytes.length) {
                                controller.close();
                                return;
                            }

                            const chunk = dataBytes.slice(offset, offset + chunkSize);
                            controller.enqueue(chunk);
                            offset += chunkSize;

                            setTimeout(pushChunk, 5);
                        };

                        pushChunk();
                    }
                });

                const startTime = Date.now();
                const result = await mockProvider.putStream(testKey, stream, {
                    chunkSize: 1024
                });
                const endTime = Date.now();

                if (result.success) {
                    const resultText = `✅ 流式上传测试成功！
测试文件: ${testKey}
数据大小: ${dataBytes.length} 字节
上传耗时: ${endTime - startTime}ms
分块数量: ${result.metadata.chunks}
上传类型: ${result.metadata.uploadType}
时间: ${new Date().toLocaleString()}`;

                    showResult('upload-result', resultText, 'success');
                } else {
                    throw result.error;
                }
            } catch (error) {
                showResult('upload-result', `❌ 流式上传测试失败: ${error.message}`, 'error');
            }
        };

        // 测试流式下载
        window.testStreamDownload = async function() {
            if (!mockProvider || !mockProvider.isInitialized) {
                showResult('download-result', '请先初始化模拟存储提供者', 'error');
                return;
            }

            try {
                showResult('download-result', '正在测试流式下载...', 'info');

                const testKey = 'test/stream-download.json';
                const testData = {
                    message: '流式下载测试数据',
                    timestamp: new Date().toISOString(),
                    content: 'x'.repeat(2000) // 2KB的测试内容
                };

                // 先上传测试文件
                const dataString = JSON.stringify(testData, null, 2);
                const putResult = await mockProvider.put(testKey, dataString);
                if (!putResult.success) {
                    throw new Error('上传测试文件失败');
                }

                const startTime = Date.now();
                const stream = await mockProvider.getStream(testKey, {
                    chunkSize: 512 // 512字节分块下载
                });

                // 读取流数据
                const reader = stream.getReader();
                const chunks = [];
                let totalBytes = 0;
                let chunkCount = 0;

                while (true) {
                    const { done, value } = await reader.read();
                    if (done) break;

                    chunks.push(value);
                    totalBytes += value.length;
                    chunkCount++;
                }

                const endTime = Date.now();

                // 重组数据验证
                const combinedData = new Uint8Array(totalBytes);
                let offset = 0;
                for (const chunk of chunks) {
                    combinedData.set(chunk, offset);
                    offset += chunk.length;
                }

                const decoder = new TextDecoder();
                const downloadedString = decoder.decode(combinedData);
                const downloadedData = JSON.parse(downloadedString);
                const dataMatches = downloadedData.message === testData.message;

                const resultText = `✅ 流式下载测试成功！
测试文件: ${testKey}
下载大小: ${totalBytes} 字节
下载耗时: ${endTime - startTime}ms
分块数量: ${chunkCount}
平均分块: ${Math.round(totalBytes / chunkCount)} 字节
数据验证: ${dataMatches ? '通过' : '失败'}
时间: ${new Date().toLocaleString()}`;

                showResult('download-result', resultText, 'success');
            } catch (error) {
                showResult('download-result', `❌ 流式下载测试失败: ${error.message}`, 'error');
            }
        };

        // 运行所有测试
        window.runAllTests = async function() {
            showResult('all-result', '正在运行所有测试...', 'info');
            
            try {
                // 初始化
                await initMockProvider();
                await new Promise(resolve => setTimeout(resolve, 500));
                
                // 流式上传测试
                await testStreamUpload();
                await new Promise(resolve => setTimeout(resolve, 500));
                
                // 流式下载测试
                await testStreamDownload();
                
                showResult('all-result', '🎉 所有测试完成！请查看各个测试结果。', 'success');
            } catch (error) {
                showResult('all-result', `❌ 测试执行失败: ${error.message}`, 'error');
            }
        };
    </script>
</body>
</html>
