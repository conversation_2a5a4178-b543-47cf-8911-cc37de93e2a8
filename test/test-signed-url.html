<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>签名URL测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔗 签名URL测试</h1>
        
        <div>
            <button onclick="testCompleteFlow()">完整流程测试</button>
            <button onclick="testExistingFile()">测试现有文件</button>
            <button onclick="testNonExistentFile()">测试不存在文件</button>
            <button onclick="clearResults()">清除结果</button>
        </div>

        <div id="result" class="result info" style="display: none;">
            等待测试结果...
        </div>
    </div>

    <script>
        // MinIO配置
        const config = {
            endpoint: 'http://127.0.0.1:9000',
            accessKey: 'FsYFOP9cOOYDyfM9odzX',
            secretKey: 'AcQaaZTcoOh6GKMuLKAcdIo4W0qcWQ8VWKkqQvLl',
            bucketName: 'eversnip',
            region: 'us-east-1'
        };

        let minioProvider = null;

        function showResult(message, type = 'info') {
            const resultDiv = document.getElementById('result');
            resultDiv.style.display = 'block';
            resultDiv.className = `result ${type}`;
            resultDiv.textContent = message;
        }

        function clearResults() {
            document.getElementById('result').style.display = 'none';
        }

        // 从主测试页面复制MinIO提供者创建函数（简化版）
        function createMinioProvider() {
            return {
                name: 'MinIO存储提供者',
                type: 'minio',
                isInitialized: false,
                _config: null,

                async initialize(config) {
                    this._config = config;
                    this.isInitialized = true;
                },

                async put(key, data) {
                    try {
                        const url = `${this._config.endpoint}/${this._config.bucketName}/${key}`;
                        const headers = await this.createAuthHeaders('PUT', `/${this._config.bucketName}/${key}`, {}, data);

                        const response = await fetch(url, {
                            method: 'PUT',
                            headers: headers,
                            body: data
                        });

                        if (response.ok) {
                            const etag = response.headers.get('etag');
                            console.log(`MinIO PUT成功: ${key}`);
                            return {
                                success: true,
                                data: undefined,
                                metadata: { etag: etag }
                            };
                        } else {
                            const errorText = await response.text();
                            throw new Error(`上传对象失败: ${response.status} ${errorText}`);
                        }
                    } catch (error) {
                        console.error(`MinIO PUT失败: ${key}`, error);
                        return {
                            success: false,
                            error: error
                        };
                    }
                },

                async getMetadata(key) {
                    try {
                        const url = `${this._config.endpoint}/${this._config.bucketName}/${key}`;
                        const headers = await this.createAuthHeaders('HEAD', `/${this._config.bucketName}/${key}`);

                        const response = await fetch(url, {
                            method: 'HEAD',
                            headers: headers
                        });

                        if (response.ok) {
                            console.log(`MinIO METADATA成功: ${key}`);
                            return {
                                success: true,
                                data: {
                                    size: parseInt(response.headers.get('content-length') || '0'),
                                    lastModified: new Date(response.headers.get('last-modified') || Date.now()),
                                    etag: response.headers.get('etag') || '',
                                    contentType: response.headers.get('content-type') || 'application/octet-stream'
                                }
                            };
                        } else if (response.status === 404) {
                            return {
                                success: false,
                                error: new Error('对象不存在')
                            };
                        } else {
                            const errorText = await response.text();
                            throw new Error(`获取元数据失败: ${response.status} ${errorText}`);
                        }
                    } catch (error) {
                        console.error(`MinIO METADATA失败: ${key}`, error);
                        return {
                            success: false,
                            error: error
                        };
                    }
                },

                async getSignedUrl(key, options) {
                    try {
                        // 首先检查文件是否存在
                        console.log(`检查文件是否存在: ${key}`);
                        const metadataResult = await this.getMetadata(key);
                        if (!metadataResult.success) {
                            throw new Error(`文件不存在: ${key}`);
                        }
                        
                        console.log(`文件存在，开始生成签名URL: ${key}`);
                        
                        const expires = options?.expires || 3600;
                        const method = options?.method || 'GET';
                        
                        const now = new Date();
                        const dateStamp = now.toISOString().slice(0, 10).replace(/-/g, '');
                        const timeStamp = now.toISOString().slice(0, 19).replace(/[-:]/g, '') + 'Z';
                        
                        const region = this._config.region || 'us-east-1';
                        const service = 's3';
                        const algorithm = 'AWS4-HMAC-SHA256';
                        const credentialScope = `${dateStamp}/${region}/${service}/aws4_request`;
                        
                        const queryParams = {
                            'X-Amz-Algorithm': algorithm,
                            'X-Amz-Credential': `${this._config.accessKey}/${credentialScope}`,
                            'X-Amz-Date': timeStamp,
                            'X-Amz-Expires': expires.toString(),
                            'X-Amz-SignedHeaders': 'host'
                        };
                        
                        const canonicalQueryString = Object.keys(queryParams)
                            .sort()
                            .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(queryParams[key])}`)
                            .join('&');
                        
                        const canonicalRequest = [
                            method,
                            `/${this._config.bucketName}/${key}`,
                            canonicalQueryString,
                            `host:${new URL(this._config.endpoint).host}\n`,
                            'host',
                            'UNSIGNED-PAYLOAD'
                        ].join('\n');
                        
                        const stringToSign = [
                            algorithm,
                            timeStamp,
                            credentialScope,
                            await this.sha256(canonicalRequest)
                        ].join('\n');
                        
                        const signingKey = await this.getSignatureKey(this._config.secretKey, dateStamp, region, service);
                        const signature = await this.hmacSha256(signingKey, stringToSign);
                        
                        const signedUrl = `${this._config.endpoint}/${this._config.bucketName}/${key}?${canonicalQueryString}&X-Amz-Signature=${signature}`;
                        
                        console.log(`MinIO 预签名URL生成成功: ${key}`);
                        console.log(`文件大小: ${metadataResult.data.size} 字节`);
                        console.log(`签名URL: ${signedUrl}`);
                        
                        return signedUrl;
                    } catch (error) {
                        console.error(`MinIO 预签名URL生成失败: ${key}`, error);
                        throw error;
                    }
                },

                async delete(key) {
                    try {
                        const url = `${this._config.endpoint}/${this._config.bucketName}/${key}`;
                        const headers = await this.createAuthHeaders('DELETE', `/${this._config.bucketName}/${key}`);

                        const response = await fetch(url, {
                            method: 'DELETE',
                            headers: headers
                        });

                        if (response.ok || response.status === 404) {
                            console.log(`MinIO DELETE成功: ${key}`);
                            return {
                                success: true,
                                data: undefined
                            };
                        } else {
                            const errorText = await response.text();
                            throw new Error(`删除对象失败: ${response.status} ${errorText}`);
                        }
                    } catch (error) {
                        console.error(`MinIO DELETE失败: ${key}`, error);
                        return {
                            success: false,
                            error: error
                        };
                    }
                },

                // AWS S3签名认证方法
                async createAuthHeaders(method, path, queryParams = {}, body = '') {
                    const now = new Date();
                    const dateStamp = now.toISOString().slice(0, 10).replace(/-/g, '');
                    const timeStamp = now.toISOString().slice(0, 19).replace(/[-:]/g, '') + 'Z';
                    
                    const region = this._config.region || 'us-east-1';
                    const service = 's3';
                    
                    const canonicalUri = path;
                    const canonicalQueryString = Object.keys(queryParams)
                        .sort()
                        .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(queryParams[key])}`)
                        .join('&');
                    
                    const headers = {
                        'host': new URL(this._config.endpoint).host,
                        'x-amz-date': timeStamp,
                        'x-amz-content-sha256': await this.sha256(body)
                    };
                    
                    const canonicalHeaders = Object.keys(headers)
                        .sort()
                        .map(key => `${key}:${headers[key]}\n`)
                        .join('');
                    
                    const signedHeaders = Object.keys(headers).sort().join(';');
                    
                    const canonicalRequest = [
                        method,
                        canonicalUri,
                        canonicalQueryString,
                        canonicalHeaders,
                        signedHeaders,
                        headers['x-amz-content-sha256']
                    ].join('\n');
                    
                    const algorithm = 'AWS4-HMAC-SHA256';
                    const credentialScope = `${dateStamp}/${region}/${service}/aws4_request`;
                    const stringToSign = [
                        algorithm,
                        timeStamp,
                        credentialScope,
                        await this.sha256(canonicalRequest)
                    ].join('\n');
                    
                    const signingKey = await this.getSignatureKey(this._config.secretKey, dateStamp, region, service);
                    const signature = await this.hmacSha256(signingKey, stringToSign);
                    
                    const authorization = `${algorithm} Credential=${this._config.accessKey}/${credentialScope}, SignedHeaders=${signedHeaders}, Signature=${signature}`;
                    
                    return {
                        'Authorization': authorization,
                        'X-Amz-Date': timeStamp,
                        'X-Amz-Content-Sha256': headers['x-amz-content-sha256']
                    };
                },

                async sha256(data) {
                    const encoder = new TextEncoder();
                    const dataBuffer = typeof data === 'string' ? encoder.encode(data) : data;
                    const hashBuffer = await crypto.subtle.digest('SHA-256', dataBuffer);
                    return Array.from(new Uint8Array(hashBuffer))
                        .map(b => b.toString(16).padStart(2, '0'))
                        .join('');
                },

                async hmacSha256(key, data) {
                    const encoder = new TextEncoder();
                    const keyBuffer = typeof key === 'string' ? encoder.encode(key) : key;
                    const dataBuffer = typeof data === 'string' ? encoder.encode(data) : data;
                    
                    const cryptoKey = await crypto.subtle.importKey(
                        'raw',
                        keyBuffer,
                        { name: 'HMAC', hash: 'SHA-256' },
                        false,
                        ['sign']
                    );
                    
                    const signature = await crypto.subtle.sign('HMAC', cryptoKey, dataBuffer);
                    return Array.from(new Uint8Array(signature))
                        .map(b => b.toString(16).padStart(2, '0'))
                        .join('');
                },

                async getSignatureKey(key, dateStamp, regionName, serviceName) {
                    const kDate = await this.hmacSha256('AWS4' + key, dateStamp);
                    const kRegion = await this.hmacSha256(this.hexToBytes(kDate), regionName);
                    const kService = await this.hmacSha256(this.hexToBytes(kRegion), serviceName);
                    const kSigning = await this.hmacSha256(this.hexToBytes(kService), 'aws4_request');
                    return this.hexToBytes(kSigning);
                },

                hexToBytes(hex) {
                    const bytes = new Uint8Array(hex.length / 2);
                    for (let i = 0; i < hex.length; i += 2) {
                        bytes[i / 2] = parseInt(hex.substr(i, 2), 16);
                    }
                    return bytes;
                }
            };
        }

        // 测试函数
        async function testCompleteFlow() {
            try {
                showResult('正在进行完整流程测试...', 'info');
                
                // 初始化提供者
                minioProvider = createMinioProvider();
                await minioProvider.initialize(config);
                
                const testKey = 'test/signed-url-complete-test.json';
                const testData = {
                    message: '完整流程测试文件',
                    timestamp: new Date().toISOString(),
                    random: Math.random()
                };

                // 1. 上传文件
                const putResult = await minioProvider.put(testKey, JSON.stringify(testData));
                if (!putResult.success) {
                    throw new Error(`上传失败: ${putResult.error?.message}`);
                }

                // 2. 生成签名URL
                const signedUrl = await minioProvider.getSignedUrl(testKey, {
                    expires: 3600,
                    method: 'GET'
                });

                // 3. 清理文件
                await minioProvider.delete(testKey);

                showResult(`✅ 完整流程测试成功！
1. 文件上传: 成功
2. 签名URL生成: 成功
3. 文件清理: 成功

测试文件: ${testKey}
签名URL: ${signedUrl}
URL长度: ${signedUrl.length} 字符
包含签名: ${signedUrl.includes('X-Amz-Signature') ? '是' : '否'}
时间: ${new Date().toLocaleString()}`, 'success');
            } catch (error) {
                showResult(`❌ 完整流程测试失败！
错误: ${error.message}
堆栈: ${error.stack}`, 'error');
            }
        }

        async function testExistingFile() {
            try {
                showResult('正在测试现有文件的签名URL...', 'info');
                
                if (!minioProvider) {
                    minioProvider = createMinioProvider();
                    await minioProvider.initialize(config);
                }
                
                const testKey = 'test/existing-file-test.json';
                const testData = { message: '现有文件测试', created: new Date().toISOString() };

                // 先上传文件
                const putResult = await minioProvider.put(testKey, JSON.stringify(testData));
                if (!putResult.success) {
                    throw new Error(`上传失败: ${putResult.error?.message}`);
                }

                // 为现有文件生成签名URL
                const signedUrl = await minioProvider.getSignedUrl(testKey, {
                    expires: 1800,
                    method: 'GET'
                });

                // 清理文件
                await minioProvider.delete(testKey);

                showResult(`✅ 现有文件签名URL测试成功！
文件: ${testKey}
签名URL: ${signedUrl}
过期时间: 1800秒 (30分钟)
时间: ${new Date().toLocaleString()}`, 'success');
            } catch (error) {
                showResult(`❌ 现有文件签名URL测试失败！
错误: ${error.message}`, 'error');
            }
        }

        async function testNonExistentFile() {
            try {
                showResult('正在测试不存在文件的签名URL...', 'info');
                
                if (!minioProvider) {
                    minioProvider = createMinioProvider();
                    await minioProvider.initialize(config);
                }
                
                const testKey = 'test/non-existent-file.json';

                // 尝试为不存在的文件生成签名URL
                try {
                    const signedUrl = await minioProvider.getSignedUrl(testKey, {
                        expires: 3600,
                        method: 'GET'
                    });
                    
                    // 如果到这里说明没有抛出错误，这是不对的
                    showResult(`❌ 不存在文件签名URL测试失败！
应该抛出错误但没有抛出
生成的URL: ${signedUrl}`, 'error');
                } catch (error) {
                    // 这是预期的行为
                    if (error.message.includes('文件不存在')) {
                        showResult(`✅ 不存在文件签名URL测试成功！
正确抛出错误: ${error.message}
测试文件: ${testKey}
时间: ${new Date().toLocaleString()}`, 'success');
                    } else {
                        throw error;
                    }
                }
            } catch (error) {
                showResult(`❌ 不存在文件签名URL测试异常！
错误: ${error.message}`, 'error');
            }
        }

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            showResult('签名URL测试页面已加载，点击按钮开始测试', 'info');
        });
    </script>
</body>
</html>
