<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MinIO连接测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .config {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .config h3 {
            margin-top: 0;
            color: #495057;
        }
        .config p {
            margin: 5px 0;
            font-family: monospace;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🗄️ MinIO连接测试</h1>
        
        <div class="config">
            <h3>MinIO配置信息</h3>
            <p><strong>Endpoint:</strong> http://127.0.0.1:9000</p>
            <p><strong>Access Key:</strong> FsYFOP9cOOYDyfM9odzX</p>
            <p><strong>Secret Key:</strong> AcQaaZTcoOh6GKMuLKAcdIo4W0qcWQ8VWKkqQvLl</p>
            <p><strong>Bucket:</strong> eversnip</p>
            <p><strong>Region:</strong> us-east-1</p>
        </div>

        <div>
            <button onclick="testConnection()">测试连接</button>
            <button onclick="testPutObject()">测试上传</button>
            <button onclick="testGetObject()">测试下载</button>
            <button onclick="testListObjects()">测试列表</button>
            <button onclick="testDeleteObject()">测试删除</button>
            <button onclick="clearResults()">清除结果</button>
        </div>

        <div id="result" class="result info" style="display: none;">
            等待测试结果...
        </div>
    </div>

    <script>
        // MinIO配置
        const config = {
            endpoint: 'http://127.0.0.1:9000',
            accessKey: 'FsYFOP9cOOYDyfM9odzX',
            secretKey: 'AcQaaZTcoOh6GKMuLKAcdIo4W0qcWQ8VWKkqQvLl',
            bucketName: 'eversnip',
            region: 'us-east-1'
        };

        function showResult(message, type = 'info') {
            const resultDiv = document.getElementById('result');
            resultDiv.style.display = 'block';
            resultDiv.className = `result ${type}`;
            resultDiv.textContent = message;
        }

        function clearResults() {
            document.getElementById('result').style.display = 'none';
        }

        // AWS S3签名认证方法
        async function createAuthHeaders(method, path, queryParams = {}, body = '') {
            const now = new Date();
            const dateStamp = now.toISOString().slice(0, 10).replace(/-/g, '');
            const timeStamp = now.toISOString().slice(0, 19).replace(/[-:]/g, '') + 'Z';
            
            const region = config.region;
            const service = 's3';
            
            // 创建规范请求
            const canonicalUri = path;
            const canonicalQueryString = Object.keys(queryParams)
                .sort()
                .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(queryParams[key])}`)
                .join('&');
            
            const headers = {
                'host': new URL(config.endpoint).host,
                'x-amz-date': timeStamp,
                'x-amz-content-sha256': await sha256(body)
            };
            
            const canonicalHeaders = Object.keys(headers)
                .sort()
                .map(key => `${key}:${headers[key]}\n`)
                .join('');
            
            const signedHeaders = Object.keys(headers).sort().join(';');
            
            const canonicalRequest = [
                method,
                canonicalUri,
                canonicalQueryString,
                canonicalHeaders,
                signedHeaders,
                headers['x-amz-content-sha256']
            ].join('\n');
            
            // 创建签名字符串
            const algorithm = 'AWS4-HMAC-SHA256';
            const credentialScope = `${dateStamp}/${region}/${service}/aws4_request`;
            const stringToSign = [
                algorithm,
                timeStamp,
                credentialScope,
                await sha256(canonicalRequest)
            ].join('\n');
            
            // 计算签名
            const signingKey = await getSignatureKey(config.secretKey, dateStamp, region, service);
            const signature = await hmacSha256(signingKey, stringToSign);
            
            // 创建授权头
            const authorization = `${algorithm} Credential=${config.accessKey}/${credentialScope}, SignedHeaders=${signedHeaders}, Signature=${signature}`;
            
            return {
                'Authorization': authorization,
                'X-Amz-Date': timeStamp,
                'X-Amz-Content-Sha256': headers['x-amz-content-sha256']
            };
        }

        async function sha256(data) {
            const encoder = new TextEncoder();
            const dataBuffer = typeof data === 'string' ? encoder.encode(data) : data;
            const hashBuffer = await crypto.subtle.digest('SHA-256', dataBuffer);
            return Array.from(new Uint8Array(hashBuffer))
                .map(b => b.toString(16).padStart(2, '0'))
                .join('');
        }

        async function hmacSha256(key, data) {
            const encoder = new TextEncoder();
            const keyBuffer = typeof key === 'string' ? encoder.encode(key) : key;
            const dataBuffer = encoder.encode(data);
            
            const cryptoKey = await crypto.subtle.importKey(
                'raw',
                keyBuffer,
                { name: 'HMAC', hash: 'SHA-256' },
                false,
                ['sign']
            );
            
            const signature = await crypto.subtle.sign('HMAC', cryptoKey, dataBuffer);
            return Array.from(new Uint8Array(signature))
                .map(b => b.toString(16).padStart(2, '0'))
                .join('');
        }

        async function getSignatureKey(key, dateStamp, regionName, serviceName) {
            const kDate = await hmacSha256('AWS4' + key, dateStamp);
            const kRegion = await hmacSha256(hexToBytes(kDate), regionName);
            const kService = await hmacSha256(hexToBytes(kRegion), serviceName);
            const kSigning = await hmacSha256(hexToBytes(kService), 'aws4_request');
            return hexToBytes(kSigning);
        }

        function hexToBytes(hex) {
            const bytes = new Uint8Array(hex.length / 2);
            for (let i = 0; i < hex.length; i += 2) {
                bytes[i / 2] = parseInt(hex.substr(i, 2), 16);
            }
            return bytes;
        }

        // 测试函数
        async function testConnection() {
            showResult('正在测试MinIO连接...', 'info');
            
            try {
                const url = `${config.endpoint}/${config.bucketName}?list-type=2&max-keys=1`;
                const headers = await createAuthHeaders('GET', `/${config.bucketName}`, {
                    'list-type': '2',
                    'max-keys': '1'
                });

                const response = await fetch(url, {
                    method: 'GET',
                    headers: headers
                });

                if (response.ok) {
                    const xmlText = await response.text();
                    showResult(`✅ MinIO连接测试成功！
状态码: ${response.status}
响应头: ${JSON.stringify(Object.fromEntries(response.headers.entries()), null, 2)}
响应内容: ${xmlText}`, 'success');
                } else {
                    const errorText = await response.text();
                    showResult(`❌ MinIO连接测试失败！
状态码: ${response.status}
错误信息: ${errorText}`, 'error');
                }
            } catch (error) {
                showResult(`❌ MinIO连接测试异常！
错误: ${error.message}
堆栈: ${error.stack}`, 'error');
            }
        }

        async function testPutObject() {
            showResult('正在测试上传对象...', 'info');
            
            try {
                const key = 'test/test-file.txt';
                const data = `测试文件内容
创建时间: ${new Date().toISOString()}
随机数: ${Math.random()}`;
                
                const url = `${config.endpoint}/${config.bucketName}/${key}`;
                const headers = await createAuthHeaders('PUT', `/${config.bucketName}/${key}`, {}, data);

                const response = await fetch(url, {
                    method: 'PUT',
                    headers: headers,
                    body: data
                });

                if (response.ok) {
                    const etag = response.headers.get('etag');
                    showResult(`✅ 上传对象成功！
文件: ${key}
ETag: ${etag}
状态码: ${response.status}`, 'success');
                } else {
                    const errorText = await response.text();
                    showResult(`❌ 上传对象失败！
文件: ${key}
状态码: ${response.status}
错误信息: ${errorText}`, 'error');
                }
            } catch (error) {
                showResult(`❌ 上传对象异常！
错误: ${error.message}`, 'error');
            }
        }

        async function testGetObject() {
            showResult('正在测试下载对象...', 'info');
            
            try {
                const key = 'test/test-file.txt';
                const url = `${config.endpoint}/${config.bucketName}/${key}`;
                const headers = await createAuthHeaders('GET', `/${config.bucketName}/${key}`);

                const response = await fetch(url, {
                    method: 'GET',
                    headers: headers
                });

                if (response.ok) {
                    const content = await response.text();
                    showResult(`✅ 下载对象成功！
文件: ${key}
内容长度: ${content.length}
内容: ${content}`, 'success');
                } else {
                    const errorText = await response.text();
                    showResult(`❌ 下载对象失败！
文件: ${key}
状态码: ${response.status}
错误信息: ${errorText}`, 'error');
                }
            } catch (error) {
                showResult(`❌ 下载对象异常！
错误: ${error.message}`, 'error');
            }
        }

        async function testListObjects() {
            showResult('正在测试列出对象...', 'info');
            
            try {
                const url = `${config.endpoint}/${config.bucketName}?list-type=2&max-keys=10`;
                const headers = await createAuthHeaders('GET', `/${config.bucketName}`, {
                    'list-type': '2',
                    'max-keys': '10'
                });

                const response = await fetch(url, {
                    method: 'GET',
                    headers: headers
                });

                if (response.ok) {
                    const xmlText = await response.text();
                    const parser = new DOMParser();
                    const xmlDoc = parser.parseFromString(xmlText, 'text/xml');
                    const contents = xmlDoc.getElementsByTagName('Contents');
                    const files = [];
                    
                    for (let i = 0; i < contents.length; i++) {
                        const keyElement = contents[i].getElementsByTagName('Key')[0];
                        const sizeElement = contents[i].getElementsByTagName('Size')[0];
                        const modifiedElement = contents[i].getElementsByTagName('LastModified')[0];
                        
                        if (keyElement) {
                            files.push({
                                key: keyElement.textContent,
                                size: sizeElement ? sizeElement.textContent : '0',
                                lastModified: modifiedElement ? modifiedElement.textContent : ''
                            });
                        }
                    }
                    
                    showResult(`✅ 列出对象成功！
对象数量: ${files.length}
对象列表:
${files.map(f => `  ${f.key} (${f.size} bytes, ${f.lastModified})`).join('\n')}

原始XML响应:
${xmlText}`, 'success');
                } else {
                    const errorText = await response.text();
                    showResult(`❌ 列出对象失败！
状态码: ${response.status}
错误信息: ${errorText}`, 'error');
                }
            } catch (error) {
                showResult(`❌ 列出对象异常！
错误: ${error.message}`, 'error');
            }
        }

        async function testDeleteObject() {
            showResult('正在测试删除对象...', 'info');
            
            try {
                const key = 'test/test-file.txt';
                const url = `${config.endpoint}/${config.bucketName}/${key}`;
                const headers = await createAuthHeaders('DELETE', `/${config.bucketName}/${key}`);

                const response = await fetch(url, {
                    method: 'DELETE',
                    headers: headers
                });

                if (response.ok || response.status === 404) {
                    showResult(`✅ 删除对象成功！
文件: ${key}
状态码: ${response.status}`, 'success');
                } else {
                    const errorText = await response.text();
                    showResult(`❌ 删除对象失败！
文件: ${key}
状态码: ${response.status}
错误信息: ${errorText}`, 'error');
                }
            } catch (error) {
                showResult(`❌ 删除对象异常！
错误: ${error.message}`, 'error');
            }
        }
    </script>
</body>
</html>
