#!/usr/bin/env node

/**
 * 自动化流式上传下载测试脚本
 * 使用 Puppeteer 自动化浏览器测试
 */

const puppeteer = require('puppeteer');
const path = require('path');

async function runStreamTests() {
    console.log('🚀 开始运行流式上传下载测试...\n');

    let browser;
    try {
        // 启动浏览器
        browser = await puppeteer.launch({
            headless: false, // 显示浏览器界面
            devtools: true,  // 打开开发者工具
            args: ['--no-sandbox', '--disable-setuid-sandbox']
        });

        const page = await browser.newPage();
        
        // 设置视口大小
        await page.setViewport({ width: 1200, height: 800 });

        // 监听控制台输出
        page.on('console', msg => {
            const type = msg.type();
            const text = msg.text();
            if (type === 'log') {
                console.log(`📝 ${text}`);
            } else if (type === 'error') {
                console.error(`❌ ${text}`);
            } else if (type === 'warn') {
                console.warn(`⚠️  ${text}`);
            }
        });

        // 监听页面错误
        page.on('pageerror', error => {
            console.error(`💥 页面错误: ${error.message}`);
        });

        // 访问测试页面
        const testUrl = 'http://localhost:3000/infrastructure-test.html';
        console.log(`🌐 访问测试页面: ${testUrl}`);
        await page.goto(testUrl, { waitUntil: 'networkidle0' });

        // 等待页面加载完成
        await page.waitForSelector('#storage-type', { timeout: 10000 });
        console.log('✅ 测试页面加载完成\n');

        // 配置MinIO存储
        console.log('⚙️  配置MinIO存储...');
        await page.select('#storage-type', 'minio');
        
        // 等待配置字段出现
        await page.waitForSelector('#config-endpoint', { timeout: 5000 });
        
        // 填写MinIO配置
        await page.type('#config-endpoint', 'http://127.0.0.1:9000');
        await page.type('#config-accessKey', 'minioadmin');
        await page.type('#config-secretKey', 'minioadmin');
        await page.type('#config-bucketName', 'eversnip');
        await page.type('#config-region', 'us-east-1');
        
        console.log('✅ MinIO配置完成');

        // 初始化存储
        console.log('🔧 初始化存储提供者...');
        await page.click('button[onclick="initializeStorage()"]');
        
        // 等待初始化完成
        await page.waitForFunction(() => {
            const result = document.getElementById('storage-results');
            return result && result.style.display !== 'none' && result.textContent.includes('初始化');
        }, { timeout: 15000 });

        // 检查初始化结果
        const initResult = await page.$eval('#storage-results', el => el.textContent);
        if (initResult.includes('✅')) {
            console.log('✅ 存储提供者初始化成功\n');
        } else {
            throw new Error(`存储初始化失败: ${initResult}`);
        }

        // 运行流式上传测试
        console.log('📤 开始流式上传测试...');
        await page.click('button[onclick="testStreamUpload()"]');
        
        // 等待测试完成
        await page.waitForFunction(() => {
            const result = document.getElementById('stream-results');
            return result && result.style.display !== 'none' && 
                   (result.textContent.includes('✅') || result.textContent.includes('❌'));
        }, { timeout: 30000 });

        const uploadResult = await page.$eval('#stream-results', el => el.textContent);
        console.log(`📤 流式上传测试结果:\n${uploadResult}\n`);

        // 运行流式下载测试
        console.log('📥 开始流式下载测试...');
        await page.click('button[onclick="testStreamDownload()"]');
        
        // 等待测试完成
        await page.waitForFunction(() => {
            const result = document.getElementById('stream-results');
            return result && result.style.display !== 'none' && 
                   (result.textContent.includes('✅') || result.textContent.includes('❌'));
        }, { timeout: 30000 });

        const downloadResult = await page.$eval('#stream-results', el => el.textContent);
        console.log(`📥 流式下载测试结果:\n${downloadResult}\n`);

        // 运行大文件流式测试
        console.log('📦 开始大文件流式测试...');
        await page.click('button[onclick="testLargeFileStream()"]');
        
        // 等待测试完成（大文件测试可能需要更长时间）
        await page.waitForFunction(() => {
            const result = document.getElementById('stream-results');
            return result && result.style.display !== 'none' && 
                   (result.textContent.includes('✅') || result.textContent.includes('❌'));
        }, { timeout: 60000 });

        const largeFileResult = await page.$eval('#stream-results', el => el.textContent);
        console.log(`📦 大文件流式测试结果:\n${largeFileResult}\n`);

        // 运行性能测试
        console.log('⚡ 开始流式性能测试...');
        await page.click('button[onclick="testStreamPerformance()"]');
        
        // 等待性能测试完成（性能测试需要更长时间）
        await page.waitForFunction(() => {
            const result = document.getElementById('stream-results');
            return result && result.style.display !== 'none' && 
                   (result.textContent.includes('✅') || result.textContent.includes('❌'));
        }, { timeout: 120000 });

        const performanceResult = await page.$eval('#stream-results', el => el.textContent);
        console.log(`⚡ 流式性能测试结果:\n${performanceResult}\n`);

        // 获取测试统计
        const stats = await page.evaluate(() => {
            return {
                total: document.getElementById('total-tests').textContent,
                passed: document.getElementById('passed-tests').textContent,
                failed: document.getElementById('failed-tests').textContent,
                successRate: document.getElementById('success-rate').textContent
            };
        });

        console.log('📊 测试统计:');
        console.log(`   总测试数: ${stats.total}`);
        console.log(`   通过测试: ${stats.passed}`);
        console.log(`   失败测试: ${stats.failed}`);
        console.log(`   成功率: ${stats.successRate}`);

        // 检查是否所有测试都通过
        const allTestsPassed = parseInt(stats.failed) === 0 && parseInt(stats.passed) > 0;
        
        if (allTestsPassed) {
            console.log('\n🎉 所有流式上传下载测试都通过了！');
        } else {
            console.log('\n⚠️  部分测试失败，请检查测试结果');
        }

        // 保持浏览器打开一段时间以便查看结果
        console.log('\n🔍 浏览器将保持打开状态30秒以便查看详细结果...');
        await new Promise(resolve => setTimeout(resolve, 30000));

    } catch (error) {
        console.error(`💥 测试执行失败: ${error.message}`);
        console.error(error.stack);
    } finally {
        if (browser) {
            await browser.close();
            console.log('🔚 浏览器已关闭');
        }
    }
}

// 检查是否安装了 puppeteer
async function checkPuppeteer() {
    try {
        require('puppeteer');
        return true;
    } catch (error) {
        console.error('❌ 未找到 puppeteer 依赖');
        console.log('请运行以下命令安装 puppeteer:');
        console.log('npm install puppeteer');
        return false;
    }
}

// 主函数
async function main() {
    console.log('🧪 流式上传下载自动化测试');
    console.log('================================\n');

    if (!(await checkPuppeteer())) {
        process.exit(1);
    }

    await runStreamTests();
}

// 运行测试
if (require.main === module) {
    main().catch(error => {
        console.error('💥 测试脚本执行失败:', error);
        process.exit(1);
    });
}

module.exports = { runStreamTests };
