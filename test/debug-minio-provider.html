<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>调试MinIO提供者</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 调试MinIO提供者</h1>
        
        <div>
            <button onclick="testProviderCreation()">1. 测试提供者创建</button>
            <button onclick="testProviderInitialization()">2. 测试提供者初始化</button>
            <button onclick="testConnection()">3. 测试连接</button>
            <button onclick="testCRUD()">4. 测试CRUD操作</button>
            <button onclick="testBatch()">5. 测试批量操作</button>
            <button onclick="clearResults()">清除结果</button>
        </div>

        <div id="result" class="result info" style="display: none;">
            等待测试结果...
        </div>
    </div>

    <script>
        let minioProvider = null;

        function showResult(message, type = 'info') {
            const resultDiv = document.getElementById('result');
            resultDiv.style.display = 'block';
            resultDiv.className = `result ${type}`;
            resultDiv.textContent = message;
        }

        function clearResults() {
            document.getElementById('result').style.display = 'none';
        }

        // 从主测试页面复制MinIO提供者创建函数
        function createMinioProvider() {
            return {
                name: 'MinIO存储提供者',
                type: 'minio',
                isInitialized: false,
                _config: null,

                async initialize(config) {
                    this._config = config;
                    
                    // 验证配置
                    if (!config.endpoint || !config.accessKey || !config.secretKey || !config.bucketName) {
                        throw new Error('MinIO配置不完整');
                    }

                    console.log('正在初始化MinIO连接:', {
                        endpoint: config.endpoint,
                        bucket: config.bucketName,
                        region: config.region || 'us-east-1'
                    });

                    // 测试连接
                    try {
                        await this.testConnection();
                        this.isInitialized = true;
                        console.log('MinIO初始化成功');
                    } catch (error) {
                        console.error('MinIO初始化失败:', error);
                        throw error;
                    }
                },

                async dispose() {
                    this.isInitialized = false;
                    this._config = null;
                    return Promise.resolve();
                },

                async testConnection() {
                    try {
                        // 尝试列出bucket来测试连接
                        const url = `${this._config.endpoint}/${this._config.bucketName}?list-type=2&max-keys=1`;
                        const headers = await this.createAuthHeaders('GET', `/${this._config.bucketName}`, {
                            'list-type': '2',
                            'max-keys': '1'
                        });

                        const response = await fetch(url, {
                            method: 'GET',
                            headers: headers
                        });

                        if (response.ok) {
                            console.log('MinIO连接测试成功');
                            return true;
                        } else {
                            const errorText = await response.text();
                            console.error('MinIO连接测试失败:', response.status, errorText);
                            throw new Error(`连接失败: ${response.status} ${response.statusText}`);
                        }
                    } catch (error) {
                        console.error('MinIO连接测试异常:', error);
                        throw error;
                    }
                },

                async get(key) {
                    if (!this.isInitialized) {
                        throw new Error('存储提供者未初始化');
                    }
                    
                    try {
                        const url = `${this._config.endpoint}/${this._config.bucketName}/${key}`;
                        const headers = await this.createAuthHeaders('GET', `/${this._config.bucketName}/${key}`);

                        const response = await fetch(url, {
                            method: 'GET',
                            headers: headers
                        });

                        if (response.ok) {
                            const data = await response.text();
                            console.log(`MinIO GET成功: ${key}`);
                            return {
                                success: true,
                                data: data,
                                metadata: { 
                                    source: 'minio',
                                    contentType: response.headers.get('content-type'),
                                    contentLength: response.headers.get('content-length')
                                }
                            };
                        } else if (response.status === 404) {
                            return {
                                success: false,
                                error: new Error('对象不存在')
                            };
                        } else {
                            const errorText = await response.text();
                            throw new Error(`获取对象失败: ${response.status} ${errorText}`);
                        }
                    } catch (error) {
                        console.error(`MinIO GET失败: ${key}`, error);
                        return {
                            success: false,
                            error: error
                        };
                    }
                },

                async put(key, data) {
                    if (!this.isInitialized) {
                        throw new Error('存储提供者未初始化');
                    }
                    
                    try {
                        const url = `${this._config.endpoint}/${this._config.bucketName}/${key}`;
                        const headers = await this.createAuthHeaders('PUT', `/${this._config.bucketName}/${key}`, {}, data);

                        const response = await fetch(url, {
                            method: 'PUT',
                            headers: headers,
                            body: data
                        });

                        if (response.ok) {
                            const etag = response.headers.get('etag');
                            console.log(`MinIO PUT成功: ${key}`);
                            return {
                                success: true,
                                data: undefined,
                                metadata: { 
                                    etag: etag,
                                    source: 'minio'
                                }
                            };
                        } else {
                            const errorText = await response.text();
                            throw new Error(`上传对象失败: ${response.status} ${errorText}`);
                        }
                    } catch (error) {
                        console.error(`MinIO PUT失败: ${key}`, error);
                        return {
                            success: false,
                            error: error
                        };
                    }
                },

                async delete(key) {
                    if (!this.isInitialized) {
                        throw new Error('存储提供者未初始化');
                    }
                    
                    try {
                        const url = `${this._config.endpoint}/${this._config.bucketName}/${key}`;
                        const headers = await this.createAuthHeaders('DELETE', `/${this._config.bucketName}/${key}`);

                        const response = await fetch(url, {
                            method: 'DELETE',
                            headers: headers
                        });

                        if (response.ok || response.status === 404) {
                            console.log(`MinIO DELETE成功: ${key}`);
                            return {
                                success: true,
                                data: undefined
                            };
                        } else {
                            const errorText = await response.text();
                            throw new Error(`删除对象失败: ${response.status} ${errorText}`);
                        }
                    } catch (error) {
                        console.error(`MinIO DELETE失败: ${key}`, error);
                        return {
                            success: false,
                            error: error
                        };
                    }
                },

                // AWS S3签名认证方法
                async createAuthHeaders(method, path, queryParams = {}, body = '') {
                    const now = new Date();
                    const dateStamp = now.toISOString().slice(0, 10).replace(/-/g, '');
                    const timeStamp = now.toISOString().slice(0, 19).replace(/[-:]/g, '') + 'Z';
                    
                    const region = this._config.region || 'us-east-1';
                    const service = 's3';
                    
                    // 创建规范请求
                    const canonicalUri = path;
                    const canonicalQueryString = Object.keys(queryParams)
                        .sort()
                        .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(queryParams[key])}`)
                        .join('&');
                    
                    const headers = {
                        'host': new URL(this._config.endpoint).host,
                        'x-amz-date': timeStamp,
                        'x-amz-content-sha256': await this.sha256(body)
                    };
                    
                    const canonicalHeaders = Object.keys(headers)
                        .sort()
                        .map(key => `${key}:${headers[key]}\n`)
                        .join('');
                    
                    const signedHeaders = Object.keys(headers).sort().join(';');
                    
                    const canonicalRequest = [
                        method,
                        canonicalUri,
                        canonicalQueryString,
                        canonicalHeaders,
                        signedHeaders,
                        headers['x-amz-content-sha256']
                    ].join('\n');
                    
                    // 创建签名字符串
                    const algorithm = 'AWS4-HMAC-SHA256';
                    const credentialScope = `${dateStamp}/${region}/${service}/aws4_request`;
                    const stringToSign = [
                        algorithm,
                        timeStamp,
                        credentialScope,
                        await this.sha256(canonicalRequest)
                    ].join('\n');
                    
                    // 计算签名
                    const signingKey = await this.getSignatureKey(this._config.secretKey, dateStamp, region, service);
                    const signature = await this.hmacSha256(signingKey, stringToSign);
                    
                    // 创建授权头
                    const authorization = `${algorithm} Credential=${this._config.accessKey}/${credentialScope}, SignedHeaders=${signedHeaders}, Signature=${signature}`;
                    
                    return {
                        'Authorization': authorization,
                        'X-Amz-Date': timeStamp,
                        'X-Amz-Content-Sha256': headers['x-amz-content-sha256']
                    };
                },

                async sha256(data) {
                    const encoder = new TextEncoder();
                    const dataBuffer = typeof data === 'string' ? encoder.encode(data) : data;
                    const hashBuffer = await crypto.subtle.digest('SHA-256', dataBuffer);
                    return Array.from(new Uint8Array(hashBuffer))
                        .map(b => b.toString(16).padStart(2, '0'))
                        .join('');
                },

                async hmacSha256(key, data) {
                    const encoder = new TextEncoder();
                    const keyBuffer = typeof key === 'string' ? encoder.encode(key) : key;
                    const dataBuffer = typeof data === 'string' ? encoder.encode(data) : data;
                    
                    const cryptoKey = await crypto.subtle.importKey(
                        'raw',
                        keyBuffer,
                        { name: 'HMAC', hash: 'SHA-256' },
                        false,
                        ['sign']
                    );
                    
                    const signature = await crypto.subtle.sign('HMAC', cryptoKey, dataBuffer);
                    return Array.from(new Uint8Array(signature))
                        .map(b => b.toString(16).padStart(2, '0'))
                        .join('');
                },

                async getSignatureKey(key, dateStamp, regionName, serviceName) {
                    const kDate = await this.hmacSha256('AWS4' + key, dateStamp);
                    const kRegion = await this.hmacSha256(this.hexToBytes(kDate), regionName);
                    const kService = await this.hmacSha256(this.hexToBytes(kRegion), serviceName);
                    const kSigning = await this.hmacSha256(this.hexToBytes(kService), 'aws4_request');
                    return this.hexToBytes(kSigning);
                },

                hexToBytes(hex) {
                    const bytes = new Uint8Array(hex.length / 2);
                    for (let i = 0; i < hex.length; i += 2) {
                        bytes[i / 2] = parseInt(hex.substr(i, 2), 16);
                    }
                    return bytes;
                }
            };
        }

        // 测试函数
        async function testProviderCreation() {
            try {
                showResult('正在测试提供者创建...', 'info');
                
                minioProvider = createMinioProvider();
                
                showResult(`✅ 提供者创建成功！
名称: ${minioProvider.name}
类型: ${minioProvider.type}
初始化状态: ${minioProvider.isInitialized}`, 'success');
            } catch (error) {
                showResult(`❌ 提供者创建失败！
错误: ${error.message}
堆栈: ${error.stack}`, 'error');
            }
        }

        async function testProviderInitialization() {
            if (!minioProvider) {
                showResult('请先创建提供者', 'error');
                return;
            }

            try {
                showResult('正在测试提供者初始化...', 'info');
                
                const config = {
                    endpoint: 'http://127.0.0.1:9000',
                    accessKey: 'FsYFOP9cOOYDyfM9odzX',
                    secretKey: 'AcQaaZTcoOh6GKMuLKAcdIo4W0qcWQ8VWKkqQvLl',
                    bucketName: 'eversnip',
                    region: 'us-east-1'
                };

                await minioProvider.initialize(config);
                
                showResult(`✅ 提供者初始化成功！
配置: ${JSON.stringify(config, null, 2)}
初始化状态: ${minioProvider.isInitialized}`, 'success');
            } catch (error) {
                showResult(`❌ 提供者初始化失败！
错误: ${error.message}
堆栈: ${error.stack}`, 'error');
            }
        }

        async function testConnection() {
            if (!minioProvider || !minioProvider.isInitialized) {
                showResult('请先初始化提供者', 'error');
                return;
            }

            try {
                showResult('正在测试连接...', 'info');
                
                const isConnected = await minioProvider.testConnection();
                
                showResult(`✅ 连接测试成功！
连接状态: ${isConnected}`, 'success');
            } catch (error) {
                showResult(`❌ 连接测试失败！
错误: ${error.message}
堆栈: ${error.stack}`, 'error');
            }
        }

        async function testCRUD() {
            if (!minioProvider || !minioProvider.isInitialized) {
                showResult('请先初始化提供者', 'error');
                return;
            }

            try {
                showResult('正在测试CRUD操作...', 'info');
                
                const testKey = 'test/debug-test.json';
                const testData = {
                    message: 'Debug test data',
                    timestamp: new Date().toISOString(),
                    number: 42
                };

                // 测试上传
                const putResult = await minioProvider.put(testKey, JSON.stringify(testData));
                if (!putResult.success) {
                    throw new Error(`上传失败: ${putResult.error?.message}`);
                }

                // 测试获取
                const getResult = await minioProvider.get(testKey);
                if (!getResult.success) {
                    throw new Error(`获取失败: ${getResult.error?.message}`);
                }

                const retrievedData = JSON.parse(getResult.data);
                const dataMatches = retrievedData.message === testData.message;

                // 测试删除
                const deleteResult = await minioProvider.delete(testKey);
                if (!deleteResult.success) {
                    throw new Error(`删除失败: ${deleteResult.error?.message}`);
                }

                showResult(`✅ CRUD操作测试成功！
上传结果: ${putResult.success ? '成功' : '失败'}
获取结果: ${getResult.success ? '成功' : '失败'}
数据匹配: ${dataMatches}
删除结果: ${deleteResult.success ? '成功' : '失败'}
原始数据: ${JSON.stringify(testData, null, 2)}
获取数据: ${JSON.stringify(retrievedData, null, 2)}`, 'success');
            } catch (error) {
                showResult(`❌ CRUD操作测试失败！
错误: ${error.message}
堆栈: ${error.stack}`, 'error');
            }
        }

        async function testBatch() {
            if (!minioProvider || !minioProvider.isInitialized) {
                showResult('请先初始化提供者', 'error');
                return;
            }

            try {
                showResult('正在测试批量操作...', 'info');
                
                const testItems = {
                    'test/debug-batch1.json': { id: 1, name: 'Debug Item 1' },
                    'test/debug-batch2.json': { id: 2, name: 'Debug Item 2' },
                    'test/debug-batch3.json': { id: 3, name: 'Debug Item 3' }
                };

                // 批量上传
                for (const [key, data] of Object.entries(testItems)) {
                    const result = await minioProvider.put(key, JSON.stringify(data));
                    if (!result.success) {
                        throw new Error(`批量上传失败: ${key}`);
                    }
                }

                // 批量获取
                const results = {};
                for (const key of Object.keys(testItems)) {
                    const result = await minioProvider.get(key);
                    if (result.success) {
                        results[key] = JSON.parse(result.data);
                    }
                }

                // 批量删除
                for (const key of Object.keys(testItems)) {
                    await minioProvider.delete(key);
                }

                const retrievedCount = Object.keys(results).length;
                const expectedCount = Object.keys(testItems).length;

                showResult(`✅ 批量操作测试成功！
上传文件数: ${expectedCount}
获取文件数: ${retrievedCount}
批量结果: ${JSON.stringify(results, null, 2)}`, 'success');
            } catch (error) {
                showResult(`❌ 批量操作测试失败！
错误: ${error.message}
堆栈: ${error.stack}`, 'error');
            }
        }
    </script>
</body>
</html>
