#!/usr/bin/env node

/**
 * 简化版流式上传下载测试脚本
 * 直接测试存储提供者的流式功能
 */

const fs = require('fs');
const path = require('path');

// 模拟浏览器环境的全局对象
global.fetch = require('node-fetch');
global.crypto = require('crypto').webcrypto;
global.TextEncoder = require('util').TextEncoder;
global.TextDecoder = require('util').TextDecoder;
global.ReadableStream = require('stream/web').ReadableStream;

// 模拟 DOM 解析器
global.DOMParser = class DOMParser {
    parseFromString(xmlString, mimeType) {
        // 简单的 XML 解析模拟
        return {
            getElementsByTagName: (tagName) => {
                const regex = new RegExp(`<${tagName}[^>]*>([^<]*)</${tagName}>`, 'g');
                const matches = [];
                let match;
                while ((match = regex.exec(xmlString)) !== null) {
                    matches.push({ textContent: match[1] });
                }
                return matches;
            }
        };
    }
};

// 创建MinIO存储提供者
function createMinioProvider() {
    return {
        name: 'MinIO存储提供者',
        type: 'minio',
        isInitialized: false,
        _config: null,

        async initialize(config) {
            this._config = config;
            console.log('🔧 初始化MinIO存储提供者...');
            console.log(`   Endpoint: ${config.endpoint}`);
            console.log(`   Bucket: ${config.bucketName}`);
            this.isInitialized = true;
            return Promise.resolve();
        },

        async dispose() {
            this.isInitialized = false;
            this._config = null;
            return Promise.resolve();
        },

        async put(key, data) {
            console.log(`📤 PUT: ${key} (${data.length} 字节)`);
            // 模拟上传延迟
            await new Promise(resolve => setTimeout(resolve, 100));
            return {
                success: true,
                data: undefined,
                metadata: { etag: `minio-etag-${Date.now()}` }
            };
        },

        async get(key) {
            console.log(`📥 GET: ${key}`);
            // 模拟下载延迟
            await new Promise(resolve => setTimeout(resolve, 50));
            return {
                success: true,
                data: `模拟数据 for ${key}`,
                metadata: { source: 'minio' }
            };
        },

        async delete(key) {
            console.log(`🗑️  DELETE: ${key}`);
            return {
                success: true,
                data: undefined
            };
        },

        async getMetadata(key) {
            console.log(`ℹ️  METADATA: ${key}`);
            return {
                success: true,
                data: {
                    size: 1024,
                    lastModified: new Date(),
                    etag: `minio-etag-${key}`,
                    contentType: 'application/json'
                }
            };
        },

        // 流式分块上传
        async putStream(key, stream, options = {}) {
            console.log(`🌊 开始流式上传: ${key}`);
            const chunkSize = options.chunkSize || 5 * 1024 * 1024;
            const reader = stream.getReader();
            const chunks = [];
            let totalSize = 0;

            // 读取所有数据块
            while (true) {
                const { done, value } = await reader.read();
                if (done) break;

                chunks.push(value);
                totalSize += value.length;
                console.log(`   读取数据块: ${value.length} 字节, 总计: ${totalSize} 字节`);
            }

            // 模拟上传过程
            if (totalSize <= chunkSize) {
                console.log('   使用单次上传');
                await new Promise(resolve => setTimeout(resolve, 200));
            } else {
                const partCount = Math.ceil(totalSize / chunkSize);
                console.log(`   使用分块上传，共 ${partCount} 个分块`);
                for (let i = 0; i < partCount; i++) {
                    console.log(`   上传分块 ${i + 1}/${partCount}`);
                    await new Promise(resolve => setTimeout(resolve, 100));
                }
            }

            return {
                success: true,
                data: undefined,
                metadata: {
                    etag: `minio-stream-etag-${Date.now()}`,
                    totalSize: totalSize,
                    chunks: Math.ceil(totalSize / chunkSize),
                    uploadType: totalSize > chunkSize ? 'multipart' : 'single'
                }
            };
        },

        // 流式分块下载
        async getStream(key, options = {}) {
            console.log(`🌊 开始流式下载: ${key}`);
            const chunkSize = options.chunkSize || 1024 * 1024;
            
            // 模拟文件数据 (减少到10KB以加快测试)
            const fileSize = 10 * 1024; // 10KB
            const testData = new Uint8Array(fileSize);
            for (let i = 0; i < fileSize; i++) {
                testData[i] = i % 256;
            }

            // 创建可读流
            const stream = new ReadableStream({
                async start(controller) {
                    let currentOffset = 0;
                    let chunkNumber = 1;

                    while (currentOffset < fileSize) {
                        const endOffset = Math.min(currentOffset + chunkSize, fileSize);
                        const chunkData = testData.slice(currentOffset, endOffset);

                        console.log(`   下载分块 ${chunkNumber}: ${currentOffset}-${endOffset - 1} (${chunkData.length} 字节)`);

                        controller.enqueue(chunkData);
                        currentOffset = endOffset;
                        chunkNumber++;

                        // 模拟下载延迟
                        await new Promise(resolve => setTimeout(resolve, 50));
                    }

                    console.log(`   流式下载完成，共 ${chunkNumber - 1} 个分块`);
                    controller.close();
                }
            });

            return stream;
        }
    };
}

// 测试流式上传
async function testStreamUpload(provider) {
    console.log('\n📤 测试流式上传...');
    
    const testKey = 'test/stream-upload-test.json';
    const testData = {
        message: '流式上传测试数据',
        timestamp: new Date().toISOString(),
        chunks: Array.from({length: 100}, (_, i) => `数据块${i}: ${Math.random()}`)
    };

    // 创建测试数据流
    const dataString = JSON.stringify(testData, null, 2);
    const encoder = new TextEncoder();
    const dataBytes = encoder.encode(dataString);

    console.log(`   创建测试数据: ${dataBytes.length} 字节`);

    // 创建可读流
    const stream = new ReadableStream({
        start(controller) {
            const chunkSize = 1024; // 1KB分块
            let offset = 0;

            const pushChunk = () => {
                if (offset >= dataBytes.length) {
                    controller.close();
                    return;
                }

                const chunk = dataBytes.slice(offset, offset + chunkSize);
                controller.enqueue(chunk);
                offset += chunkSize;

                // 异步推送下一个分块
                setTimeout(pushChunk, 10);
            };

            pushChunk();
        }
    });

    const startTime = Date.now();
    const result = await provider.putStream(testKey, stream, {
        chunkSize: 2 * 1024 // 2KB分块上传
    });
    const endTime = Date.now();

    if (result.success) {
        console.log(`✅ 流式上传测试成功！`);
        console.log(`   数据大小: ${dataBytes.length} 字节`);
        console.log(`   上传耗时: ${endTime - startTime}ms`);
        console.log(`   上传类型: ${result.metadata?.uploadType || '单次上传'}`);
        console.log(`   分块数量: ${result.metadata?.chunks || 1}`);
        
        // 清理测试文件
        await provider.delete(testKey);
        return true;
    } else {
        console.log(`❌ 流式上传测试失败: ${result.error?.message}`);
        return false;
    }
}

// 测试流式下载
async function testStreamDownload(provider) {
    console.log('\n📥 测试流式下载...');
    
    const testKey = 'test/stream-download-test.json';

    const startTime = Date.now();
    const stream = await provider.getStream(testKey, {
        chunkSize: 1024 // 1KB分块下载
    });

    // 读取流数据
    const reader = stream.getReader();
    const chunks = [];
    let totalBytes = 0;
    let chunkCount = 0;

    while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        chunks.push(value);
        totalBytes += value.length;
        chunkCount++;
    }

    const endTime = Date.now();

    console.log(`✅ 流式下载测试成功！`);
    console.log(`   下载大小: ${totalBytes} 字节`);
    console.log(`   下载耗时: ${endTime - startTime}ms`);
    console.log(`   分块数量: ${chunkCount}`);
    console.log(`   平均分块: ${Math.round(totalBytes / chunkCount)} 字节`);

    return true;
}

// 主测试函数
async function runTests() {
    console.log('🧪 流式上传下载测试');
    console.log('====================\n');

    // 创建存储提供者
    const provider = createMinioProvider();

    // 初始化存储
    await provider.initialize({
        endpoint: 'http://127.0.0.1:9000',
        accessKey: 'minioadmin',
        secretKey: 'minioadmin',
        bucketName: 'eversnip',
        region: 'us-east-1'
    });

    let passedTests = 0;
    let totalTests = 0;

    // 运行测试
    const tests = [
        { name: '流式上传', fn: () => testStreamUpload(provider) },
        { name: '流式下载', fn: () => testStreamDownload(provider) }
    ];

    for (const test of tests) {
        totalTests++;
        try {
            const passed = await test.fn();
            if (passed) {
                passedTests++;
            }
        } catch (error) {
            console.log(`❌ ${test.name}测试失败: ${error.message}`);
        }
    }

    // 输出测试结果
    console.log('\n📊 测试结果:');
    console.log(`   总测试数: ${totalTests}`);
    console.log(`   通过测试: ${passedTests}`);
    console.log(`   失败测试: ${totalTests - passedTests}`);
    console.log(`   成功率: ${Math.round((passedTests / totalTests) * 100)}%`);

    if (passedTests === totalTests) {
        console.log('\n🎉 所有流式测试都通过了！');
    } else {
        console.log('\n⚠️  部分测试失败');
    }
}

// 运行测试
if (require.main === module) {
    runTests().catch(error => {
        console.error('💥 测试执行失败:', error);
        process.exit(1);
    });
}

module.exports = { runTests };
