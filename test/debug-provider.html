<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>存储提供者调试</title>
    <style>
        body {
            font-family: monospace;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            background: #007bff;
            color: white;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 存储提供者调试工具</h1>
        
        <button onclick="debugMinioProvider()">调试MinIO提供者</button>
        <button onclick="debugHuaweiProvider()">调试华为云OBS提供者</button>
        
        <div id="debug-result" class="result"></div>
    </div>

    <script>
        function showResult(message) {
            document.getElementById('debug-result').textContent = message;
        }

        // 创建MinIO存储提供者 (简化版本)
        function createMinioProvider() {
            return {
                name: 'MinIO存储提供者',
                type: 'minio',
                isInitialized: false,
                _config: null,

                async initialize(config) {
                    this._config = config;
                    this.isInitialized = true;
                    return Promise.resolve();
                },

                async put(key, data) {
                    return { success: true, metadata: { etag: 'test-etag' } };
                },

                async get(key) {
                    return { success: true, data: 'test data' };
                },

                async getMetadata(key) {
                    return {
                        success: true,
                        data: {
                            size: 1024,
                            lastModified: new Date(),
                            etag: 'test-etag',
                            contentType: 'application/json'
                        }
                    };
                },

                async downloadChunk(key, startByte, endByte) {
                    const size = endByte - startByte + 1;
                    const data = new Uint8Array(size);
                    for (let i = 0; i < size; i++) {
                        data[i] = i % 256;
                    }
                    return data;
                },

                // 流式分块上传
                async putStream(key, stream, options = {}) {
                    try {
                        const reader = stream.getReader();
                        const chunks = [];
                        let totalSize = 0;

                        while (true) {
                            const { done, value } = await reader.read();
                            if (done) break;
                            chunks.push(value);
                            totalSize += value.length;
                        }

                        return {
                            success: true,
                            metadata: {
                                totalSize: totalSize,
                                chunks: chunks.length,
                                uploadType: 'stream'
                            }
                        };
                    } catch (error) {
                        return { success: false, error: error };
                    }
                },

                // 流式分块下载
                async getStream(key, options = {}) {
                    try {
                        const chunkSize = options.chunkSize || 1024;
                        const testData = new Uint8Array(5000); // 5KB测试数据
                        for (let i = 0; i < testData.length; i++) {
                            testData[i] = i % 256;
                        }

                        const self = this;
                        const stream = new ReadableStream({
                            start(controller) {
                                let currentOffset = 0;
                                let chunkNumber = 1;

                                const pushChunk = () => {
                                    if (currentOffset >= testData.length) {
                                        controller.close();
                                        return;
                                    }

                                    const endOffset = Math.min(currentOffset + chunkSize, testData.length);
                                    const chunkData = testData.slice(currentOffset, endOffset);

                                    controller.enqueue(chunkData);
                                    currentOffset = endOffset;
                                    chunkNumber++;

                                    setTimeout(pushChunk, 10);
                                };

                                pushChunk();
                            }
                        });

                        return stream;
                    } catch (error) {
                        throw error;
                    }
                }
            };
        }

        // 创建华为云OBS存储提供者 (简化版本)
        function createHuaweiObsProvider() {
            return {
                name: '华为云OBS存储提供者',
                type: 'huawei-obs',
                isInitialized: false,
                _config: null,

                async initialize(config) {
                    this._config = config;
                    this.isInitialized = true;
                    return Promise.resolve();
                },

                async put(key, data) {
                    return { success: true, metadata: { etag: 'obs-etag' } };
                },

                async get(key) {
                    return { success: true, data: 'obs test data' };
                },

                async getMetadata(key) {
                    return {
                        success: true,
                        data: {
                            size: 1024,
                            lastModified: new Date(),
                            etag: 'obs-etag',
                            contentType: 'application/json'
                        }
                    };
                },

                // 流式分块上传
                async putStream(key, stream, options = {}) {
                    try {
                        const reader = stream.getReader();
                        const chunks = [];
                        let totalSize = 0;

                        while (true) {
                            const { done, value } = await reader.read();
                            if (done) break;
                            chunks.push(value);
                            totalSize += value.length;
                        }

                        return {
                            success: true,
                            metadata: {
                                totalSize: totalSize,
                                chunks: chunks.length,
                                uploadType: 'obs-stream'
                            }
                        };
                    } catch (error) {
                        return { success: false, error: error };
                    }
                },

                // 流式分块下载
                async getStream(key, options = {}) {
                    try {
                        const chunkSize = options.chunkSize || 1024;
                        const testData = new Uint8Array(3000); // 3KB测试数据
                        for (let i = 0; i < testData.length; i++) {
                            testData[i] = (i * 2) % 256;
                        }

                        const stream = new ReadableStream({
                            start(controller) {
                                let currentOffset = 0;

                                const pushChunk = () => {
                                    if (currentOffset >= testData.length) {
                                        controller.close();
                                        return;
                                    }

                                    const endOffset = Math.min(currentOffset + chunkSize, testData.length);
                                    const chunkData = testData.slice(currentOffset, endOffset);

                                    controller.enqueue(chunkData);
                                    currentOffset = endOffset;

                                    setTimeout(pushChunk, 10);
                                };

                                pushChunk();
                            }
                        });

                        return stream;
                    } catch (error) {
                        throw error;
                    }
                }
            };
        }

        // 调试函数
        function debugProvider(provider, name) {
            let result = `=== ${name} 调试信息 ===\n\n`;
            
            result += `提供者名称: ${provider.name}\n`;
            result += `提供者类型: ${provider.type}\n`;
            result += `初始化状态: ${provider.isInitialized}\n\n`;
            
            result += `可用方法:\n`;
            const methods = Object.getOwnPropertyNames(provider).filter(prop => typeof provider[prop] === 'function');
            methods.forEach(method => {
                result += `  - ${method}()\n`;
            });
            
            result += `\n流式方法检查:\n`;
            result += `  - putStream: ${typeof provider.putStream === 'function' ? '✅ 存在' : '❌ 不存在'}\n`;
            result += `  - getStream: ${typeof provider.getStream === 'function' ? '✅ 存在' : '❌ 不存在'}\n`;
            
            if (typeof provider.putStream === 'function') {
                result += `  - putStream 函数签名: ${provider.putStream.toString().substring(0, 100)}...\n`;
            }
            
            if (typeof provider.getStream === 'function') {
                result += `  - getStream 函数签名: ${provider.getStream.toString().substring(0, 100)}...\n`;
            }
            
            return result;
        }

        window.debugMinioProvider = async function() {
            try {
                const provider = createMinioProvider();
                await provider.initialize({
                    endpoint: 'http://127.0.0.1:9000',
                    accessKey: 'minioadmin',
                    secretKey: 'minioadmin',
                    bucketName: 'eversnip',
                    region: 'us-east-1'
                });
                
                let result = debugProvider(provider, 'MinIO');
                
                // 测试流式上传
                result += `\n=== 流式上传测试 ===\n`;
                try {
                    const testData = new TextEncoder().encode('Hello Stream!');
                    const stream = new ReadableStream({
                        start(controller) {
                            controller.enqueue(testData);
                            controller.close();
                        }
                    });
                    
                    const uploadResult = await provider.putStream('test/debug.txt', stream);
                    result += `上传结果: ${uploadResult.success ? '成功' : '失败'}\n`;
                    if (uploadResult.success) {
                        result += `上传元数据: ${JSON.stringify(uploadResult.metadata, null, 2)}\n`;
                    }
                } catch (error) {
                    result += `上传错误: ${error.message}\n`;
                }
                
                // 测试流式下载
                result += `\n=== 流式下载测试 ===\n`;
                try {
                    const downloadStream = await provider.getStream('test/debug.txt');
                    const reader = downloadStream.getReader();
                    let totalBytes = 0;
                    let chunkCount = 0;
                    
                    while (true) {
                        const { done, value } = await reader.read();
                        if (done) break;
                        totalBytes += value.length;
                        chunkCount++;
                    }
                    
                    result += `下载结果: 成功\n`;
                    result += `总字节数: ${totalBytes}\n`;
                    result += `分块数量: ${chunkCount}\n`;
                } catch (error) {
                    result += `下载错误: ${error.message}\n`;
                }
                
                showResult(result);
            } catch (error) {
                showResult(`MinIO调试失败: ${error.message}`);
            }
        };

        window.debugHuaweiProvider = async function() {
            try {
                const provider = createHuaweiObsProvider();
                await provider.initialize({
                    endpoint: 'https://obs.cn-north-4.myhuaweicloud.com',
                    accessKey: 'test-key',
                    secretKey: 'test-secret',
                    bucketName: 'test-bucket',
                    region: 'cn-north-4'
                });
                
                let result = debugProvider(provider, '华为云OBS');
                
                // 测试流式上传
                result += `\n=== 流式上传测试 ===\n`;
                try {
                    const testData = new TextEncoder().encode('Hello OBS Stream!');
                    const stream = new ReadableStream({
                        start(controller) {
                            controller.enqueue(testData);
                            controller.close();
                        }
                    });
                    
                    const uploadResult = await provider.putStream('test/obs-debug.txt', stream);
                    result += `上传结果: ${uploadResult.success ? '成功' : '失败'}\n`;
                    if (uploadResult.success) {
                        result += `上传元数据: ${JSON.stringify(uploadResult.metadata, null, 2)}\n`;
                    }
                } catch (error) {
                    result += `上传错误: ${error.message}\n`;
                }
                
                // 测试流式下载
                result += `\n=== 流式下载测试 ===\n`;
                try {
                    const downloadStream = await provider.getStream('test/obs-debug.txt');
                    const reader = downloadStream.getReader();
                    let totalBytes = 0;
                    let chunkCount = 0;
                    
                    while (true) {
                        const { done, value } = await reader.read();
                        if (done) break;
                        totalBytes += value.length;
                        chunkCount++;
                    }
                    
                    result += `下载结果: 成功\n`;
                    result += `总字节数: ${totalBytes}\n`;
                    result += `分块数量: ${chunkCount}\n`;
                } catch (error) {
                    result += `下载错误: ${error.message}\n`;
                }
                
                showResult(result);
            } catch (error) {
                showResult(`华为云OBS调试失败: ${error.message}`);
            }
        };
    </script>
</body>
</html>
