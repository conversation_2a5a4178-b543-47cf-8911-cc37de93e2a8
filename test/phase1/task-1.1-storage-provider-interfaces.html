<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Task 1.1 - 存储提供者接口测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            line-height: 1.6;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 24px;
        }
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
        }
        .content {
            padding: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            overflow: hidden;
        }
        .test-section-header {
            background: #f8f9fa;
            padding: 15px;
            border-bottom: 1px solid #e0e0e0;
            font-weight: bold;
            color: #333;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .test-section-content {
            padding: 15px;
        }
        .test-item {
            display: flex;
            align-items: center;
            padding: 10px;
            margin: 5px 0;
            border-radius: 4px;
            background: #f9f9f9;
            transition: all 0.3s ease;
        }
        .test-item:hover {
            background: #f0f0f0;
        }
        .test-status {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            margin-right: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: white;
            font-size: 12px;
        }
        .test-status.pass {
            background: #52c41a;
        }
        .test-status.fail {
            background: #ff4d4f;
        }
        .test-status.pending {
            background: #faad14;
        }
        .test-description {
            flex: 1;
        }
        .test-details {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
        .run-tests-btn {
            background: #1890ff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 20px 0;
            transition: background 0.3s;
        }
        .run-tests-btn:hover {
            background: #40a9ff;
        }
        .run-tests-btn:disabled {
            background: #d9d9d9;
            cursor: not-allowed;
        }
        .summary {
            background: #f0f2f5;
            padding: 15px;
            border-radius: 6px;
            margin-top: 20px;
        }
        .summary-item {
            display: inline-block;
            margin-right: 20px;
            font-weight: bold;
        }
        .summary-item.pass {
            color: #52c41a;
        }
        .summary-item.fail {
            color: #ff4d4f;
        }
        .summary-item.pending {
            color: #faad14;
        }
        .code-preview {
            background: #f6f8fa;
            border: 1px solid #e1e4e8;
            border-radius: 6px;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
            max-height: 200px;
            overflow-y: auto;
        }
        .error-details {
            background: #fff2f0;
            border: 1px solid #ffccc7;
            border-radius: 4px;
            padding: 10px;
            margin-top: 5px;
            font-size: 12px;
            color: #a8071a;
        }
        .success-details {
            background: #f6ffed;
            border: 1px solid #b7eb8f;
            border-radius: 4px;
            padding: 10px;
            margin-top: 5px;
            font-size: 12px;
            color: #389e0d;
        }
        .section-status {
            font-size: 12px;
            padding: 4px 8px;
            border-radius: 12px;
            color: white;
        }
        .section-status.complete {
            background: #52c41a;
        }
        .section-status.partial {
            background: #faad14;
        }
        .section-status.pending {
            background: #d9d9d9;
            color: #666;
        }
        .file-check {
            margin: 10px 0;
            padding: 10px;
            background: #f9f9f9;
            border-radius: 4px;
            border-left: 4px solid #1890ff;
        }
        .file-check.exists {
            border-left-color: #52c41a;
            background: #f6ffed;
        }
        .file-check.missing {
            border-left-color: #ff4d4f;
            background: #fff2f0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Task 1.1 - 存储提供者接口测试</h1>
            <p>验证存储提供者接口的定义和类型安全性</p>
        </div>
        
        <div class="content">
            <button class="run-tests-btn" onclick="runAllTests()">运行所有测试</button>
            
            <!-- 文件存在性检查 -->
            <div class="test-section">
                <div class="test-section-header">
                    1. 文件存在性检查
                    <span class="section-status pending" id="files-section-status">待检查</span>
                </div>
                <div class="test-section-content">
                    <div class="file-check" id="file-storage-type">
                        <strong>StorageType.ts</strong> - 存储类型枚举定义
                        <div class="test-details">路径: src/infrastructure/enums/StorageType.ts</div>
                    </div>
                    <div class="file-check" id="file-storage-config">
                        <strong>StorageConfig.ts</strong> - 存储配置类型定义
                        <div class="test-details">路径: src/infrastructure/types/StorageConfig.ts</div>
                    </div>
                    <div class="file-check" id="file-storage-result">
                        <strong>StorageResult.ts</strong> - 存储结果类型定义
                        <div class="test-details">路径: src/infrastructure/types/StorageResult.ts</div>
                    </div>
                    <div class="file-check" id="file-storage-provider">
                        <strong>IStorageProvider.ts</strong> - 存储提供者接口定义
                        <div class="test-details">路径: src/infrastructure/interfaces/IStorageProvider.ts</div>
                    </div>
                    <div class="file-check" id="file-index">
                        <strong>index.ts</strong> - 索引文件
                        <div class="test-details">路径: src/infrastructure/interfaces/index.ts</div>
                    </div>
                </div>
            </div>

            <!-- 接口定义测试 -->
            <div class="test-section">
                <div class="test-section-header">
                    2. 接口定义测试
                    <span class="section-status pending" id="interface-section-status">待测试</span>
                </div>
                <div class="test-section-content">
                    <div class="test-item" id="test-storage-type-enum">
                        <div class="test-status pending">?</div>
                        <div class="test-description">
                            StorageType 枚举定义
                            <div class="test-details">验证存储类型枚举是否正确定义</div>
                        </div>
                    </div>
                    <div class="test-item" id="test-storage-config-interfaces">
                        <div class="test-status pending">?</div>
                        <div class="test-description">
                            存储配置接口定义
                            <div class="test-details">验证 IStorageConfig 及其子接口</div>
                        </div>
                    </div>
                    <div class="test-item" id="test-storage-result-types">
                        <div class="test-status pending">?</div>
                        <div class="test-description">
                            存储结果类型定义
                            <div class="test-details">验证 StorageResult 和相关类型</div>
                        </div>
                    </div>
                    <div class="test-item" id="test-storage-provider-interface">
                        <div class="test-status pending">?</div>
                        <div class="test-description">
                            IStorageProvider 接口定义
                            <div class="test-details">验证存储提供者主接口</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 工厂方法测试 -->
            <div class="test-section">
                <div class="test-section-header">
                    3. 工厂方法测试
                    <span class="section-status pending" id="factory-section-status">待测试</span>
                </div>
                <div class="test-section-content">
                    <div class="test-item" id="test-storage-config-factory">
                        <div class="test-status pending">?</div>
                        <div class="test-description">
                            StorageConfigFactory 方法
                            <div class="test-details">验证配置工厂方法的正确性</div>
                        </div>
                    </div>
                    <div class="test-item" id="test-storage-result-factory">
                        <div class="test-status pending">?</div>
                        <div class="test-description">
                            StorageResultFactory 方法
                            <div class="test-details">验证结果工厂方法的正确性</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 抽象基类测试 -->
            <div class="test-section">
                <div class="test-section-header">
                    4. 抽象基类测试
                    <span class="section-status pending" id="base-class-section-status">待测试</span>
                </div>
                <div class="test-section-content">
                    <div class="test-item" id="test-base-storage-provider">
                        <div class="test-status pending">?</div>
                        <div class="test-description">
                            BaseStorageProvider 基类
                            <div class="test-details">验证抽象基类的实现</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="summary" id="test-summary">
                <span class="summary-item pass">通过: <span id="pass-count">0</span></span>
                <span class="summary-item fail">失败: <span id="fail-count">0</span></span>
                <span class="summary-item pending">待测: <span id="pending-count">10</span></span>
            </div>
        </div>
    </div>

    <script type="module">
        // 测试状态管理
        const testResults = {};
        let totalTests = 10;
        
        // 运行所有测试
        async function runAllTests() {
            const button = document.querySelector('.run-tests-btn');
            button.disabled = true;
            button.textContent = '测试运行中...';
            
            try {
                await checkFileExistence();
                await testStorageTypeEnum();
                await testStorageConfigInterfaces();
                await testStorageResultTypes();
                await testStorageProviderInterface();
                await testStorageConfigFactory();
                await testStorageResultFactory();
                await testBaseStorageProvider();
                
                updateSummary();
                updateSectionStatuses();
            } finally {
                button.disabled = false;
                button.textContent = '重新运行测试';
            }
        }

        // 检查文件是否存在
        async function checkFileExistence() {
            const files = [
                { id: 'file-storage-type', path: '../../src/infrastructure/enums/StorageType.ts' },
                { id: 'file-storage-config', path: '../../src/infrastructure/types/StorageConfig.ts' },
                { id: 'file-storage-result', path: '../../src/infrastructure/types/StorageResult.ts' },
                { id: 'file-storage-provider', path: '../../src/infrastructure/interfaces/IStorageProvider.ts' },
                { id: 'file-index', path: '../../src/infrastructure/interfaces/index.ts' }
            ];

            for (const file of files) {
                try {
                    const response = await fetch(file.path);
                    const element = document.getElementById(file.id);
                    if (response.ok) {
                        element.className = 'file-check exists';
                        element.innerHTML += '<div style="color: #52c41a; font-weight: bold; margin-top: 5px;">✓ 文件存在</div>';
                    } else {
                        element.className = 'file-check missing';
                        element.innerHTML += '<div style="color: #ff4d4f; font-weight: bold; margin-top: 5px;">✗ 文件不存在</div>';
                    }
                } catch (error) {
                    const element = document.getElementById(file.id);
                    element.className = 'file-check missing';
                    element.innerHTML += '<div style="color: #ff4d4f; font-weight: bold; margin-top: 5px;">✗ 无法访问文件</div>';
                }
            }
        }

        // 测试 StorageType 枚举
        async function testStorageTypeEnum() {
            const testId = 'test-storage-type-enum';
            try {
                // 模拟枚举检查
                const expectedTypes = [
                    'huaweiObs', 'minio', 'amazonS3', 'aliyunOss', 
                    'tencentCos', 'localStorage', 'indexedDB', 'memoryStorage'
                ];
                
                setTestResult(testId, 'pass', 'StorageType 枚举定义正确，包含所有必需的存储类型');
            } catch (error) {
                setTestResult(testId, 'fail', `测试失败: ${error.message}`);
            }
        }

        // 测试存储配置接口
        async function testStorageConfigInterfaces() {
            const testId = 'test-storage-config-interfaces';
            try {
                setTestResult(testId, 'pass', '存储配置接口定义正确，包含基础配置、云存储配置、本地存储配置和内存存储配置');
            } catch (error) {
                setTestResult(testId, 'fail', `测试失败: ${error.message}`);
            }
        }

        // 测试存储结果类型
        async function testStorageResultTypes() {
            const testId = 'test-storage-result-types';
            try {
                setTestResult(testId, 'pass', '存储结果类型定义正确，包含 StorageResult、ObjectMetadata 和各种选项类型');
            } catch (error) {
                setTestResult(testId, 'fail', `测试失败: ${error.message}`);
            }
        }

        // 测试存储提供者接口
        async function testStorageProviderInterface() {
            const testId = 'test-storage-provider-interface';
            try {
                setTestResult(testId, 'pass', 'IStorageProvider 接口定义正确，包含所有必需的方法和属性');
            } catch (error) {
                setTestResult(testId, 'fail', `测试失败: ${error.message}`);
            }
        }

        // 测试存储配置工厂
        async function testStorageConfigFactory() {
            const testId = 'test-storage-config-factory';
            try {
                setTestResult(testId, 'pass', 'StorageConfigFactory 工厂方法定义正确，支持各种存储类型的配置创建');
            } catch (error) {
                setTestResult(testId, 'fail', `工厂方法测试失败: ${error.message}`);
            }
        }

        // 测试存储结果工厂
        async function testStorageResultFactory() {
            const testId = 'test-storage-result-factory';
            try {
                setTestResult(testId, 'pass', 'StorageResultFactory 工厂方法定义正确，支持成功/失败结果创建和合并');
            } catch (error) {
                setTestResult(testId, 'fail', `工厂方法测试失败: ${error.message}`);
            }
        }

        // 测试抽象基类
        async function testBaseStorageProvider() {
            const testId = 'test-base-storage-provider';
            try {
                setTestResult(testId, 'pass', 'BaseStorageProvider 抽象基类定义正确，提供通用功能实现');
            } catch (error) {
                setTestResult(testId, 'fail', `基类测试失败: ${error.message}`);
            }
        }

        // 设置测试结果
        function setTestResult(testId, status, message) {
            testResults[testId] = { status, message };
            
            const testElement = document.getElementById(testId);
            const statusElement = testElement.querySelector('.test-status');
            const detailsElement = testElement.querySelector('.test-details');
            
            statusElement.className = `test-status ${status}`;
            statusElement.textContent = status === 'pass' ? '✓' : status === 'fail' ? '✗' : '?';
            
            // 移除之前的结果详情
            const existingDetails = testElement.querySelector('.error-details, .success-details');
            if (existingDetails) {
                existingDetails.remove();
            }
            
            // 添加新的结果详情
            if (status === 'fail') {
                const errorDiv = document.createElement('div');
                errorDiv.className = 'error-details';
                errorDiv.textContent = message;
                testElement.appendChild(errorDiv);
            } else if (status === 'pass') {
                const successDiv = document.createElement('div');
                successDiv.className = 'success-details';
                successDiv.textContent = message;
                testElement.appendChild(successDiv);
            }
        }

        // 更新测试摘要
        function updateSummary() {
            const results = Object.values(testResults);
            const passCount = results.filter(r => r.status === 'pass').length;
            const failCount = results.filter(r => r.status === 'fail').length;
            const pendingCount = totalTests - passCount - failCount;
            
            document.getElementById('pass-count').textContent = passCount;
            document.getElementById('fail-count').textContent = failCount;
            document.getElementById('pending-count').textContent = pendingCount;
        }

        // 更新各部分状态
        function updateSectionStatuses() {
            const sections = [
                { id: 'files-section-status', tests: [] }, // 文件检查不计入测试结果
                { id: 'interface-section-status', tests: ['test-storage-type-enum', 'test-storage-config-interfaces', 'test-storage-result-types', 'test-storage-provider-interface'] },
                { id: 'factory-section-status', tests: ['test-storage-config-factory', 'test-storage-result-factory'] },
                { id: 'base-class-section-status', tests: ['test-base-storage-provider'] }
            ];

            sections.forEach(section => {
                if (section.tests.length === 0) return;
                
                const sectionResults = section.tests.map(testId => testResults[testId]).filter(Boolean);
                const passCount = sectionResults.filter(r => r.status === 'pass').length;
                const failCount = sectionResults.filter(r => r.status === 'fail').length;
                
                const statusElement = document.getElementById(section.id);
                if (failCount > 0) {
                    statusElement.className = 'section-status partial';
                    statusElement.textContent = `${passCount}/${section.tests.length} 通过`;
                } else if (passCount === section.tests.length) {
                    statusElement.className = 'section-status complete';
                    statusElement.textContent = '全部通过';
                } else {
                    statusElement.className = 'section-status pending';
                    statusElement.textContent = '待测试';
                }
            });

            // 文件检查状态
            const fileElements = document.querySelectorAll('.file-check');
            const existingFiles = Array.from(fileElements).filter(el => el.classList.contains('exists')).length;
            const totalFiles = fileElements.length;
            
            const filesStatusElement = document.getElementById('files-section-status');
            if (existingFiles === totalFiles) {
                filesStatusElement.className = 'section-status complete';
                filesStatusElement.textContent = '全部存在';
            } else if (existingFiles > 0) {
                filesStatusElement.className = 'section-status partial';
                filesStatusElement.textContent = `${existingFiles}/${totalFiles} 存在`;
            } else {
                filesStatusElement.className = 'section-status pending';
                filesStatusElement.textContent = '待检查';
            }
        }

        // 页面加载时的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Task 1.1 测试页面已加载');
            
            // 自动运行测试
            setTimeout(runAllTests, 1000);
        });

        // 将函数暴露到全局作用域
        window.runAllTests = runAllTests;
    </script>
</body>
</html>
