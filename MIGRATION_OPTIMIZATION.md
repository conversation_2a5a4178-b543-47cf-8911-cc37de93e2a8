# 存储迁移优化方案

## 问题分析

在迁移大视频文件时，原有的迁移代码存在以下问题：

1. **内存溢出**：大视频文件被完整加载到内存中，导致页面崩溃
2. **缺乏内存监控**：没有监控内存使用情况
3. **没有文件大小限制**：没有对大文件进行特殊处理
4. **缺乏超时控制**：长时间处理可能导致页面无响应
5. **没有崩溃恢复机制**：页面崩溃后无法恢复迁移进度

## 优化方案

### 1. 内存监控和管理

- **内存使用检查**：在处理每个文件前检查内存使用情况
- **内存阈值控制**：设置80%内存使用阈值，超过时进行垃圾回收
- **强制垃圾回收**：通过多种方式触发垃圾回收
- **内存释放等待**：等待内存释放后再继续处理

```javascript
_checkMemoryUsage() {
  if (performance && performance.memory) {
    const memory = performance.memory;
    const usedPercent = memory.usedJSHeapSize / memory.jsHeapSizeLimit;
    
    return {
      used: memory.usedJSHeapSize,
      total: memory.jsHeapSizeLimit,
      usedPercent: usedPercent,
      isOverThreshold: usedPercent > this.memoryThreshold
    };
  }
  return { used: 0, total: 0, usedPercent: 0, isOverThreshold: false };
}
```

### 2. 大文件特殊处理

- **文件大小检查**：识别超过100MB的大文件
- **大文件跳过机制**：内存不足时跳过大文件处理
- **超时时间调整**：大文件使用更长的超时时间
- **重试次数调整**：大文件减少重试次数以避免重复失败

```javascript
_isLargeFile(object) {
  const size = object.Size || object.size || 0;
  return size > this.maxFileSize; // 100MB
}
```

### 3. 超时控制机制

- **操作超时**：为每个文件操作设置5分钟超时
- **大文件超时**：大文件使用更长的超时时间（3倍）
- **超时包装器**：使用Promise包装器实现超时控制

```javascript
async _withTimeout(operation, timeout = this.operationTimeout) {
  return new Promise(async (resolve, reject) => {
    const timeoutId = setTimeout(() => {
      reject(new Error(`操作超时（${timeout}ms）`));
    }, timeout);
    
    try {
      const result = await operation();
      clearTimeout(timeoutId);
      resolve(result);
    } catch (error) {
      clearTimeout(timeoutId);
      reject(error);
    }
  });
}
```

### 4. 重试机制

- **指数退避**：重试延迟逐次翻倍
- **最大重试次数**：默认3次重试
- **大文件优化**：大文件减少重试次数
- **错误分类**：区分不同类型的错误

```javascript
async _withRetry(operation, maxRetries = this.maxRetries, delay = 1000) {
  let lastError;
  
  for (let i = 0; i <= maxRetries; i++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error;
      
      if (i < maxRetries) {
        console.warn(`操作失败，${delay}ms后进行第${i + 1}次重试:`, error.message);
        await new Promise(resolve => setTimeout(resolve, delay));
        delay *= 2; // 指数退避
      }
    }
  }
  
  throw lastError;
}
```

### 5. 崩溃恢复机制

- **进度持久化**：将迁移进度保存到localStorage
- **心跳机制**：每5秒更新一次心跳时间戳
- **崩溃检测**：启动时检查是否有未完成的迁移
- **恢复界面**：提供用户友好的恢复选项

```javascript
_saveProgress(progressData) {
  try {
    const data = {
      migrationId: this.migrationId,
      sourceProvider: this.sourceProvider,
      destinationProvider: this.destinationProvider,
      totalItems: this.totalItems,
      processedItems: this.processedItems,
      errors: this.errors,
      timestamp: Date.now(),
      ...progressData
    };
    
    localStorage.setItem(this.progressPersistenceKey, JSON.stringify(data));
  } catch (error) {
    console.warn('保存迁移进度失败:', error);
  }
}
```

### 6. 心跳机制

- **定期更新**：每5秒更新一次心跳
- **状态监控**：监控迁移进程是否正常运行
- **自动清理**：迁移完成后自动清理心跳

```javascript
_startHeartbeat() {
  this._stopHeartbeat(); // 确保没有重复的心跳
  
  this.heartbeatInterval = setInterval(() => {
    this.lastHeartbeat = Date.now();
    this._saveProgress({ heartbeat: this.lastHeartbeat });
  }, 5000); // 每5秒一次心跳
}
```

## 用户界面优化

### 1. 恢复迁移模态框

当检测到未完成的迁移时，显示恢复选项：

- 显示迁移详情（源、目标、进度、时间）
- 提供恢复或放弃选项
- 显示崩溃错误信息（如果有）

### 2. 进度显示优化

- 显示大文件处理状态
- 显示内存使用情况
- 显示当前处理的文件信息
- 显示重试和超时信息

## 配置参数

```javascript
// 性能优化配置
this.maxFileSize = 100 * 1024 * 1024; // 100MB 大文件阈值
this.chunkSize = 10 * 1024 * 1024; // 10MB 分块大小（预留）
this.memoryThreshold = 0.8; // 80% 内存使用阈值
this.operationTimeout = 5 * 60 * 1000; // 5分钟 操作超时
this.maxRetries = 3; // 最大重试次数

// 崩溃恢复配置
this.progressPersistenceKey = 'migration_progress'; // 进度存储键
this.heartbeatInterval = 5000; // 5秒心跳间隔
```

## 使用方法

### 1. 正常迁移

迁移过程会自动应用所有优化：

```javascript
const result = await storageMigrationService.startMigration(
  sourceProvider,
  destinationProvider,
  (progress) => {
    console.log(`进度: ${progress.progress}% - ${progress.message}`);
  }
);
```

### 2. 恢复迁移

检查并恢复未完成的迁移：

```javascript
// 检查未完成的迁移
const unfinished = storageMigrationService.checkUnfinishedMigration();

if (unfinished) {
  // 恢复迁移
  const result = await storageMigrationService.resumeMigration(
    unfinished,
    progressCallback
  );
}
```

## 效果

通过这些优化，迁移过程现在能够：

1. **处理大文件**：避免内存溢出，安全处理大视频文件
2. **自动恢复**：页面崩溃后能够从断点继续
3. **监控状态**：实时监控内存和进度状态
4. **错误处理**：更好的错误处理和重试机制
5. **用户体验**：提供清晰的进度反馈和恢复选项

这些优化大大提高了迁移的稳定性和可靠性，特别是在处理大量数据或大文件时。
