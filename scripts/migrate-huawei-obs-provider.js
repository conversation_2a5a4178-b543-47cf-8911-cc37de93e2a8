/**
 * 华为云OBS提供者迁移脚本
 */
const fs = require('fs');
const path = require('path');

const filesToUpdate = [
  'src/services/storage/StorageManager.js',
  'src/services/CloudStorageService.js',
  'src/services/OssStorageService.js'
];

function updateImports(filePath) {
  if (!fs.existsSync(filePath)) {
    console.log(`文件不存在: ${filePath}`);
    return;
  }

  let content = fs.readFileSync(filePath, 'utf8');

  // 更新import语句
  content = content.replace(
    /import.*HuaweiObsProvider.*from.*['"].*\/storage\/HuaweiObsProvider.*['"];?/g,
    "import { HuaweiObsProvider } from '../infrastructure/providers/HuaweiObsProvider';"
  );

  // 更新require语句
  content = content.replace(
    /const.*HuaweiObsProvider.*=.*require\(['"].*\/storage\/HuaweiObsProvider.*['"]\);?/g,
    "const { HuaweiObsProvider } = require('../infrastructure/providers/HuaweiObsProvider');"
  );

  fs.writeFileSync(filePath, content, 'utf8');
  console.log(`已更新: ${filePath}`);
}

function createBackup(filePath) {
  if (fs.existsSync(filePath)) {
    const backupPath = `${filePath}.backup.${Date.now()}`;
    fs.copyFileSync(filePath, backupPath);
    console.log(`已创建备份: ${backupPath}`);
  }
}

function validateMigration(filePath) {
  if (!fs.existsSync(filePath)) {
    return false;
  }

  const content = fs.readFileSync(filePath, 'utf8');
  
  // 检查是否还有旧的引用
  const oldImportPattern = /import.*HuaweiObsProvider.*from.*['"].*\/storage\/HuaweiObsProvider.*['"];?/g;
  const oldRequirePattern = /const.*HuaweiObsProvider.*=.*require\(['"].*\/storage\/HuaweiObsProvider.*['"]\);?/g;
  
  if (oldImportPattern.test(content) || oldRequirePattern.test(content)) {
    console.log(`⚠️  ${filePath} 仍包含旧的引用`);
    return false;
  }

  return true;
}

// 执行迁移
console.log('开始迁移华为云OBS提供者引用...');

// 创建备份
console.log('\n1. 创建文件备份...');
filesToUpdate.forEach(createBackup);

// 更新引用
console.log('\n2. 更新文件引用...');
filesToUpdate.forEach(updateImports);

// 验证迁移
console.log('\n3. 验证迁移结果...');
let allValid = true;
filesToUpdate.forEach(filePath => {
  if (!validateMigration(filePath)) {
    allValid = false;
  }
});

if (allValid) {
  console.log('\n✅ 迁移完成！所有文件已成功更新。');
} else {
  console.log('\n⚠️  迁移完成，但部分文件可能需要手动检查。');
}

console.log('\n注意事项：');
console.log('1. 请运行 npm run type-check 检查TypeScript类型');
console.log('2. 请运行测试确保功能正常');
console.log('3. 如有问题，可以从备份文件恢复');
