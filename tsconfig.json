{"compilerOptions": {"target": "ES2020", "lib": ["ES2020", "DOM", "DOM.Iterable"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "ESNext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "declaration": true, "declarationMap": true, "sourceMap": true, "outDir": "./dist/types", "baseUrl": "./src", "paths": {"@/*": ["*"], "@/infrastructure/*": ["infrastructure/*"], "@/services/*": ["services/*"], "@/components/*": ["components/*"], "@/utils/*": ["utils/*"]}}, "include": ["src/**/*.ts", "src/**/*.tsx"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.test.tsx"]}