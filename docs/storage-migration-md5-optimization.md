# 存储迁移MD5优化功能

## 功能概述

存储迁移功能现在支持通过对比文件的MD5值来智能跳过相同的文件，从而优化迁移过程，节省时间和成本。

## 主要特性

### 1. 智能文件比较
- 使用文件的ETag（MD5哈希值）来比较源文件和目标文件
- 自动跳过MD5值相同的文件
- 支持华为云OBS和MinIO等不同存储提供者的元数据获取

### 2. 详细的迁移报告
- 显示成功迁移的文件数量
- 显示跳过的相同文件数量
- 列出跳过的文件清单（最多显示10个）
- 显示迁移失败的文件详情

### 3. 用户友好的界面
- 在确认对话框中提示用户关于智能跳过功能
- 在迁移过程中显示当前正在检查或迁移的文件
- 在结果页面中展示跳过文件的详细信息

## 技术实现

### 1. 接口扩展
在`IStorageProvider`接口中添加了`getObjectMetadata`方法：
```javascript
async getObjectMetadata(key) {
  // 返回包含etag、size、lastModified等信息的对象
}
```

### 2. 存储提供者实现
- **华为云OBS**: 使用`getObjectMetadata` API获取文件元数据
- **MinIO**: 使用HEAD请求获取文件元数据

### 3. 迁移逻辑优化
在`StorageMigrationService`中添加了`_shouldSkipFile`方法：
- 获取源文件的ETag
- 检查目标文件是否存在
- 比较两个文件的ETag
- 决定是否跳过文件

## 使用流程

1. **开始迁移**: 用户选择源和目标存储提供者
2. **文件检查**: 系统逐个检查每个文件的MD5值
3. **智能跳过**: 相同的文件被自动跳过
4. **迁移执行**: 只迁移不同或不存在的文件
5. **结果展示**: 显示详细的迁移报告

## 优势

### 1. 成本节省
- 避免重复传输相同的文件
- 减少网络带宽使用
- 降低存储API调用次数

### 2. 时间效率
- 大幅减少迁移时间
- 特别适用于增量迁移场景
- 支持中断后的续传优化

### 3. 数据安全
- 基于MD5值的精确比较
- 确保只跳过完全相同的文件
- 出错时默认不跳过，确保数据完整性

## 示例场景

### 场景1: 首次完整迁移
```
源存储: 100个文件
目标存储: 空
结果: 迁移100个文件，跳过0个文件
```

### 场景2: 增量迁移
```
源存储: 100个文件
目标存储: 80个相同文件 + 5个不同文件
结果: 迁移20个文件，跳过80个文件
```

### 场景3: 重新迁移
```
源存储: 100个文件
目标存储: 100个相同文件
结果: 迁移0个文件，跳过100个文件
```

## 注意事项

1. **MD5比较**: 基于文件内容的MD5值，确保精确匹配
2. **错误处理**: 获取元数据失败时默认进行迁移，确保数据安全
3. **性能考虑**: 元数据获取比文件传输快得多，总体提升性能
4. **兼容性**: 支持不同存储提供者之间的迁移

## 配置说明

该功能默认启用，无需额外配置。用户在迁移确认对话框中会看到相关提示信息。

## 未来扩展

1. 支持基于文件大小和修改时间的快速预筛选
2. 添加用户可配置的跳过策略
3. 支持部分文件的强制重新迁移选项
4. 添加迁移历史记录和统计分析
