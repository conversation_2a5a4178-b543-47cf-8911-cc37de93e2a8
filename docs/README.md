# 拾光忆栈 使用文档

欢迎使用拾光忆栈（MemoryKeeper）- 一个专注于个人记忆和知识管理的Chrome扩展。本文档将帮助您了解如何安装、配置和使用拾光忆栈的各项功能。

## 文档目录

- [快速开始](./getting-started/README.md)
  - [安装指南](./getting-started/installation.md)
  - [初始设置](./getting-started/initial-setup.md)
  - [界面概览](./getting-started/interface-overview.md)

- [基本功能](./basic-features/README.md)
  - [保存记忆](./basic-features/saving-memories.md)
  - [浏览记忆](./basic-features/browsing-memories.md)
  - [搜索与过滤](./basic-features/search-and-filter.md)
  - [分类与标签](./basic-features/categories-and-tags.md)

- [高级功能](./advanced-features/README.md)
  - [多设备同步](./advanced-features/multi-device-sync.md)
  - [云存储配置](./advanced-features/cloud-storage.md)
  - [数据加密](./advanced-features/encryption.md)
  - [数据导入导出](./advanced-features/import-export.md)
  - [快捷键](./advanced-features/shortcuts.md)

- [设置与配置](./settings/README.md)
  - [用户设置](./settings/user-settings.md)
  - [同步设置](./settings/sync-settings.md)
  - [缓存设置](./settings/cache-settings.md)
  - [外观设置](./settings/appearance.md)

- [故障排除](./troubleshooting/README.md)
  - [常见问题](./troubleshooting/common-issues.md)
  - [同步问题](./troubleshooting/sync-issues.md)
  - [性能优化](./troubleshooting/performance.md)

- [开发者文档](./developers/README.md)
  - [架构概览](./developers/architecture.md)
  - [存储结构](./developers/storage-structure.md)
  - [API参考](./developers/api-reference.md)
  - [贡献指南](./developers/contributing.md)

- [重构设计文档](./refactoring/) 📋
  - [重构总体规划](./refactoring/00-refactoring-overview.md) - 重构目标和架构设计
  - [重构执行检查清单](./refactoring/00-refactoring-checklist.md) - 详细的执行跟踪清单
  - [接口设计文档](./refactoring/interfaces/) - 预先定义的所有接口
  - [阶段1任务文档](./refactoring/phase1/) - 基础设施层重构任务
  - [阶段2任务文档](./refactoring/phase2/) - 数据层重构任务
  - [重构执行脚本](./refactoring/scripts/) - 自动化执行工具

## 关于拾光忆栈

拾光忆栈是一个专注于个人记忆和知识管理的Chrome扩展，它允许您从网页中保存文本片段、图片和视频，并通过分类、标签和搜索功能进行组织和管理。拾光忆栈注重数据隐私和安全，支持使用私有云存储，确保您的个人数据完全由您自己控制。

### 主要特点

- **便捷保存**：从任何网页快速保存文本、图片和视频
- **智能组织**：通过分类、标签和搜索功能轻松管理记忆
- **私密安全**：支持数据加密和私有云存储
- **多设备同步**：在不同设备间无缝同步您的记忆
- **高度可定制**：根据个人偏好自定义外观和功能

## 联系与支持

如果您在使用过程中遇到任何问题，或有任何建议和反馈，请通过以下方式联系我们：

- 提交问题：[GitHub Issues](https://github.com/AIPlayZone/MemoryKeeper/issues)
- 电子邮件：<EMAIL>

## 许可证

拾光忆栈使用 MIT 许可证。详情请参阅 [LICENSE](../LICENSE) 文件。
