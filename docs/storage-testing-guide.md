# 🗄️ 存储提供者测试指南

## 📋 概述

本指南将帮助您使用真实的存储服务配置来测试基础设施层的存储功能。测试页面支持MinIO和华为云OBS两种存储类型，您可以输入真实的连接信息进行完整的功能测试。

## 🚀 快速开始

### 1. 打开测试页面
```bash
open test/infrastructure-test.html
```

### 2. 配置存储服务

#### MinIO配置示例
```
存储类型: MinIO
Endpoint: http://localhost:9000
Access Key: minioadmin
Secret Key: minioadmin
Bucket名称: test-bucket
区域: us-east-1
使用SSL: ☐ (本地测试通常不需要)
```

#### 华为云OBS配置示例
```
存储类型: 华为云OBS
Endpoint: https://obs.cn-north-4.myhuaweicloud.com
Access Key: 您的华为云Access Key
Secret Key: 您的华为云Secret Key
Bucket名称: 您的bucket名称
区域: cn-north-4
```

### 3. 测试流程
1. **选择存储类型** → 自动显示对应配置字段
2. **填写配置信息** → 输入真实的连接参数
3. **测试配置** → 验证配置是否正确
4. **初始化存储** → 创建存储提供者实例
5. **运行测试** → 执行各项存储功能测试

## 🔧 详细配置说明

### MinIO配置参数

| 参数 | 必需 | 说明 | 示例 |
|------|------|------|------|
| Endpoint | ✅ | MinIO服务器地址 | `http://localhost:9000` |
| Access Key | ✅ | MinIO访问密钥 | `minioadmin` |
| Secret Key | ✅ | MinIO秘密密钥 | `minioadmin` |
| Bucket名称 | ✅ | 存储桶名称 | `test-bucket` |
| 区域 | ❌ | 服务区域 | `us-east-1` |
| 使用SSL | ❌ | 是否启用HTTPS | 本地通常不需要 |

### 华为云OBS配置参数

| 参数 | 必需 | 说明 | 示例 |
|------|------|------|------|
| Endpoint | ✅ | OBS服务终端节点 | `https://obs.cn-north-4.myhuaweicloud.com` |
| Access Key | ✅ | 华为云Access Key ID | 从华为云控制台获取 |
| Secret Key | ✅ | 华为云Secret Access Key | 从华为云控制台获取 |
| Bucket名称 | ✅ | OBS存储桶名称 | 您创建的bucket名称 |
| 区域 | ❌ | OBS服务区域 | `cn-north-4` |

## 🧪 测试功能说明

### 基础存储操作测试

#### 1. 连接测试
- **功能**: 验证存储服务连接状态
- **测试内容**: 网络连通性、认证信息正确性
- **预期结果**: 显示连接成功状态

#### 2. CRUD操作测试
- **功能**: 测试创建、读取、更新、删除操作
- **测试内容**: 
  - 上传测试文件
  - 下载并验证文件内容
  - 删除测试文件
- **预期结果**: 所有操作成功，数据一致性验证通过

#### 3. 批量操作测试
- **功能**: 测试批量文件操作性能
- **测试内容**:
  - 批量上传多个文件
  - 批量下载文件
  - 批量删除文件
- **预期结果**: 批量操作成功，性能符合预期

#### 4. 元数据操作测试
- **功能**: 测试文件元数据管理
- **测试内容**:
  - 获取文件大小、修改时间
  - 获取ETag和内容类型
- **预期结果**: 元数据信息准确完整

### 高级存储功能测试

#### 1. 签名URL测试
- **功能**: 测试预签名URL生成
- **测试内容**: 生成临时访问URL
- **预期结果**: URL格式正确，包含有效期信息

#### 2. 统计信息测试
- **功能**: 测试存储使用统计
- **测试内容**: 获取对象数量、总大小等信息
- **预期结果**: 统计数据准确

#### 3. 列表操作测试
- **功能**: 测试文件列表和过滤
- **测试内容**:
  - 列出所有文件
  - 按前缀过滤文件
- **预期结果**: 列表功能正常，过滤准确

## 🔍 测试结果解读

### 成功指标
- ✅ **绿色背景**: 测试通过
- 📊 **详细信息**: 显示操作详情和性能数据
- 🎯 **统计更新**: 成功测试计数增加

### 失败指标
- ❌ **红色背景**: 测试失败
- 🚨 **错误信息**: 显示具体错误原因
- 🔧 **排查建议**: 检查配置和网络连接

### 常见错误及解决方案

#### 1. 连接超时
```
错误: 操作超时: 30000ms
解决: 检查网络连接和Endpoint地址
```

#### 2. 认证失败
```
错误: Access Denied
解决: 验证Access Key和Secret Key是否正确
```

#### 3. Bucket不存在
```
错误: NoSuchBucket
解决: 确认Bucket名称正确，或先创建Bucket
```

#### 4. 权限不足
```
错误: Forbidden
解决: 检查账户权限，确保有读写权限
```

## 🛡️ 安全注意事项

### 1. 凭证保护
- ❌ 不要在生产环境中使用测试凭证
- ❌ 不要将凭证提交到版本控制系统
- ✅ 使用临时凭证或测试专用账户
- ✅ 定期轮换访问密钥

### 2. 网络安全
- ✅ 生产环境务必使用HTTPS
- ✅ 配置适当的防火墙规则
- ✅ 限制访问来源IP

### 3. 数据安全
- ✅ 测试时使用非敏感数据
- ✅ 及时清理测试数据
- ✅ 启用存储加密

## 📊 性能基准

### MinIO (本地部署)
- **连接延迟**: < 10ms
- **小文件上传**: < 100ms
- **批量操作**: 10个文件 < 1s

### 华为云OBS
- **连接延迟**: 50-200ms (取决于地区)
- **小文件上传**: 200-500ms
- **批量操作**: 10个文件 < 3s

## 🔄 持续测试建议

### 1. 定期测试
- 每次代码更新后运行存储测试
- 定期验证生产环境连接
- 监控存储性能变化

### 2. 自动化集成
- 将存储测试集成到CI/CD流程
- 设置性能阈值告警
- 自动生成测试报告

### 3. 多环境验证
- 开发环境：使用本地MinIO
- 测试环境：使用云存储服务
- 生产环境：定期连接测试

## 📞 技术支持

如果在测试过程中遇到问题，请：

1. **检查配置**: 确认所有必需参数已正确填写
2. **查看控制台**: 浏览器开发者工具中的Console面板
3. **网络诊断**: 确认网络连接和防火墙设置
4. **文档参考**: 查阅存储服务官方文档

---

**祝您测试顺利！** 🎉
