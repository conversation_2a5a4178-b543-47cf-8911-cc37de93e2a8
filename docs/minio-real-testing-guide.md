# 🗄️ MinIO真实数据测试指南

## 📋 概述

本指南将帮助您使用真实的MinIO服务配置进行完整的存储功能测试。我们已经预配置了您提供的MinIO连接信息，可以直接进行真实的存储操作测试。

## 🔧 MinIO配置信息

```
Endpoint: http://127.0.0.1:9000
Access Key: FsYFOP9cOOYDyfM9odzX
Secret Key: AcQaaZTcoOh6GKMuLKAcdIo4W0qcWQ8VWKkqQvLl
Bucket: eversnip
Region: us-east-1
```

## 🚀 测试步骤

### 1. 基础连接测试

打开 `test/minio-connection-test.html` 进行基础连接测试：

```bash
open test/minio-connection-test.html
```

**测试项目**：
- ✅ **连接测试**: 验证MinIO服务可访问性
- ✅ **上传测试**: 创建测试文件并上传
- ✅ **下载测试**: 下载并验证文件内容
- ✅ **列表测试**: 列出bucket中的所有对象
- ✅ **删除测试**: 删除测试文件

### 2. 完整功能测试

打开 `test/infrastructure-test.html` 进行完整的基础设施层测试：

```bash
open test/infrastructure-test.html
```

**页面已预配置MinIO信息**，您只需要：

1. **确认配置**: 页面会自动填入MinIO配置信息
2. **初始化存储**: 点击"初始化存储"按钮
3. **运行测试**: 执行各项存储功能测试

## 🧪 测试功能详解

### 基础存储操作

#### 1. 连接测试
- **功能**: 通过ListObjects API验证连接
- **实现**: 使用AWS4-HMAC-SHA256签名认证
- **验证**: 返回200状态码和XML响应

#### 2. CRUD操作测试
- **上传**: 使用PUT方法上传JSON测试数据
- **下载**: 使用GET方法获取并验证数据
- **删除**: 使用DELETE方法清理测试数据
- **验证**: 数据完整性和一致性检查

#### 3. 批量操作测试
- **批量上传**: 同时上传多个测试文件
- **批量下载**: 获取多个文件并验证
- **批量删除**: 清理所有测试文件
- **性能**: 测量批量操作的执行时间

#### 4. 元数据操作测试
- **HEAD请求**: 获取对象元数据信息
- **信息验证**: 文件大小、修改时间、ETag等
- **错误处理**: 不存在对象的处理

### 高级存储功能

#### 1. 预签名URL测试
- **生成**: 创建带有时效性的访问URL
- **验证**: URL格式和签名正确性
- **访问**: 通过预签名URL直接访问对象

#### 2. 统计信息测试
- **对象计数**: 统计bucket中的对象数量
- **大小计算**: 计算总存储空间使用量
- **最后修改**: 获取最新的修改时间

#### 3. 列表操作测试
- **全部列表**: 列出bucket中的所有对象
- **前缀过滤**: 按照前缀筛选对象
- **分页支持**: 处理大量对象的分页

## 🔍 技术实现

### AWS S3兼容API

MinIO完全兼容AWS S3 API，我们使用标准的S3 API进行操作：

```javascript
// 认证签名
const headers = await createAuthHeaders(method, path, queryParams, body);

// API调用
const response = await fetch(url, {
    method: method,
    headers: headers,
    body: body
});
```

### 签名认证流程

1. **创建规范请求**: 标准化HTTP请求格式
2. **生成签名字符串**: 包含时间戳和凭证范围
3. **计算HMAC签名**: 使用AWS4-HMAC-SHA256算法
4. **构建授权头**: 包含完整的认证信息

### 错误处理机制

```javascript
try {
    const response = await fetch(url, options);
    if (response.ok) {
        // 处理成功响应
    } else {
        // 处理HTTP错误
        const errorText = await response.text();
        throw new Error(`操作失败: ${response.status} ${errorText}`);
    }
} catch (error) {
    // 处理网络错误和异常
    console.error('操作异常:', error);
}
```

## 📊 预期测试结果

### 成功指标

#### 连接测试
```
✅ MinIO连接测试成功！
状态码: 200
响应头: {
  "content-type": "application/xml",
  "server": "MinIO",
  ...
}
```

#### CRUD操作
```
✅ CRUD操作测试通过
上传结果: 成功
获取结果: 成功
数据匹配: true
删除结果: 成功
```

#### 批量操作
```
✅ 批量操作测试通过
批量上传: 成功
批量获取: 成功
获取数量: 3/3
批量删除: 成功
```

### 常见错误及解决方案

#### 1. 连接被拒绝
```
错误: Failed to fetch
解决: 确认MinIO服务正在运行 (http://127.0.0.1:9000)
```

#### 2. 认证失败
```
错误: 403 Forbidden
解决: 检查Access Key和Secret Key是否正确
```

#### 3. Bucket不存在
```
错误: 404 NoSuchBucket
解决: 确认bucket "eversnip" 已创建
```

#### 4. CORS错误
```
错误: CORS policy blocked
解决: MinIO需要配置CORS策略允许浏览器访问
```

## 🛠️ MinIO服务配置

### 启动MinIO服务

```bash
# 使用Docker启动MinIO
docker run -p 9000:9000 -p 9001:9001 \
  -e "MINIO_ROOT_USER=FsYFOP9cOOYDyfM9odzX" \
  -e "MINIO_ROOT_PASSWORD=AcQaaZTcoOh6GKMuLKAcdIo4W0qcWQ8VWKkqQvLl" \
  minio/minio server /data --console-address ":9001"
```

### 创建Bucket

```bash
# 使用mc客户端创建bucket
mc alias set local http://127.0.0.1:9000 FsYFOP9cOOYDyfM9odzX AcQaaZTcoOh6GKMuLKAcdIo4W0qcWQ8VWKkqQvLl
mc mb local/eversnip
```

### 配置CORS策略

```bash
# 允许浏览器跨域访问
mc anonymous set-json cors.json local/eversnip
```

cors.json内容：
```json
{
  "CORSRules": [
    {
      "AllowedOrigins": ["*"],
      "AllowedMethods": ["GET", "PUT", "POST", "DELETE", "HEAD"],
      "AllowedHeaders": ["*"],
      "ExposeHeaders": ["ETag", "x-amz-request-id"]
    }
  ]
}
```

## 🎯 测试建议

### 1. 测试顺序
1. 先运行基础连接测试
2. 确认所有基础操作正常
3. 再进行完整功能测试
4. 最后进行性能和压力测试

### 2. 数据安全
- 使用测试专用的bucket
- 避免在生产数据上进行测试
- 及时清理测试产生的数据

### 3. 性能监控
- 观察操作响应时间
- 监控网络请求状态
- 记录错误和异常情况

## 📞 故障排除

### 检查清单
- [ ] MinIO服务是否正在运行
- [ ] 网络连接是否正常
- [ ] 认证信息是否正确
- [ ] Bucket是否存在
- [ ] CORS策略是否配置
- [ ] 浏览器控制台是否有错误

### 调试方法
1. 打开浏览器开发者工具
2. 查看Console面板的错误信息
3. 检查Network面板的请求详情
4. 验证请求头和响应状态

---

**现在您可以使用真实的MinIO配置进行完整的存储功能测试了！** 🎉
