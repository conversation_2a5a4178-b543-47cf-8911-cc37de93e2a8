# 阶段1实施计划：基础设施层重构

## 1. 阶段概述

### 1.1 目标
建立稳定、可扩展的基础设施层，为上层提供统一、可靠的底层服务。

### 1.2 时间安排
- **预计时间**：2-3天
- **开始时间**：待定
- **里程碑**：基础设施层测试页面全部通过

### 1.3 成功标准
- [ ] 所有存储提供者通过统一接口测试
- [ ] 网络请求客户端功能完整且稳定
- [ ] 加密解密功能正常工作
- [ ] 工具函数库完整可用
- [ ] 测试覆盖率达到80%以上
- [ ] 基础设施层测试页面功能完整

## 2. 详细任务分解

### 2.1 任务1：重构存储提供者接口和实现 (8小时)

#### 2.1.1 创建统一存储接口
**文件**: `src/infrastructure/storage/interfaces/IStorageProvider.ts`

```typescript
export interface IStorageProvider {
  readonly name: string;
  readonly type: StorageType;
  readonly isInitialized: boolean;
  
  // 生命周期管理
  initialize(config: IStorageConfig): Promise<void>;
  dispose(): Promise<void>;
  
  // 连接测试
  testConnection(): Promise<boolean>;
  
  // 基础操作
  get(key: string, options?: GetOptions): Promise<StorageResult<any>>;
  put(key: string, data: any, options?: PutOptions): Promise<StorageResult<void>>;
  delete(key: string): Promise<StorageResult<void>>;
  list(prefix?: string, options?: ListOptions): Promise<StorageResult<string[]>>;
  
  // 批量操作
  getBatch(keys: string[]): Promise<StorageResult<Record<string, any>>>;
  putBatch(items: Record<string, any>): Promise<StorageResult<void>>;
  deleteBatch(keys: string[]): Promise<StorageResult<void>>;
  
  // 元数据操作
  getMetadata(key: string): Promise<StorageResult<ObjectMetadata>>;
  
  // 流式操作
  getStream(key: string): Promise<ReadableStream>;
  putStream(key: string, stream: ReadableStream): Promise<StorageResult<void>>;
  
  // URL生成
  getSignedUrl(key: string, options?: SignedUrlOptions): Promise<string>;
}
```

#### 2.1.2 定义配置和结果类型
**文件**: `src/infrastructure/storage/interfaces/IStorageConfig.ts`

```typescript
export interface IStorageConfig {
  readonly type: StorageType;
  readonly name: string;
  readonly endpoint?: string;
  readonly region?: string;
  readonly accessKey?: string;
  readonly secretKey?: string;
  readonly bucketName?: string;
  readonly timeout?: number;
  readonly retryCount?: number;
  readonly [key: string]: any;
}

export interface StorageResult<T> {
  success: boolean;
  data?: T;
  error?: Error;
  metadata?: Record<string, any>;
}

export interface ObjectMetadata {
  size: number;
  lastModified: Date;
  etag: string;
  contentType: string;
  customMetadata?: Record<string, string>;
}
```

#### 2.1.3 重构现有存储提供者
**任务清单**:
- [ ] 重构 `HuaweiObsProvider` 实现新接口
- [ ] 重构 `MinioProvider` 实现新接口
- [ ] 创建 `LocalStorageProvider` 用于本地测试
- [ ] 创建 `MemoryStorageProvider` 用于单元测试

#### 2.1.4 创建存储工厂
**文件**: `src/infrastructure/storage/factory/StorageProviderFactory.ts`

```typescript
export class StorageProviderFactory {
  private static providers = new Map<StorageType, () => IStorageProvider>();
  
  static register(type: StorageType, factory: () => IStorageProvider): void {
    this.providers.set(type, factory);
  }
  
  static create(config: IStorageConfig): IStorageProvider {
    const factory = this.providers.get(config.type);
    if (!factory) {
      throw new Error(`Unsupported storage type: ${config.type}`);
    }
    
    const provider = factory();
    return provider;
  }
  
  static getSupportedTypes(): StorageType[] {
    return Array.from(this.providers.keys());
  }
}
```

### 2.2 任务2：统一网络请求客户端 (4小时)

#### 2.2.1 创建HTTP客户端接口
**文件**: `src/infrastructure/network/IHttpClient.ts`

```typescript
export interface IHttpClient {
  get<T>(url: string, options?: RequestOptions): Promise<HttpResponse<T>>;
  post<T>(url: string, data?: any, options?: RequestOptions): Promise<HttpResponse<T>>;
  put<T>(url: string, data?: any, options?: RequestOptions): Promise<HttpResponse<T>>;
  delete<T>(url: string, options?: RequestOptions): Promise<HttpResponse<T>>;
  
  // 流式请求
  getStream(url: string, options?: RequestOptions): Promise<ReadableStream>;
  postStream(url: string, stream: ReadableStream, options?: RequestOptions): Promise<HttpResponse<void>>;
  
  // 请求拦截器
  addRequestInterceptor(interceptor: RequestInterceptor): void;
  addResponseInterceptor(interceptor: ResponseInterceptor): void;
}
```

#### 2.2.2 实现HTTP客户端
**文件**: `src/infrastructure/network/HttpClient.ts`

主要功能：
- 统一的请求/响应处理
- 自动重试机制
- 请求/响应拦截器
- 超时控制
- 错误处理

### 2.3 任务3：重构加密服务 (3小时)

#### 2.3.1 创建加密服务接口
**文件**: `src/infrastructure/crypto/ICryptoService.ts`

```typescript
export interface ICryptoService {
  encrypt(data: string | ArrayBuffer, key?: string): Promise<EncryptResult>;
  decrypt(encryptedData: string | ArrayBuffer, key?: string): Promise<DecryptResult>;
  
  generateKey(): Promise<string>;
  deriveKey(password: string, salt: string): Promise<string>;
  
  hash(data: string | ArrayBuffer, algorithm?: HashAlgorithm): Promise<string>;
  
  // 流式加密
  createEncryptStream(key: string): TransformStream;
  createDecryptStream(key: string): TransformStream;
}
```

#### 2.3.2 实现AES加密服务
**文件**: `src/infrastructure/crypto/AESCryptoService.ts`

主要功能：
- AES-256-GCM加密
- 密钥派生
- 流式加密支持
- 安全的随机数生成

### 2.4 任务4：建立工具函数库 (3小时)

#### 2.4.1 日志工具
**文件**: `src/infrastructure/utils/Logger.ts`

功能：
- 分级日志记录
- 格式化输出
- 日志过滤
- 性能监控

#### 2.4.2 事件发射器
**文件**: `src/infrastructure/utils/EventEmitter.ts`

功能：
- 类型安全的事件系统
- 异步事件处理
- 事件命名空间
- 内存泄漏防护

#### 2.4.3 验证工具
**文件**: `src/infrastructure/utils/Validator.ts`

功能：
- 数据类型验证
- 格式验证
- 自定义验证规则
- 错误信息本地化

### 2.5 任务5：创建基础设施层测试页面 (4小时)

#### 2.5.1 测试页面结构
**文件**: `test/infrastructure/infrastructure-test.html`

页面包含以下测试模块：
- 存储提供者测试
- 网络客户端测试
- 加密服务测试
- 工具函数测试
- 性能基准测试

#### 2.5.2 存储测试脚本
**文件**: `test/infrastructure/storage-test.js`

测试内容：
- 连接测试
- CRUD操作测试
- 批量操作测试
- 错误处理测试
- 性能测试

#### 2.5.3 网络测试脚本
**文件**: `test/infrastructure/network-test.js`

测试内容：
- HTTP方法测试
- 拦截器测试
- 重试机制测试
- 超时处理测试

#### 2.5.4 加密测试脚本
**文件**: `test/infrastructure/crypto-test.js`

测试内容：
- 加密解密测试
- 密钥生成测试
- 哈希函数测试
- 性能测试

## 3. 实施步骤

### 3.1 第1天
**上午 (4小时)**:
- 创建基础设施层目录结构
- 定义存储接口和类型
- 重构 HuaweiObsProvider

**下午 (4小时)**:
- 重构 MinioProvider
- 创建 LocalStorageProvider 和 MemoryStorageProvider
- 实现存储工厂

### 3.2 第2天
**上午 (4小时)**:
- 实现HTTP客户端
- 创建请求/响应拦截器
- 实现重试和超时机制

**下午 (4小时)**:
- 实现加密服务
- 创建工具函数库
- 开始测试页面开发

### 3.3 第3天
**上午 (4小时)**:
- 完成测试页面开发
- 编写测试脚本
- 进行集成测试

**下午 (4小时)**:
- 修复发现的问题
- 优化性能
- 完善文档

## 4. 验收标准

### 4.1 功能验收
- [ ] 所有存储提供者可以正常初始化和连接
- [ ] CRUD操作功能正常
- [ ] 批量操作性能达标
- [ ] 网络请求功能完整
- [ ] 加密解密功能正确
- [ ] 工具函数工作正常

### 4.2 质量验收
- [ ] 代码覆盖率 ≥ 80%
- [ ] 所有接口有完整的TypeScript类型定义
- [ ] 错误处理完善
- [ ] 性能指标达标

### 4.3 测试验收
- [ ] 测试页面功能完整
- [ ] 所有测试用例通过
- [ ] 性能基准测试通过
- [ ] 错误场景测试通过

## 5. 风险和缓解措施

### 5.1 技术风险
**风险**: 现有代码兼容性问题
**缓解**: 保持向后兼容，渐进式替换

**风险**: 性能回归
**缓解**: 建立性能基准，持续监控

### 5.2 时间风险
**风险**: 任务复杂度超出预期
**缓解**: 优先实现核心功能，次要功能可延后

**风险**: 测试时间不足
**缓解**: 并行开发测试代码，提前发现问题

## 6. 交付物

### 6.1 代码交付
- 重构后的基础设施层代码
- 完整的TypeScript类型定义
- 单元测试代码

### 6.2 测试交付
- 基础设施层测试页面
- 测试脚本和用例
- 性能基准报告

### 6.3 文档交付
- 接口文档
- 使用指南
- 迁移指南

## 7. 下一阶段准备

阶段1完成后，需要为阶段2（数据层重构）做好准备：
- 确认基础设施层接口稳定
- 准备数据层设计文档
- 规划Repository接口设计
