# 任务3.1：实现内存管理器

## 📋 任务概述

**任务ID**: Task-3.1
**任务名称**: 实现内存管理器
**所属阶段**: 阶段3 - 业务层重构
**预计时间**: 3小时
**依赖任务**: Phase-2 (数据层重构完成)
**后续任务**: Task-3.2 (实现同步管理器)

## 🎯 任务目标

实现内存管理器，提供内存的业务逻辑处理，包括创建、编辑、搜索、分类和标签管理等功能。

## 🛠️ 实施步骤

### 步骤1：定义内存管理器接口
**文件**: `src/business/interfaces/IMemoryManager.ts`

```typescript
import { Memory, MemoryType, MemorySearchCriteria } from '../../data/entities/Memory';
import { PageRequest, PageResult, DataResult } from '../../data/types/DataTypes';

export interface IMemoryManager {
  // 基础操作
  createMemory(memoryData: CreateMemoryRequest): Promise<DataResult<Memory>>;
  updateMemory(id: string, updates: UpdateMemoryRequest): Promise<DataResult<Memory>>;
  deleteMemory(id: string): Promise<DataResult<boolean>>;
  getMemory(id: string): Promise<DataResult<Memory | null>>;

  // 查询操作
  searchMemories(criteria: MemorySearchCriteria): Promise<DataResult<Memory[]>>;
  getMemoriesPage(criteria: MemorySearchCriteria, page: PageRequest): Promise<DataResult<PageResult<Memory>>>;
  getRecentMemories(limit?: number): Promise<DataResult<Memory[]>>;
  getFavoriteMemories(): Promise<DataResult<Memory[]>>;
  getArchivedMemories(): Promise<DataResult<Memory[]>>;

  // 分类和标签
  getMemoriesByCategory(categoryId: string): Promise<DataResult<Memory[]>>;
  getMemoriesByTag(tag: string): Promise<DataResult<Memory[]>>;
  getAllTags(): Promise<DataResult<string[]>>;
  getPopularTags(limit?: number): Promise<DataResult<Array<{ tag: string; count: number }>>>;

  // 批量操作
  batchUpdateMemories(updates: Array<{ id: string; data: Partial<Memory> }>): Promise<DataResult<Memory[]>>;
  batchDeleteMemories(ids: string[]): Promise<DataResult<number>>;

  // 统计信息
  getMemoryStats(): Promise<DataResult<MemoryStats>>;
  getMemoryCountByType(): Promise<DataResult<Record<MemoryType, number>>>;
  getMemoryCountByMonth(): Promise<DataResult<Array<{ month: string; count: number }>>>;
}

export interface CreateMemoryRequest {
  title: string;
  content: string;
  type: MemoryType;
  tags?: string[];
  categoryId?: string;
  attachments?: any[];
  metadata?: any;
  isPrivate?: boolean;
  isFavorite?: boolean;
  historicalDate?: Date;
}

export interface UpdateMemoryRequest {
  title?: string;
  content?: string;
  type?: MemoryType;
  tags?: string[];
  categoryId?: string;
  attachments?: any[];
  metadata?: any;
  isPrivate?: boolean;
  isFavorite?: boolean;
  isArchived?: boolean;
  historicalDate?: Date;
}

export interface MemoryStats {
  totalCount: number;
  privateCount: number;
  favoriteCount: number;
  archivedCount: number;
  totalSize: number;
  averageSize: number;
  oldestMemory?: Date;
  newestMemory?: Date;
}
```

### 步骤2：实现内存管理器
**文件**: `src/business/managers/MemoryManager.ts`

```typescript
import { IMemoryManager, CreateMemoryRequest, UpdateMemoryRequest, MemoryStats } from '../interfaces/IMemoryManager';
import { Memory, MemoryType, MemorySearchCriteria } from '../../data/entities/Memory';
import { IRepository } from '../../data/interfaces/IRepository';
import { PageRequest, PageResult, DataResult, SearchCriteria, FilterOperator } from '../../data/types/DataTypes';
import { IEventEmitter } from '../../infrastructure/utils/EventEmitter';
import { logger } from '../../infrastructure/utils/Logger';
import { validator } from '../../infrastructure/utils/Validator';

export class MemoryManager implements IMemoryManager {
  constructor(
    private memoryRepository: IRepository<Memory>,
    private eventEmitter: IEventEmitter
  ) {}

  async createMemory(request: CreateMemoryRequest): Promise<DataResult<Memory>> {
    try {
      // 验证请求数据
      const validationResult = this.validateCreateRequest(request);
      if (!validationResult.valid) {
        return {
          success: false,
          error: new Error(`创建内存验证失败: ${validationResult.errors.join(', ')}`)
        };
      }

      // 创建内存对象
      const memory: Memory = {
        id: this.generateMemoryId(),
        title: request.title.trim(),
        content: request.content.trim(),
        type: request.type,
        tags: this.normalizeTags(request.tags || []),
        categoryId: request.categoryId,
        attachments: request.attachments || [],
        metadata: {
          ...request.metadata,
          people: request.metadata?.people || [],
          events: request.metadata?.events || [],
          customFields: request.metadata?.customFields || {}
        },
        isPrivate: request.isPrivate || false,
        isFavorite: request.isFavorite || false,
        isArchived: false,
        historicalDate: request.historicalDate,
        createdAt: new Date(),
        updatedAt: new Date(),
        version: 1
      };

      // 保存到仓库
      const saveResult = await this.memoryRepository.save(memory);

      if (saveResult.success) {
        // 发射事件
        this.eventEmitter.emit('memoryCreated', memory);
        logger.info(`内存创建成功: ${memory.id} - ${memory.title}`);
      }

      return saveResult;
    } catch (error) {
      logger.error('创建内存失败:', error);
      return { success: false, error: error as Error };
    }
  }

  async updateMemory(id: string, request: UpdateMemoryRequest): Promise<DataResult<Memory>> {
    try {
      // 获取现有内存
      const existingResult = await this.memoryRepository.findById(id);
      if (!existingResult.success || !existingResult.data) {
        return {
          success: false,
          error: new Error(`内存不存在: ${id}`)
        };
      }

      const existingMemory = existingResult.data;

      // 验证更新请求
      const validationResult = this.validateUpdateRequest(request);
      if (!validationResult.valid) {
        return {
          success: false,
          error: new Error(`更新内存验证失败: ${validationResult.errors.join(', ')}`)
        };
      }

      // 准备更新数据
      const updates: Partial<Memory> = {
        ...request,
        updatedAt: new Date()
      };

      // 处理标签标准化
      if (request.tags) {
        updates.tags = this.normalizeTags(request.tags);
      }

      // 更新内存
      const updateResult = await this.memoryRepository.update(id, updates);

      if (updateResult.success) {
        // 发射事件
        this.eventEmitter.emit('memoryUpdated', {
          oldMemory: existingMemory,
          newMemory: updateResult.data
        });
        logger.info(`内存更新成功: ${id}`);
      }

      return updateResult;
    } catch (error) {
      logger.error(`更新内存失败 [${id}]:`, error);
      return { success: false, error: error as Error };
    }
  }

  async deleteMemory(id: string): Promise<DataResult<boolean>> {
    try {
      // 检查内存是否存在
      const existsResult = await this.memoryRepository.exists(id);
      if (!existsResult.success || !existsResult.data) {
        return {
          success: false,
          error: new Error(`内存不存在: ${id}`)
        };
      }

      // 删除内存
      const deleteResult = await this.memoryRepository.delete(id);

      if (deleteResult.success) {
        // 发射事件
        this.eventEmitter.emit('memoryDeleted', { id });
        logger.info(`内存删除成功: ${id}`);
      }

      return deleteResult;
    } catch (error) {
      logger.error(`删除内存失败 [${id}]:`, error);
      return { success: false, error: error as Error };
    }
  }

  async getMemory(id: string): Promise<DataResult<Memory | null>> {
    try {
      const result = await this.memoryRepository.findById(id);

      if (result.success && result.data) {
        // 发射访问事件
        this.eventEmitter.emit('memoryAccessed', { id, memory: result.data });
      }

      return result;
    } catch (error) {
      logger.error(`获取内存失败 [${id}]:`, error);
      return { success: false, error: error as Error };
    }
  }

  async searchMemories(criteria: MemorySearchCriteria): Promise<DataResult<Memory[]>> {
    try {
      const searchCriteria = this.buildSearchCriteria(criteria);
      return await this.memoryRepository.findAll(searchCriteria);
    } catch (error) {
      logger.error('搜索内存失败:', error);
      return { success: false, error: error as Error };
    }
  }

  async getMemoriesPage(criteria: MemorySearchCriteria, page: PageRequest): Promise<DataResult<PageResult<Memory>>> {
    try {
      const searchCriteria = this.buildSearchCriteria(criteria);
      return await this.memoryRepository.findPage(searchCriteria, page);
    } catch (error) {
      logger.error('分页获取内存失败:', error);
      return { success: false, error: error as Error };
    }
  }

  async getRecentMemories(limit: number = 10): Promise<DataResult<Memory[]>> {
    try {
      const criteria: SearchCriteria<Memory> = {
        sort: [{ property: 'updatedAt', direction: 'DESC' }]
      };

      const result = await this.memoryRepository.findAll(criteria);

      if (result.success && result.data) {
        // 限制返回数量
        result.data = result.data.slice(0, limit);
      }

      return result;
    } catch (error) {
      logger.error('获取最近内存失败:', error);
      return { success: false, error: error as Error };
    }
  }

  async getFavoriteMemories(): Promise<DataResult<Memory[]>> {
    try {
      const criteria: SearchCriteria<Memory> = {
        filters: [{
          property: 'isFavorite',
          operator: FilterOperator.EQUALS,
          value: true
        }],
        sort: [{ property: 'updatedAt', direction: 'DESC' }]
      };

      return await this.memoryRepository.findAll(criteria);
    } catch (error) {
      logger.error('获取收藏内存失败:', error);
      return { success: false, error: error as Error };
    }
  }

  async getArchivedMemories(): Promise<DataResult<Memory[]>> {
    try {
      const criteria: SearchCriteria<Memory> = {
        filters: [{
          property: 'isArchived',
          operator: FilterOperator.EQUALS,
          value: true
        }],
        sort: [{ property: 'updatedAt', direction: 'DESC' }]
      };

      return await this.memoryRepository.findAll(criteria);
    } catch (error) {
      logger.error('获取归档内存失败:', error);
      return { success: false, error: error as Error };
    }
  }

  async getMemoriesByCategory(categoryId: string): Promise<DataResult<Memory[]>> {
    try {
      const criteria: SearchCriteria<Memory> = {
        filters: [{
          property: 'categoryId',
          operator: FilterOperator.EQUALS,
          value: categoryId
        }]
      };

      return await this.memoryRepository.findAll(criteria);
    } catch (error) {
      logger.error(`按分类获取内存失败 [${categoryId}]:`, error);
      return { success: false, error: error as Error };
    }
  }

  async getMemoriesByTag(tag: string): Promise<DataResult<Memory[]>> {
    try {
      const criteria: SearchCriteria<Memory> = {
        filters: [{
          property: 'tags',
          operator: FilterOperator.IN,
          values: [tag]
        }]
      };

      return await this.memoryRepository.findAll(criteria);
    } catch (error) {
      logger.error(`按标签获取内存失败 [${tag}]:`, error);
      return { success: false, error: error as Error };
    }
  }

  async getAllTags(): Promise<DataResult<string[]>> {
    try {
      const allMemoriesResult = await this.memoryRepository.findAll();

      if (!allMemoriesResult.success) {
        return { success: false, error: allMemoriesResult.error };
      }

      const memories = allMemoriesResult.data || [];
      const tagSet = new Set<string>();

      memories.forEach(memory => {
        memory.tags.forEach(tag => tagSet.add(tag));
      });

      const tags = Array.from(tagSet).sort();

      return { success: true, data: tags };
    } catch (error) {
      logger.error('获取所有标签失败:', error);
      return { success: false, error: error as Error };
    }
  }

  async getPopularTags(limit: number = 10): Promise<DataResult<Array<{ tag: string; count: number }>>> {
    try {
      const allMemoriesResult = await this.memoryRepository.findAll();

      if (!allMemoriesResult.success) {
        return { success: false, error: allMemoriesResult.error };
      }

      const memories = allMemoriesResult.data || [];
      const tagCounts = new Map<string, number>();

      memories.forEach(memory => {
        memory.tags.forEach(tag => {
          tagCounts.set(tag, (tagCounts.get(tag) || 0) + 1);
        });
      });

      const popularTags = Array.from(tagCounts.entries())
        .map(([tag, count]) => ({ tag, count }))
        .sort((a, b) => b.count - a.count)
        .slice(0, limit);

      return { success: true, data: popularTags };
    } catch (error) {
      logger.error('获取热门标签失败:', error);
      return { success: false, error: error as Error };
    }
  }

  async batchUpdateMemories(updates: Array<{ id: string; data: Partial<Memory> }>): Promise<DataResult<Memory[]>> {
    try {
      const results: Memory[] = [];
      const errors: string[] = [];

      for (const update of updates) {
        const result = await this.updateMemory(update.id, update.data);

        if (result.success && result.data) {
          results.push(result.data);
        } else {
          errors.push(`${update.id}: ${result.error?.message}`);
        }
      }

      if (errors.length > 0) {
        return {
          success: false,
          error: new Error(`批量更新部分失败: ${errors.join(', ')}`),
          data: results
        };
      }

      return { success: true, data: results };
    } catch (error) {
      logger.error('批量更新内存失败:', error);
      return { success: false, error: error as Error };
    }
  }

  async batchDeleteMemories(ids: string[]): Promise<DataResult<number>> {
    try {
      let deletedCount = 0;
      const errors: string[] = [];

      for (const id of ids) {
        const result = await this.deleteMemory(id);

        if (result.success) {
          deletedCount++;
        } else {
          errors.push(`${id}: ${result.error?.message}`);
        }
      }

      if (errors.length > 0) {
        return {
          success: false,
          error: new Error(`批量删除部分失败: ${errors.join(', ')}`),
          data: deletedCount
        };
      }

      return { success: true, data: deletedCount };
    } catch (error) {
      logger.error('批量删除内存失败:', error);
      return { success: false, error: error as Error };
    }
  }

  async getMemoryStats(): Promise<DataResult<MemoryStats>> {
    try {
      const allMemoriesResult = await this.memoryRepository.findAll();

      if (!allMemoriesResult.success) {
        return { success: false, error: allMemoriesResult.error };
      }

      const memories = allMemoriesResult.data || [];

      const stats: MemoryStats = {
        totalCount: memories.length,
        privateCount: memories.filter(m => m.isPrivate).length,
        favoriteCount: memories.filter(m => m.isFavorite).length,
        archivedCount: memories.filter(m => m.isArchived).length,
        totalSize: memories.reduce((sum, m) => sum + (m.content?.length || 0), 0),
        averageSize: 0,
        oldestMemory: undefined,
        newestMemory: undefined
      };

      if (stats.totalCount > 0) {
        stats.averageSize = Math.round(stats.totalSize / stats.totalCount);

        const sortedByDate = memories.sort((a, b) => a.createdAt.getTime() - b.createdAt.getTime());
        stats.oldestMemory = sortedByDate[0]?.createdAt;
        stats.newestMemory = sortedByDate[sortedByDate.length - 1]?.createdAt;
      }

      return { success: true, data: stats };
    } catch (error) {
      logger.error('获取内存统计失败:', error);
      return { success: false, error: error as Error };
    }
  }

  async getMemoryCountByType(): Promise<DataResult<Record<MemoryType, number>>> {
    try {
      const allMemoriesResult = await this.memoryRepository.findAll();

      if (!allMemoriesResult.success) {
        return { success: false, error: allMemoriesResult.error };
      }

      const memories = allMemoriesResult.data || [];
      const counts: Record<MemoryType, number> = {
        [MemoryType.TEXT]: 0,
        [MemoryType.IMAGE]: 0,
        [MemoryType.VIDEO]: 0,
        [MemoryType.AUDIO]: 0,
        [MemoryType.MIXED]: 0
      };

      memories.forEach(memory => {
        counts[memory.type]++;
      });

      return { success: true, data: counts };
    } catch (error) {
      logger.error('按类型统计内存失败:', error);
      return { success: false, error: error as Error };
    }
  }

  async getMemoryCountByMonth(): Promise<DataResult<Array<{ month: string; count: number }>>> {
    try {
      const allMemoriesResult = await this.memoryRepository.findAll();

      if (!allMemoriesResult.success) {
        return { success: false, error: allMemoriesResult.error };
      }

      const memories = allMemoriesResult.data || [];
      const monthCounts = new Map<string, number>();

      memories.forEach(memory => {
        const month = memory.createdAt.toISOString().substring(0, 7); // YYYY-MM
        monthCounts.set(month, (monthCounts.get(month) || 0) + 1);
      });

      const result = Array.from(monthCounts.entries())
        .map(([month, count]) => ({ month, count }))
        .sort((a, b) => a.month.localeCompare(b.month));

      return { success: true, data: result };
    } catch (error) {
      logger.error('按月统计内存失败:', error);
      return { success: false, error: error as Error };
    }
  }

  // 私有辅助方法
  private validateCreateRequest(request: CreateMemoryRequest): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!request.title?.trim()) {
      errors.push('标题不能为空');
    }

    if (!request.content?.trim()) {
      errors.push('内容不能为空');
    }

    if (!Object.values(MemoryType).includes(request.type)) {
      errors.push('无效的内存类型');
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  private validateUpdateRequest(request: UpdateMemoryRequest): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (request.title !== undefined && !request.title?.trim()) {
      errors.push('标题不能为空');
    }

    if (request.content !== undefined && !request.content?.trim()) {
      errors.push('内容不能为空');
    }

    if (request.type !== undefined && !Object.values(MemoryType).includes(request.type)) {
      errors.push('无效的内存类型');
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  private normalizeTags(tags: string[]): string[] {
    return tags
      .map(tag => tag.trim().toLowerCase())
      .filter(tag => tag.length > 0)
      .filter((tag, index, array) => array.indexOf(tag) === index); // 去重
  }

  private generateMemoryId(): string {
    return `mem_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private buildSearchCriteria(criteria: MemorySearchCriteria): SearchCriteria<Memory> {
    const searchCriteria: SearchCriteria<Memory> = {
      filters: [],
      search: criteria.keyword
    };

    // 添加过滤条件
    if (criteria.type) {
      searchCriteria.filters!.push({
        property: 'type',
        operator: FilterOperator.EQUALS,
        value: criteria.type
      });
    }

    if (criteria.tags && criteria.tags.length > 0) {
      searchCriteria.filters!.push({
        property: 'tags',
        operator: FilterOperator.IN,
        values: criteria.tags
      });
    }

    if (criteria.categoryId) {
      searchCriteria.filters!.push({
        property: 'categoryId',
        operator: FilterOperator.EQUALS,
        value: criteria.categoryId
      });
    }

    if (criteria.isPrivate !== undefined) {
      searchCriteria.filters!.push({
        property: 'isPrivate',
        operator: FilterOperator.EQUALS,
        value: criteria.isPrivate
      });
    }

    if (criteria.isFavorite !== undefined) {
      searchCriteria.filters!.push({
        property: 'isFavorite',
        operator: FilterOperator.EQUALS,
        value: criteria.isFavorite
      });
    }

    if (criteria.isArchived !== undefined) {
      searchCriteria.filters!.push({
        property: 'isArchived',
        operator: FilterOperator.EQUALS,
        value: criteria.isArchived
      });
    }

    if (criteria.dateRange) {
      searchCriteria.filters!.push({
        property: 'createdAt',
        operator: FilterOperator.BETWEEN,
        values: [criteria.dateRange.start, criteria.dateRange.end]
      });
    }

    return searchCriteria;
  }
}
```

## ✅ 验收标准

### 功能验收
- [ ] IMemoryManager接口定义完整
- [ ] MemoryManager实现所有接口方法
- [ ] 支持内存的CRUD操作
- [ ] 支持搜索和分页功能
- [ ] 支持标签和分类管理
- [ ] 支持统计功能

### 质量验收
- [ ] TypeScript编译无错误
- [ ] 业务逻辑正确
- [ ] 错误处理完善
- [ ] 事件发射机制正常

## 🔄 幂等性保证

### 执行前检查
```bash
#!/bin/bash
echo "🔍 检查任务3.1执行状态..."

if [ ! -f "test/data-layer-test.html" ]; then
  echo "❌ 依赖任务Phase-2未完成，请先完成数据层重构"
  exit 1
fi

if [ -f "src/business/managers/MemoryManager.ts" ]; then
  echo "⚠️  内存管理器已存在，任务可能已完成"
  read -p "是否继续？(y/N): " confirm
  if [ "$confirm" != "y" ]; then
    exit 1
  fi
fi

echo "✅ 前置检查完成，可以执行任务3.1"
```

### 执行后验证
```bash
#!/bin/bash
echo "🔍 验证任务3.1执行结果..."

files=(
  "src/business/interfaces/IMemoryManager.ts"
  "src/business/managers/MemoryManager.ts"
)

all_created=true
for file in "${files[@]}"; do
  if [ -f "$file" ]; then
    echo "✅ $file 创建成功"
  else
    echo "❌ $file 创建失败"
    all_created=false
  fi
done

if npm run type-check > /dev/null 2>&1; then
  echo "✅ TypeScript编译检查通过"
else
  echo "❌ TypeScript编译检查失败"
  all_created=false
fi

if [ "$all_created" = true ]; then
  echo "🎉 任务3.1执行成功！"
  exit 0
else
  echo "💥 任务3.1执行失败，请检查错误信息"
  exit 1
fi
```

## 📝 完成确认

任务完成后，请在检查清单中标记：
- [ ] 任务3.1已完成
- [ ] 内存管理器接口定义完成
- [ ] 内存管理器实现完成
- [ ] 业务逻辑测试通过
- [ ] 准备执行任务3.2

## 🔗 相关链接

- [上一个阶段：Phase-2 数据层重构](../phase2/task-2.6-data-layer-test-page.md)
- [下一个任务：Task-3.2 实现同步管理器](./task-3.2-sync-manager.md)
- [重构检查清单](../00-refactoring-checklist.md)