# 任务3.3：实现配置管理器

## 📋 任务概述

**任务ID**: Task-3.3
**任务名称**: 实现配置管理器
**所属阶段**: 阶段3 - 业务层重构
**预计时间**: 2小时
**依赖任务**: Task-3.2 (实现同步管理器)
**后续任务**: Task-3.4 (实现搜索管理器)

## 🎯 任务目标

实现配置管理器，提供配置的业务逻辑处理，包括配置验证、默认值管理、配置导入导出和变更通知。

## 🛠️ 实施步骤

### 步骤1：定义配置管理器接口
**文件**: `src/business/interfaces/IConfigManager.ts`

```typescript
import { Configuration, ConfigurationType, ConfigurationCategory } from '../../data/entities/Configuration';
import { DataResult } from '../../data/types/DataTypes';

export interface IConfigManager {
  // 基础配置操作
  getConfig<T = any>(key: string, defaultValue?: T): Promise<T | undefined>;
  setConfig(key: string, value: any, type?: ConfigurationType): Promise<DataResult<Configuration>>;
  removeConfig(key: string): Promise<DataResult<boolean>>;

  // 批量操作
  getConfigs(keys: string[]): Promise<Record<string, any>>;
  setConfigs(configs: Record<string, any>): Promise<DataResult<Configuration[]>>;

  // 分类管理
  getConfigsByCategory(category: ConfigurationCategory): Promise<DataResult<Configuration[]>>;
  getCategoryDefaults(category: ConfigurationCategory): Promise<Record<string, any>>;
  resetCategoryToDefaults(category: ConfigurationCategory): Promise<DataResult<number>>;

  // 配置验证
  validateConfig(key: string, value: any): Promise<ValidationResult>;
  validateConfigs(configs: Record<string, any>): Promise<ValidationResult>;

  // 导入导出
  exportConfigs(categories?: ConfigurationCategory[]): Promise<DataResult<ConfigExport>>;
  importConfigs(configData: ConfigExport, options?: ImportOptions): Promise<DataResult<ImportResult>>;

  // 配置监听
  watchConfig(key: string, callback: (value: any, oldValue: any) => void): () => void;
  watchCategory(category: ConfigurationCategory, callback: (configs: Configuration[]) => void): () => void;

  // 系统配置
  initializeDefaults(): Promise<void>;
  getSystemInfo(): Promise<SystemInfo>;
}

export interface ValidationResult {
  valid: boolean;
  errors: ValidationError[];
}

export interface ValidationError {
  key: string;
  message: string;
  code: string;
}

export interface ConfigExport {
  version: string;
  exportedAt: Date;
  categories: ConfigurationCategory[];
  configurations: Configuration[];
  metadata: {
    totalCount: number;
    encrypted: boolean;
    checksum: string;
  };
}

export interface ImportOptions {
  overwrite?: boolean;
  validateOnly?: boolean;
  categories?: ConfigurationCategory[];
  password?: string;
}

export interface ImportResult {
  imported: number;
  skipped: number;
  errors: string[];
  conflicts: string[];
}

export interface SystemInfo {
  version: string;
  platform: string;
  userAgent: string;
  storage: {
    type: string;
    available: boolean;
    quota?: number;
    used?: number;
  };
  features: {
    encryption: boolean;
    sync: boolean;
    offline: boolean;
  };
}
```

## 🛠️ 实施步骤

### 步骤1：定义配置管理器接口
**文件**: `src/business/interfaces/IConfigManager.ts`

```typescript
import { Configuration, ConfigurationType, ConfigurationCategory } from '../../data/entities/Configuration';
import { DataResult } from '../../data/types/DataTypes';

export interface IConfigManager {
  // 基础配置操作
  getConfig<T = any>(key: string, defaultValue?: T): Promise<T | undefined>;
  setConfig<T = any>(key: string, value: T, category?: ConfigurationCategory): Promise<DataResult<Configuration>>;
  removeConfig(key: string): Promise<DataResult<boolean>>;
  
  // 批量操作
  getConfigs(keys: string[]): Promise<Record<string, any>>;
  setConfigs(configs: Record<string, any>): Promise<DataResult<Configuration[]>>;
  
  // 分类管理
  getConfigsByCategory(category: ConfigurationCategory): Promise<DataResult<Configuration[]>>;
  resetCategoryToDefaults(category: ConfigurationCategory): Promise<DataResult<number>>;
  
  // 导入导出
  exportConfigs(categories?: ConfigurationCategory[]): Promise<DataResult<ConfigExport>>;
  importConfigs(configData: ConfigExport, options?: ImportOptions): Promise<DataResult<ImportResult>>;
  
  // 默认值管理
  registerDefaults(defaults: DefaultConfigMap): void;
  getDefaults(): DefaultConfigMap;
  resetToDefaults(keys?: string[]): Promise<DataResult<number>>;
  
  // 验证和监听
  validateConfig(key: string, value: any): Promise<ValidationResult>;
  onConfigChanged(callback: ConfigChangeCallback): () => void;
  
  // 系统配置
  getSystemInfo(): Promise<SystemInfo>;
  getStorageInfo(): Promise<StorageInfo>;
}

export interface ConfigExport {
  version: string;
  exportedAt: Date;
  categories: ConfigurationCategory[];
  configs: Configuration[];
  metadata: {
    totalCount: number;
    encrypted: boolean;
    checksum: string;
  };
}

export interface ImportOptions {
  overwrite?: boolean;
  validateOnly?: boolean;
  categories?: ConfigurationCategory[];
  skipEncrypted?: boolean;
}

export interface ImportResult {
  imported: number;
  skipped: number;
  errors: string[];
  conflicts: string[];
}

export interface DefaultConfigMap {
  [key: string]: {
    value: any;
    type: ConfigurationType;
    category: ConfigurationCategory;
    description?: string;
    validation?: ValidationRule[];
  };
}

export interface ValidationResult {
  valid: boolean;
  errors: string[];
  warnings: string[];
}

export interface ValidationRule {
  type: 'required' | 'type' | 'range' | 'pattern' | 'custom';
  params?: any;
  message?: string;
}

export type ConfigChangeCallback = (event: ConfigChangeEvent) => void;

export interface ConfigChangeEvent {
  key: string;
  oldValue: any;
  newValue: any;
  category: ConfigurationCategory;
  timestamp: Date;
}

export interface SystemInfo {
  version: string;
  platform: string;
  userAgent: string;
  language: string;
  timezone: string;
  memory: {
    used: number;
    total: number;
  };
}

export interface StorageInfo {
  type: string;
  available: boolean;
  quota: {
    used: number;
    total: number;
  };
  performance: {
    readSpeed: number;
    writeSpeed: number;
  };
}
```

### 步骤2：实现配置管理器
**文件**: `src/business/managers/ConfigManager.ts`

```typescript
import { IConfigManager, ConfigExport, ImportOptions, ImportResult, DefaultConfigMap, ValidationResult, ConfigChangeCallback, SystemInfo, StorageInfo } from '../interfaces/IConfigManager';
import { Configuration, ConfigurationType, ConfigurationCategory } from '../../data/entities/Configuration';
import { IRepository } from '../../data/interfaces/IRepository';
import { DataResult } from '../../data/types/DataTypes';
import { IEventEmitter } from '../../infrastructure/utils/EventEmitter';
import { ICryptoService } from '../../infrastructure/interfaces/ICryptoService';
import { logger } from '../../infrastructure/utils/Logger';

export class ConfigManager implements IConfigManager {
  private defaults: DefaultConfigMap = {};
  private changeCallbacks: ConfigChangeCallback[] = [];

  constructor(
    private configRepository: IRepository<Configuration>,
    private eventEmitter: IEventEmitter,
    private cryptoService: ICryptoService
  ) {
    this.initializeDefaults();
  }

  async getConfig<T = any>(key: string, defaultValue?: T): Promise<T | undefined> {
    try {
      const result = await this.configRepository.findBy('key', key);
      
      if (result.success && result.data && result.data.length > 0) {
        return result.data[0].value as T;
      }
      
      // 返回默认值
      if (defaultValue !== undefined) {
        return defaultValue;
      }
      
      // 检查注册的默认值
      const defaultConfig = this.defaults[key];
      if (defaultConfig) {
        return defaultConfig.value as T;
      }
      
      return undefined;
    } catch (error) {
      logger.error(`获取配置失败 [${key}]:`, error);
      return defaultValue;
    }
  }

  async setConfig<T = any>(key: string, value: T, category: ConfigurationCategory = ConfigurationCategory.USER): Promise<DataResult<Configuration>> {
    try {
      // 验证配置值
      const validationResult = await this.validateConfig(key, value);
      if (!validationResult.valid) {
        return {
          success: false,
          error: new Error(`配置验证失败: ${validationResult.errors.join(', ')}`)
        };
      }

      // 获取现有配置
      const existingResult = await this.configRepository.findBy('key', key);
      let config: Configuration;
      let oldValue: any = undefined;

      if (existingResult.success && existingResult.data && existingResult.data.length > 0) {
        // 更新现有配置
        const existing = existingResult.data[0];
        oldValue = existing.value;
        
        const updateResult = await this.configRepository.update(existing.id, {
          value,
          updatedAt: new Date()
        });
        
        if (!updateResult.success) {
          return updateResult;
        }
        
        config = updateResult.data!;
      } else {
        // 创建新配置
        const newConfig: Configuration = {
          id: this.generateConfigId(),
          key,
          value,
          type: this.inferType(value),
          category,
          isEncrypted: this.shouldEncrypt(key),
          isReadonly: false,
          createdAt: new Date(),
          updatedAt: new Date(),
          version: 1
        };

        const saveResult = await this.configRepository.save(newConfig);
        if (!saveResult.success) {
          return saveResult;
        }
        
        config = saveResult.data!;
      }

      // 发射变更事件
      this.emitConfigChange(key, oldValue, value, category);
      
      return { success: true, data: config };
    } catch (error) {
      logger.error(`设置配置失败 [${key}]:`, error);
      return { success: false, error: error as Error };
    }
  }

  async removeConfig(key: string): Promise<DataResult<boolean>> {
    try {
      const existingResult = await this.configRepository.findBy('key', key);
      
      if (!existingResult.success || !existingResult.data || existingResult.data.length === 0) {
        return { success: true, data: false };
      }

      const config = existingResult.data[0];
      
      // 检查是否为只读配置
      if (config.isReadonly) {
        return {
          success: false,
          error: new Error('只读配置不能删除')
        };
      }

      const deleteResult = await this.configRepository.delete(config.id);
      
      if (deleteResult.success) {
        this.emitConfigChange(key, config.value, undefined, config.category);
      }
      
      return deleteResult;
    } catch (error) {
      logger.error(`删除配置失败 [${key}]:`, error);
      return { success: false, error: error as Error };
    }
  }

  async getConfigs(keys: string[]): Promise<Record<string, any>> {
    const result: Record<string, any> = {};
    
    for (const key of keys) {
      const value = await this.getConfig(key);
      if (value !== undefined) {
        result[key] = value;
      }
    }
    
    return result;
  }

  async setConfigs(configs: Record<string, any>): Promise<DataResult<Configuration[]>> {
    const results: Configuration[] = [];
    const errors: string[] = [];

    for (const [key, value] of Object.entries(configs)) {
      const result = await this.setConfig(key, value);
      
      if (result.success && result.data) {
        results.push(result.data);
      } else {
        errors.push(`${key}: ${result.error?.message}`);
      }
    }

    if (errors.length > 0) {
      return {
        success: false,
        error: new Error(`批量设置配置部分失败: ${errors.join(', ')}`),
        data: results
      };
    }

    return { success: true, data: results };
  }

  async getConfigsByCategory(category: ConfigurationCategory): Promise<DataResult<Configuration[]>> {
    try {
      return await this.configRepository.findBy('category', category);
    } catch (error) {
      logger.error(`按分类获取配置失败 [${category}]:`, error);
      return { success: false, error: error as Error };
    }
  }

  async resetCategoryToDefaults(category: ConfigurationCategory): Promise<DataResult<number>> {
    try {
      let resetCount = 0;
      
      for (const [key, defaultConfig] of Object.entries(this.defaults)) {
        if (defaultConfig.category === category) {
          const result = await this.setConfig(key, defaultConfig.value, category);
          if (result.success) {
            resetCount++;
          }
        }
      }
      
      return { success: true, data: resetCount };
    } catch (error) {
      logger.error(`重置分类配置失败 [${category}]:`, error);
      return { success: false, error: error as Error };
    }
  }

  async exportConfigs(categories?: ConfigurationCategory[]): Promise<DataResult<ConfigExport>> {
    try {
      let allConfigs: Configuration[];
      
      if (categories && categories.length > 0) {
        const results = await Promise.all(
          categories.map(cat => this.getConfigsByCategory(cat))
        );
        
        allConfigs = results
          .filter(r => r.success && r.data)
          .flatMap(r => r.data!);
      } else {
        const result = await this.configRepository.findAll();
        if (!result.success) {
          return { success: false, error: result.error };
        }
        allConfigs = result.data || [];
      }

      // 生成校验和
      const checksum = await this.generateChecksum(allConfigs);

      const exportData: ConfigExport = {
        version: '1.0.0',
        exportedAt: new Date(),
        categories: categories || Object.values(ConfigurationCategory),
        configs: allConfigs,
        metadata: {
          totalCount: allConfigs.length,
          encrypted: allConfigs.some(c => c.isEncrypted),
          checksum
        }
      };

      return { success: true, data: exportData };
    } catch (error) {
      logger.error('导出配置失败:', error);
      return { success: false, error: error as Error };
    }
  }

  async importConfigs(configData: ConfigExport, options: ImportOptions = {}): Promise<DataResult<ImportResult>> {
    try {
      // 验证导入数据
      const validationResult = await this.validateImportData(configData);
      if (!validationResult.valid) {
        return {
          success: false,
          error: new Error(`导入数据验证失败: ${validationResult.errors.join(', ')}`)
        };
      }

      if (options.validateOnly) {
        return {
          success: true,
          data: {
            imported: 0,
            skipped: 0,
            errors: [],
            conflicts: []
          }
        };
      }

      const result: ImportResult = {
        imported: 0,
        skipped: 0,
        errors: [],
        conflicts: []
      };

      for (const config of configData.configs) {
        try {
          // 检查分类过滤
          if (options.categories && !options.categories.includes(config.category)) {
            result.skipped++;
            continue;
          }

          // 检查加密配置
          if (options.skipEncrypted && config.isEncrypted) {
            result.skipped++;
            continue;
          }

          // 检查冲突
          const existing = await this.getConfig(config.key);
          if (existing !== undefined && !options.overwrite) {
            result.conflicts.push(config.key);
            result.skipped++;
            continue;
          }

          // 导入配置
          const importResult = await this.setConfig(config.key, config.value, config.category);
          if (importResult.success) {
            result.imported++;
          } else {
            result.errors.push(`${config.key}: ${importResult.error?.message}`);
          }
        } catch (error) {
          result.errors.push(`${config.key}: ${error.message}`);
        }
      }

      return { success: true, data: result };
    } catch (error) {
      logger.error('导入配置失败:', error);
      return { success: false, error: error as Error };
    }
  }

  registerDefaults(defaults: DefaultConfigMap): void {
    Object.assign(this.defaults, defaults);
  }

  getDefaults(): DefaultConfigMap {
    return { ...this.defaults };
  }

  async resetToDefaults(keys?: string[]): Promise<DataResult<number>> {
    try {
      const keysToReset = keys || Object.keys(this.defaults);
      let resetCount = 0;

      for (const key of keysToReset) {
        const defaultConfig = this.defaults[key];
        if (defaultConfig) {
          const result = await this.setConfig(key, defaultConfig.value, defaultConfig.category);
          if (result.success) {
            resetCount++;
          }
        }
      }

      return { success: true, data: resetCount };
    } catch (error) {
      logger.error('重置默认配置失败:', error);
      return { success: false, error: error as Error };
    }
  }

  async validateConfig(key: string, value: any): Promise<ValidationResult> {
    const result: ValidationResult = {
      valid: true,
      errors: [],
      warnings: []
    };

    // 检查默认配置的验证规则
    const defaultConfig = this.defaults[key];
    if (defaultConfig && defaultConfig.validation) {
      for (const rule of defaultConfig.validation) {
        const ruleResult = this.validateRule(value, rule);
        if (!ruleResult.valid) {
          result.valid = false;
          result.errors.push(ruleResult.message || `验证规则失败: ${rule.type}`);
        }
      }
    }

    return result;
  }

  onConfigChanged(callback: ConfigChangeCallback): () => void {
    this.changeCallbacks.push(callback);
    
    return () => {
      const index = this.changeCallbacks.indexOf(callback);
      if (index > -1) {
        this.changeCallbacks.splice(index, 1);
      }
    };
  }

  async getSystemInfo(): Promise<SystemInfo> {
    return {
      version: '1.0.0',
      platform: navigator.platform,
      userAgent: navigator.userAgent,
      language: navigator.language,
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      memory: {
        used: (performance as any).memory?.usedJSHeapSize || 0,
        total: (performance as any).memory?.totalJSHeapSize || 0
      }
    };
  }

  async getStorageInfo(): Promise<StorageInfo> {
    let quota = { used: 0, total: 0 };
    
    try {
      if ('storage' in navigator && 'estimate' in navigator.storage) {
        const estimate = await navigator.storage.estimate();
        quota = {
          used: estimate.usage || 0,
          total: estimate.quota || 0
        };
      }
    } catch (error) {
      logger.warn('获取存储配额失败:', error);
    }

    return {
      type: 'IndexedDB',
      available: true,
      quota,
      performance: {
        readSpeed: 0, // 需要实际测量
        writeSpeed: 0  // 需要实际测量
      }
    };
  }

  // 私有方法
  private initializeDefaults(): void {
    this.registerDefaults({
      'app.theme': {
        value: 'light',
        type: ConfigurationType.STRING,
        category: ConfigurationCategory.UI,
        description: '应用主题'
      },
      'app.language': {
        value: 'zh-CN',
        type: ConfigurationType.STRING,
        category: ConfigurationCategory.UI,
        description: '应用语言'
      },
      'storage.autoBackup': {
        value: true,
        type: ConfigurationType.BOOLEAN,
        category: ConfigurationCategory.STORAGE,
        description: '自动备份'
      },
      'sync.interval': {
        value: 300000,
        type: ConfigurationType.NUMBER,
        category: ConfigurationCategory.SYNC,
        description: '同步间隔(毫秒)'
      }
    });
  }

  private inferType(value: any): ConfigurationType {
    if (typeof value === 'string') return ConfigurationType.STRING;
    if (typeof value === 'number') return ConfigurationType.NUMBER;
    if (typeof value === 'boolean') return ConfigurationType.BOOLEAN;
    if (Array.isArray(value)) return ConfigurationType.ARRAY;
    if (typeof value === 'object') return ConfigurationType.OBJECT;
    return ConfigurationType.STRING;
  }

  private shouldEncrypt(key: string): boolean {
    const sensitiveKeys = ['password', 'token', 'secret', 'key'];
    return sensitiveKeys.some(sensitive => key.toLowerCase().includes(sensitive));
  }

  private generateConfigId(): string {
    return `config_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private emitConfigChange(key: string, oldValue: any, newValue: any, category: ConfigurationCategory): void {
    const event = {
      key,
      oldValue,
      newValue,
      category,
      timestamp: new Date()
    };

    // 通知回调函数
    this.changeCallbacks.forEach(callback => {
      try {
        callback(event);
      } catch (error) {
        logger.error('配置变更回调执行失败:', error);
      }
    });

    // 发射事件
    this.eventEmitter.emit('configChanged', event);
  }

  private async generateChecksum(configs: Configuration[]): Promise<string> {
    const data = JSON.stringify(configs.map(c => ({ key: c.key, value: c.value })));
    return await this.cryptoService.hash(data);
  }

  private async validateImportData(configData: ConfigExport): Promise<ValidationResult> {
    const result: ValidationResult = {
      valid: true,
      errors: [],
      warnings: []
    };

    if (!configData.version) {
      result.valid = false;
      result.errors.push('缺少版本信息');
    }

    if (!Array.isArray(configData.configs)) {
      result.valid = false;
      result.errors.push('配置数据格式错误');
    }

    return result;
  }

  private validateRule(value: any, rule: any): { valid: boolean; message?: string } {
    switch (rule.type) {
      case 'required':
        return {
          valid: value !== null && value !== undefined && value !== '',
          message: rule.message || '此配置为必填项'
        };
      case 'type':
        const expectedType = rule.params?.type;
        return {
          valid: typeof value === expectedType,
          message: rule.message || `配置类型必须为${expectedType}`
        };
      case 'range':
        const { min, max } = rule.params || {};
        const valid = (min === undefined || value >= min) && (max === undefined || value <= max);
        return {
          valid,
          message: rule.message || `配置值必须在${min}-${max}范围内`
        };
      default:
        return { valid: true };
    }
  }
}
```

## ✅ 验收标准

### 功能验收
- [ ] IConfigManager接口定义完整
- [ ] ConfigManager实现所有接口方法
- [ ] 支持配置的CRUD操作
- [ ] 支持配置导入导出功能
- [ ] 支持默认值管理
- [ ] 支持配置验证和变更通知

### 质量验收
- [ ] TypeScript编译无错误
- [ ] 业务逻辑正确
- [ ] 错误处理完善
- [ ] 事件发射机制正常

## 🔄 幂等性保证

### 执行前检查
```bash
#!/bin/bash
echo "🔍 检查任务3.3执行状态..."

if [ ! -f "src/business/managers/SyncManager.ts" ]; then
  echo "❌ 依赖任务Task-3.2未完成，请先执行Task-3.2"
  exit 1
fi

if [ -f "src/business/managers/ConfigManager.ts" ]; then
  echo "⚠️  配置管理器已存在，任务可能已完成"
  read -p "是否继续？(y/N): " confirm
  if [ "$confirm" != "y" ]; then
    exit 1
  fi
fi

echo "✅ 前置检查完成，可以执行任务3.3"
```

### 执行后验证
```bash
#!/bin/bash
echo "🔍 验证任务3.3执行结果..."

files=(
  "src/business/interfaces/IConfigManager.ts"
  "src/business/managers/ConfigManager.ts"
)

all_created=true
for file in "${files[@]}"; do
  if [ -f "$file" ]; then
    echo "✅ $file 创建成功"
  else
    echo "❌ $file 创建失败"
    all_created=false
  fi
done

if npm run type-check > /dev/null 2>&1; then
  echo "✅ TypeScript编译检查通过"
else
  echo "❌ TypeScript编译检查失败"
  all_created=false
fi

if [ "$all_created" = true ]; then
  echo "🎉 任务3.3执行成功！"
  exit 0
else
  echo "💥 任务3.3执行失败，请检查错误信息"
  exit 1
fi
```

## 📝 完成确认

任务完成后，请在检查清单中标记：
- [ ] 任务3.3已完成
- [ ] 配置管理器接口定义完成
- [ ] 配置管理器实现完成
- [ ] 配置业务逻辑测试通过
- [ ] 准备执行任务3.4

## 🔗 相关链接

- [上一个任务：Task-3.2 实现同步管理器](./task-3.2-sync-manager.md)
- [下一个任务：Task-3.4 实现搜索管理器](./task-3.4-search-manager.md)
- [重构检查清单](../00-refactoring-checklist.md)
