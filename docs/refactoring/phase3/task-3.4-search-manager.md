# 任务3.4：实现搜索管理器

## 📋 任务概述

**任务ID**: Task-3.4
**任务名称**: 实现搜索管理器
**所属阶段**: 阶段3 - 业务层重构
**预计时间**: 3小时
**依赖任务**: Task-3.3 (实现配置管理器)
**后续任务**: Task-3.5 (实现迁移管理器)

## 🎯 任务目标

实现搜索管理器，提供高级搜索和索引功能，包括全文搜索、标签搜索、日期范围搜索和搜索历史管理。

## 🛠️ 实施步骤

### 步骤1：定义搜索管理器接口
**文件**: `src/business/interfaces/ISearchManager.ts`

```typescript
import { Memory, MemoryType } from '../../data/entities/Memory';
import { PageRequest, PageResult, DataResult } from '../../data/types/DataTypes';

export interface ISearchManager {
  // 基础搜索
  search(query: SearchQuery): Promise<DataResult<SearchResult>>;
  searchPage(query: SearchQuery, page: PageRequest): Promise<DataResult<PageResult<Memory>>>;

  // 快速搜索
  quickSearch(keyword: string, limit?: number): Promise<DataResult<Memory[]>>;
  searchSuggestions(keyword: string, limit?: number): Promise<DataResult<SearchSuggestion[]>>;

  // 高级搜索
  advancedSearch(criteria: AdvancedSearchCriteria): Promise<DataResult<SearchResult>>;

  // 搜索历史
  getSearchHistory(limit?: number): Promise<DataResult<SearchHistoryItem[]>>;
  addToSearchHistory(query: string): Promise<void>;
  clearSearchHistory(): Promise<void>;
  removeFromSearchHistory(id: string): Promise<void>;

  // 搜索索引
  buildIndex(): Promise<DataResult<IndexStats>>;
  updateIndex(memoryIds: string[]): Promise<DataResult<number>>;
  getIndexStats(): Promise<DataResult<IndexStats>>;

  // 搜索配置
  setSearchConfig(config: SearchConfig): void;
  getSearchConfig(): SearchConfig;
}

export interface SearchQuery {
  keyword?: string;
  type?: MemoryType;
  tags?: string[];
  categoryId?: string;
  dateRange?: DateRange;
  sortBy?: SortOption;
  includeArchived?: boolean;
  includePrivate?: boolean;
}

export interface AdvancedSearchCriteria extends SearchQuery {
  titleOnly?: boolean;
  contentOnly?: boolean;
  exactMatch?: boolean;
  caseSensitive?: boolean;
  useRegex?: boolean;
  minLength?: number;
  maxLength?: number;
  hasAttachments?: boolean;
  attachmentTypes?: string[];
}

export interface SearchResult {
  memories: Memory[];
  totalCount: number;
  searchTime: number;
  highlights: SearchHighlight[];
  facets: SearchFacet[];
}

export interface SearchHighlight {
  memoryId: string;
  field: string;
  fragments: string[];
}

export interface SearchFacet {
  field: string;
  values: Array<{
    value: string;
    count: number;
  }>;
}

export interface SearchSuggestion {
  text: string;
  type: 'keyword' | 'tag' | 'title';
  count: number;
}

export interface SearchHistoryItem {
  id: string;
  query: string;
  timestamp: Date;
  resultCount: number;
}

export interface DateRange {
  start: Date;
  end: Date;
}

export interface SortOption {
  field: 'relevance' | 'date' | 'title' | 'type';
  direction: 'asc' | 'desc';
}

export interface IndexStats {
  totalDocuments: number;
  totalTerms: number;
  indexSize: number;
  lastUpdated: Date;
  buildTime: number;
}

export interface SearchConfig {
  enableFuzzySearch: boolean;
  fuzzyThreshold: number;
  maxSuggestions: number;
  maxHistoryItems: number;
  highlightEnabled: boolean;
  facetsEnabled: boolean;
  indexUpdateInterval: number;
}
```

### 步骤2：实现搜索索引
**文件**: `src/business/search/SearchIndex.ts`

```typescript
export interface SearchIndex {
  // 索引管理
  addDocument(id: string, document: IndexDocument): void;
  removeDocument(id: string): void;
  updateDocument(id: string, document: IndexDocument): void;

  // 搜索
  search(query: string, options?: SearchOptions): SearchIndexResult[];

  // 统计
  getStats(): IndexStats;
  clear(): void;
}

export interface IndexDocument {
  id: string;
  title: string;
  content: string;
  tags: string[];
  type: string;
  category?: string;
  createdAt: Date;
  metadata: Record<string, any>;
}

export interface SearchOptions {
  fuzzy?: boolean;
  fuzzyThreshold?: number;
  fields?: string[];
  boost?: Record<string, number>;
  limit?: number;
}

export interface SearchIndexResult {
  id: string;
  score: number;
  highlights: Record<string, string[]>;
}

export class InMemorySearchIndex implements SearchIndex {
  private documents = new Map<string, IndexDocument>();
  private termIndex = new Map<string, Set<string>>();
  private fieldBoosts: Record<string, number> = {
    title: 2.0,
    content: 1.0,
    tags: 1.5
  };

  addDocument(id: string, document: IndexDocument): void {
    // 移除旧文档
    if (this.documents.has(id)) {
      this.removeDocument(id);
    }

    this.documents.set(id, document);

    // 索引文档内容
    this.indexDocument(id, document);
  }

  removeDocument(id: string): void {
    const document = this.documents.get(id);
    if (!document) return;

    // 从词项索引中移除
    this.removeFromTermIndex(id, document);
    this.documents.delete(id);
  }

  updateDocument(id: string, document: IndexDocument): void {
    this.addDocument(id, document);
  }

  search(query: string, options: SearchOptions = {}): SearchIndexResult[] {
    const terms = this.tokenize(query.toLowerCase());
    const results = new Map<string, number>();

    for (const term of terms) {
      const matchingDocs = this.findMatchingDocuments(term, options.fuzzy, options.fuzzyThreshold);

      for (const [docId, score] of matchingDocs) {
        results.set(docId, (results.get(docId) || 0) + score);
      }
    }

    // 转换为结果数组并排序
    const searchResults: SearchIndexResult[] = Array.from(results.entries())
      .map(([id, score]) => ({
        id,
        score,
        highlights: this.generateHighlights(id, terms)
      }))
      .sort((a, b) => b.score - a.score);

    return options.limit ? searchResults.slice(0, options.limit) : searchResults;
  }

  getStats(): IndexStats {
    return {
      totalDocuments: this.documents.size,
      totalTerms: this.termIndex.size,
      indexSize: this.estimateIndexSize(),
      lastUpdated: new Date(),
      buildTime: 0
    };
  }

  clear(): void {
    this.documents.clear();
    this.termIndex.clear();
  }

  private indexDocument(id: string, document: IndexDocument): void {
    // 索引标题
    const titleTerms = this.tokenize(document.title.toLowerCase());
    titleTerms.forEach(term => this.addToTermIndex(term, id));

    // 索引内容
    const contentTerms = this.tokenize(document.content.toLowerCase());
    contentTerms.forEach(term => this.addToTermIndex(term, id));

    // 索引标签
    document.tags.forEach(tag => {
      const tagTerms = this.tokenize(tag.toLowerCase());
      tagTerms.forEach(term => this.addToTermIndex(term, id));
    });
  }

  private removeFromTermIndex(id: string, document: IndexDocument): void {
    const allTerms = [
      ...this.tokenize(document.title.toLowerCase()),
      ...this.tokenize(document.content.toLowerCase()),
      ...document.tags.flatMap(tag => this.tokenize(tag.toLowerCase()))
    ];

    allTerms.forEach(term => {
      const docSet = this.termIndex.get(term);
      if (docSet) {
        docSet.delete(id);
        if (docSet.size === 0) {
          this.termIndex.delete(term);
        }
      }
    });
  }

  private addToTermIndex(term: string, docId: string): void {
    if (!this.termIndex.has(term)) {
      this.termIndex.set(term, new Set());
    }
    this.termIndex.get(term)!.add(docId);
  }

  private findMatchingDocuments(term: string, fuzzy = false, threshold = 0.8): Map<string, number> {
    const results = new Map<string, number>();

    // 精确匹配
    const exactMatches = this.termIndex.get(term);
    if (exactMatches) {
      exactMatches.forEach(docId => {
        results.set(docId, (results.get(docId) || 0) + 1.0);
      });
    }

    // 模糊匹配
    if (fuzzy) {
      for (const [indexTerm, docIds] of this.termIndex.entries()) {
        if (indexTerm !== term) {
          const similarity = this.calculateSimilarity(term, indexTerm);
          if (similarity >= threshold) {
            docIds.forEach(docId => {
              results.set(docId, (results.get(docId) || 0) + similarity * 0.5);
            });
          }
        }
      }
    }

    return results;
  }

  private generateHighlights(docId: string, terms: string[]): Record<string, string[]> {
    const document = this.documents.get(docId);
    if (!document) return {};

    const highlights: Record<string, string[]> = {};

    // 生成标题高亮
    const titleHighlights = this.highlightText(document.title, terms);
    if (titleHighlights.length > 0) {
      highlights.title = titleHighlights;
    }

    // 生成内容高亮
    const contentHighlights = this.highlightText(document.content, terms, 100);
    if (contentHighlights.length > 0) {
      highlights.content = contentHighlights;
    }

    return highlights;
  }

  private highlightText(text: string, terms: string[], maxLength = 200): string[] {
    const highlights: string[] = [];
    const lowerText = text.toLowerCase();

    for (const term of terms) {
      const index = lowerText.indexOf(term);
      if (index !== -1) {
        const start = Math.max(0, index - 50);
        const end = Math.min(text.length, index + term.length + 50);
        let fragment = text.substring(start, end);

        if (start > 0) fragment = '...' + fragment;
        if (end < text.length) fragment = fragment + '...';

        // 高亮关键词
        const regex = new RegExp(`(${term})`, 'gi');
        fragment = fragment.replace(regex, '<mark>$1</mark>');

        highlights.push(fragment);
      }
    }

    return highlights;
  }

  private tokenize(text: string): string[] {
    return text
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(token => token.length > 1)
      .filter((token, index, array) => array.indexOf(token) === index);
  }

  private calculateSimilarity(str1: string, str2: string): number {
    const longer = str1.length > str2.length ? str1 : str2;
    const shorter = str1.length > str2.length ? str2 : str1;

    if (longer.length === 0) return 1.0;

    const distance = this.levenshteinDistance(longer, shorter);
    return (longer.length - distance) / longer.length;
  }

  private levenshteinDistance(str1: string, str2: string): number {
    const matrix = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(null));

    for (let i = 0; i <= str1.length; i++) matrix[0][i] = i;
    for (let j = 0; j <= str2.length; j++) matrix[j][0] = j;

    for (let j = 1; j <= str2.length; j++) {
      for (let i = 1; i <= str1.length; i++) {
        const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
        matrix[j][i] = Math.min(
          matrix[j][i - 1] + 1,
          matrix[j - 1][i] + 1,
          matrix[j - 1][i - 1] + indicator
        );
      }
    }

    return matrix[str2.length][str1.length];
  }

  private estimateIndexSize(): number {
    let size = 0;

    // 估算文档大小
    for (const doc of this.documents.values()) {
      size += JSON.stringify(doc).length * 2;
    }

    // 估算索引大小
    for (const [term, docIds] of this.termIndex.entries()) {
      size += term.length * 2 + docIds.size * 36;
    }

    return size;
  }
}
```

### 步骤3：实现搜索管理器
**文件**: `src/business/managers/SearchManager.ts`

```typescript
import { ISearchManager, SearchQuery, AdvancedSearchCriteria, SearchResult, SearchSuggestion, SearchHistoryItem, SearchConfig, IndexStats } from '../interfaces/ISearchManager';
import { Memory, MemoryType } from '../../data/entities/Memory';
import { IRepository } from '../../data/interfaces/IRepository';
import { PageRequest, PageResult, DataResult, SearchCriteria, FilterOperator } from '../../data/types/DataTypes';
import { SearchIndex, InMemorySearchIndex, IndexDocument } from '../search/SearchIndex';
import { logger } from '../../infrastructure/utils/Logger';

export class SearchManager implements ISearchManager {
  private searchIndex: SearchIndex;
  private searchHistory: SearchHistoryItem[] = [];
  private config: SearchConfig = {
    enableFuzzySearch: true,
    fuzzyThreshold: 0.7,
    maxSuggestions: 10,
    maxHistoryItems: 50,
    highlightEnabled: true,
    facetsEnabled: true,
    indexUpdateInterval: 60000
  };

  constructor(
    private memoryRepository: IRepository<Memory>
  ) {
    this.searchIndex = new InMemorySearchIndex();
    this.initializeIndex();
  }

  async search(query: SearchQuery): Promise<DataResult<SearchResult>> {
    try {
      const startTime = Date.now();

      // 添加到搜索历史
      if (query.keyword) {
        await this.addToSearchHistory(query.keyword);
      }

      // 构建搜索条件
      const searchCriteria = this.buildSearchCriteria(query);

      // 执行搜索
      const repositoryResult = await this.memoryRepository.findAll(searchCriteria);
      if (!repositoryResult.success) {
        return { success: false, error: repositoryResult.error };
      }

      let memories = repositoryResult.data || [];

      // 如果有关键词，使用索引搜索进行排序和高亮
      if (query.keyword) {
        const indexResults = this.searchIndex.search(query.keyword, {
          fuzzy: this.config.enableFuzzySearch,
          fuzzyThreshold: this.config.fuzzyThreshold
        });

        // 按索引结果重新排序
        const indexMap = new Map(indexResults.map(r => [r.id, r]));
        memories = memories
          .filter(m => indexMap.has(m.id))
          .sort((a, b) => (indexMap.get(b.id)?.score || 0) - (indexMap.get(a.id)?.score || 0));
      }

      const searchTime = Date.now() - startTime;

      const result: SearchResult = {
        memories,
        totalCount: memories.length,
        searchTime,
        highlights: this.generateHighlights(memories, query.keyword),
        facets: this.generateFacets(memories)
      };

      return { success: true, data: result };
    } catch (error) {
      logger.error('搜索失败:', error);
      return { success: false, error: error as Error };
    }
  }

  async searchPage(query: SearchQuery, page: PageRequest): Promise<DataResult<PageResult<Memory>>> {
    try {
      const searchResult = await this.search(query);
      if (!searchResult.success) {
        return { success: false, error: searchResult.error };
      }

      const memories = searchResult.data!.memories;
      const startIndex = page.page * page.size;
      const endIndex = startIndex + page.size;
      const pagedMemories = memories.slice(startIndex, endIndex);

      const pageResult: PageResult<Memory> = {
        content: pagedMemories,
        totalElements: memories.length,
        totalPages: Math.ceil(memories.length / page.size),
        page: page.page,
        size: page.size,
        first: page.page === 0,
        last: startIndex + page.size >= memories.length
      };

      return { success: true, data: pageResult };
    } catch (error) {
      logger.error('分页搜索失败:', error);
      return { success: false, error: error as Error };
    }
  }

  async quickSearch(keyword: string, limit = 10): Promise<DataResult<Memory[]>> {
    try {
      const query: SearchQuery = { keyword };
      const result = await this.search(query);

      if (result.success && result.data) {
        result.data.memories = result.data.memories.slice(0, limit);
      }

      return { success: result.success, data: result.data?.memories, error: result.error };
    } catch (error) {
      logger.error('快速搜索失败:', error);
      return { success: false, error: error as Error };
    }
  }

  async searchSuggestions(keyword: string, limit = 10): Promise<DataResult<SearchSuggestion[]>> {
    try {
      const suggestions: SearchSuggestion[] = [];

      // 获取所有内存用于生成建议
      const allMemoriesResult = await this.memoryRepository.findAll();
      if (!allMemoriesResult.success) {
        return { success: false, error: allMemoriesResult.error };
      }

      const memories = allMemoriesResult.data || [];
      const lowerKeyword = keyword.toLowerCase();

      // 标题建议
      const titleSuggestions = memories
        .filter(m => m.title.toLowerCase().includes(lowerKeyword))
        .map(m => ({
          text: m.title,
          type: 'title' as const,
          count: 1
        }));

      // 标签建议
      const tagCounts = new Map<string, number>();
      memories.forEach(m => {
        m.tags.forEach(tag => {
          if (tag.toLowerCase().includes(lowerKeyword)) {
            tagCounts.set(tag, (tagCounts.get(tag) || 0) + 1);
          }
        });
      });

      const tagSuggestions = Array.from(tagCounts.entries())
        .map(([tag, count]) => ({
          text: tag,
          type: 'tag' as const,
          count
        }));

      // 关键词建议（基于内容）
      const keywordSuggestions = this.extractKeywordSuggestions(memories, lowerKeyword);

      // 合并和排序建议
      suggestions.push(...titleSuggestions, ...tagSuggestions, ...keywordSuggestions);
      suggestions.sort((a, b) => b.count - a.count);

      // 去重并限制数量
      const uniqueSuggestions = suggestions
        .filter((suggestion, index, array) =>
          array.findIndex(s => s.text === suggestion.text) === index
        )
        .slice(0, limit);

      return { success: true, data: uniqueSuggestions };
    } catch (error) {
      logger.error('获取搜索建议失败:', error);
      return { success: false, error: error as Error };
    }
  }

  async advancedSearch(criteria: AdvancedSearchCriteria): Promise<DataResult<SearchResult>> {
    try {
      // 构建高级搜索条件
      const searchCriteria = this.buildAdvancedSearchCriteria(criteria);

      const repositoryResult = await this.memoryRepository.findAll(searchCriteria);
      if (!repositoryResult.success) {
        return { success: false, error: repositoryResult.error };
      }

      let memories = repositoryResult.data || [];

      // 应用高级过滤
      memories = this.applyAdvancedFilters(memories, criteria);

      const result: SearchResult = {
        memories,
        totalCount: memories.length,
        searchTime: 0,
        highlights: [],
        facets: this.generateFacets(memories)
      };

      return { success: true, data: result };
    } catch (error) {
      logger.error('高级搜索失败:', error);
      return { success: false, error: error as Error };
    }
  }

  async getSearchHistory(limit = 20): Promise<DataResult<SearchHistoryItem[]>> {
    try {
      const history = this.searchHistory
        .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
        .slice(0, limit);

      return { success: true, data: history };
    } catch (error) {
      logger.error('获取搜索历史失败:', error);
      return { success: false, error: error as Error };
    }
  }

  async addToSearchHistory(query: string): Promise<void> {
    try {
      // 检查是否已存在相同查询
      const existingIndex = this.searchHistory.findIndex(item => item.query === query);

      if (existingIndex > -1) {
        // 更新现有记录
        this.searchHistory[existingIndex].timestamp = new Date();
      } else {
        // 添加新记录
        const historyItem: SearchHistoryItem = {
          id: this.generateHistoryId(),
          query,
          timestamp: new Date(),
          resultCount: 0 // 这里可以传入实际的结果数量
        };

        this.searchHistory.unshift(historyItem);

        // 限制历史记录数量
        if (this.searchHistory.length > this.config.maxHistoryItems) {
          this.searchHistory = this.searchHistory.slice(0, this.config.maxHistoryItems);
        }
      }
    } catch (error) {
      logger.error('添加搜索历史失败:', error);
    }
  }

  async clearSearchHistory(): Promise<void> {
    this.searchHistory = [];
  }

  async removeFromSearchHistory(id: string): Promise<void> {
    const index = this.searchHistory.findIndex(item => item.id === id);
    if (index > -1) {
      this.searchHistory.splice(index, 1);
    }
  }

  async buildIndex(): Promise<DataResult<IndexStats>> {
    try {
      const startTime = Date.now();

      // 清空现有索引
      this.searchIndex.clear();

      // 获取所有内存
      const allMemoriesResult = await this.memoryRepository.findAll();
      if (!allMemoriesResult.success) {
        return { success: false, error: allMemoriesResult.error };
      }

      const memories = allMemoriesResult.data || [];

      // 构建索引
      for (const memory of memories) {
        const document: IndexDocument = {
          id: memory.id,
          title: memory.title,
          content: memory.content,
          tags: memory.tags,
          type: memory.type,
          category: memory.categoryId,
          createdAt: memory.createdAt,
          metadata: memory.metadata
        };

        this.searchIndex.addDocument(memory.id, document);
      }

      const buildTime = Date.now() - startTime;
      const stats = this.searchIndex.getStats();
      stats.buildTime = buildTime;

      logger.info(`搜索索引构建完成，耗时: ${buildTime}ms，文档数: ${stats.totalDocuments}`);

      return { success: true, data: stats };
    } catch (error) {
      logger.error('构建搜索索引失败:', error);
      return { success: false, error: error as Error };
    }
  }

  async updateIndex(memoryIds: string[]): Promise<DataResult<number>> {
    try {
      let updatedCount = 0;

      for (const memoryId of memoryIds) {
        const memoryResult = await this.memoryRepository.findById(memoryId);

        if (memoryResult.success && memoryResult.data) {
          const memory = memoryResult.data;
          const document: IndexDocument = {
            id: memory.id,
            title: memory.title,
            content: memory.content,
            tags: memory.tags,
            type: memory.type,
            category: memory.categoryId,
            createdAt: memory.createdAt,
            metadata: memory.metadata
          };

          this.searchIndex.updateDocument(memory.id, document);
          updatedCount++;
        } else {
          // 内存不存在，从索引中移除
          this.searchIndex.removeDocument(memoryId);
        }
      }

      return { success: true, data: updatedCount };
    } catch (error) {
      logger.error('更新搜索索引失败:', error);
      return { success: false, error: error as Error };
    }
  }

  async getIndexStats(): Promise<DataResult<IndexStats>> {
    try {
      const stats = this.searchIndex.getStats();
      return { success: true, data: stats };
    } catch (error) {
      logger.error('获取索引统计失败:', error);
      return { success: false, error: error as Error };
    }
  }

  setSearchConfig(config: SearchConfig): void {
    this.config = { ...this.config, ...config };
  }

  getSearchConfig(): SearchConfig {
    return { ...this.config };
  }

  // 私有方法
  private async initializeIndex(): Promise<void> {
    try {
      await this.buildIndex();
    } catch (error) {
      logger.error('初始化搜索索引失败:', error);
    }
  }

  private buildSearchCriteria(query: SearchQuery): SearchCriteria<Memory> {
    const criteria: SearchCriteria<Memory> = {
      filters: [],
      search: query.keyword
    };

    if (query.type) {
      criteria.filters!.push({
        property: 'type',
        operator: FilterOperator.EQUALS,
        value: query.type
      });
    }

    if (query.tags && query.tags.length > 0) {
      criteria.filters!.push({
        property: 'tags',
        operator: FilterOperator.IN,
        values: query.tags
      });
    }

    if (query.categoryId) {
      criteria.filters!.push({
        property: 'categoryId',
        operator: FilterOperator.EQUALS,
        value: query.categoryId
      });
    }

    if (!query.includeArchived) {
      criteria.filters!.push({
        property: 'isArchived',
        operator: FilterOperator.EQUALS,
        value: false
      });
    }

    if (!query.includePrivate) {
      criteria.filters!.push({
        property: 'isPrivate',
        operator: FilterOperator.EQUALS,
        value: false
      });
    }

    if (query.dateRange) {
      criteria.filters!.push({
        property: 'createdAt',
        operator: FilterOperator.BETWEEN,
        values: [query.dateRange.start, query.dateRange.end]
      });
    }

    if (query.sortBy) {
      criteria.sort = [{
        property: query.sortBy.field === 'date' ? 'createdAt' : query.sortBy.field,
        direction: query.sortBy.direction === 'asc' ? 'ASC' : 'DESC'
      }];
    }

    return criteria;
  }

  private buildAdvancedSearchCriteria(criteria: AdvancedSearchCriteria): SearchCriteria<Memory> {
    const searchCriteria = this.buildSearchCriteria(criteria);

    // 高级搜索的特殊处理在 applyAdvancedFilters 中进行
    return searchCriteria;
  }

  private applyAdvancedFilters(memories: Memory[], criteria: AdvancedSearchCriteria): Memory[] {
    let filtered = memories;

    if (criteria.minLength !== undefined) {
      filtered = filtered.filter(m => m.content.length >= criteria.minLength!);
    }

    if (criteria.maxLength !== undefined) {
      filtered = filtered.filter(m => m.content.length <= criteria.maxLength!);
    }

    if (criteria.hasAttachments !== undefined) {
      filtered = filtered.filter(m =>
        criteria.hasAttachments ? m.attachments.length > 0 : m.attachments.length === 0
      );
    }

    if (criteria.attachmentTypes && criteria.attachmentTypes.length > 0) {
      filtered = filtered.filter(m =>
        m.attachments.some(att => criteria.attachmentTypes!.includes(att.type))
      );
    }

    return filtered;
  }

  private generateHighlights(memories: Memory[], keyword?: string): any[] {
    if (!keyword || !this.config.highlightEnabled) {
      return [];
    }

    // 简化的高亮生成
    return memories.map(memory => ({
      memoryId: memory.id,
      field: 'content',
      fragments: [memory.content.substring(0, 200) + '...']
    }));
  }

  private generateFacets(memories: Memory[]): any[] {
    if (!this.config.facetsEnabled) {
      return [];
    }

    const facets: any[] = [];

    // 类型分面
    const typeCounts = new Map<string, number>();
    memories.forEach(m => {
      typeCounts.set(m.type, (typeCounts.get(m.type) || 0) + 1);
    });

    facets.push({
      field: 'type',
      values: Array.from(typeCounts.entries()).map(([value, count]) => ({ value, count }))
    });

    // 标签分面
    const tagCounts = new Map<string, number>();
    memories.forEach(m => {
      m.tags.forEach(tag => {
        tagCounts.set(tag, (tagCounts.get(tag) || 0) + 1);
      });
    });

    const topTags = Array.from(tagCounts.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 10)
      .map(([value, count]) => ({ value, count }));

    facets.push({
      field: 'tags',
      values: topTags
    });

    return facets;
  }

  private extractKeywordSuggestions(memories: Memory[], keyword: string): SearchSuggestion[] {
    const suggestions: SearchSuggestion[] = [];
    const wordCounts = new Map<string, number>();

    memories.forEach(memory => {
      const words = memory.content.toLowerCase().split(/\s+/);
      words.forEach(word => {
        if (word.includes(keyword) && word.length > 2) {
          wordCounts.set(word, (wordCounts.get(word) || 0) + 1);
        }
      });
    });

    Array.from(wordCounts.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 5)
      .forEach(([word, count]) => {
        suggestions.push({
          text: word,
          type: 'keyword',
          count
        });
      });

    return suggestions;
  }

  private generateHistoryId(): string {
    return `search_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}
```

## ✅ 验收标准

### 功能验收
- [ ] ISearchManager接口定义完整
- [ ] SearchManager实现所有接口方法
- [ ] 支持基础搜索和高级搜索
- [ ] 支持搜索建议和历史管理
- [ ] 支持搜索索引构建和更新
- [ ] 支持搜索结果高亮和分面

### 质量验收
- [ ] TypeScript编译无错误
- [ ] 搜索性能良好
- [ ] 索引构建效率高
- [ ] 错误处理完善

## 🔄 幂等性保证

### 执行前检查
```bash
#!/bin/bash
echo "🔍 检查任务3.4执行状态..."

if [ ! -f "src/business/managers/ConfigManager.ts" ]; then
  echo "❌ 依赖任务Task-3.3未完成，请先执行Task-3.3"
  exit 1
fi

if [ -f "src/business/managers/SearchManager.ts" ]; then
  echo "⚠️  搜索管理器已存在，任务可能已完成"
  read -p "是否继续？(y/N): " confirm
  if [ "$confirm" != "y" ]; then
    exit 1
  fi
fi

echo "✅ 前置检查完成，可以执行任务3.4"
```

### 执行后验证
```bash
#!/bin/bash
echo "🔍 验证任务3.4执行结果..."

files=(
  "src/business/interfaces/ISearchManager.ts"
  "src/business/search/SearchIndex.ts"
  "src/business/managers/SearchManager.ts"
)

all_created=true
for file in "${files[@]}"; do
  if [ -f "$file" ]; then
    echo "✅ $file 创建成功"
  else
    echo "❌ $file 创建失败"
    all_created=false
  fi
done

if npm run type-check > /dev/null 2>&1; then
  echo "✅ TypeScript编译检查通过"
else
  echo "❌ TypeScript编译检查失败"
  all_created=false
fi

if [ "$all_created" = true ]; then
  echo "🎉 任务3.4执行成功！"
  exit 0
else
  echo "💥 任务3.4执行失败，请检查错误信息"
  exit 1
fi
```

## 📝 完成确认

任务完成后，请在检查清单中标记：
- [ ] 任务3.4已完成
- [ ] 搜索管理器接口定义完成
- [ ] 搜索索引实现完成
- [ ] 搜索管理器实现完成
- [ ] 搜索功能测试通过
- [ ] 准备执行任务3.5

## 🔗 相关链接

- [上一个任务：Task-3.3 实现配置管理器](./task-3.3-config-manager.md)
- [下一个任务：Task-3.5 实现迁移管理器](./task-3.5-migration-manager.md)
- [重构检查清单](../00-refactoring-checklist.md)
```