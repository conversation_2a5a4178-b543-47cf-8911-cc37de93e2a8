# 任务3.6：实现安全管理器

## 📋 任务概述

**任务ID**: Task-3.6
**任务名称**: 实现安全管理器
**所属阶段**: 阶段3 - 业务层重构
**预计时间**: 3小时
**依赖任务**: Task-3.5 (实现迁移管理器)
**后续任务**: Task-3.7 (创建业务层测试页面)

## 🎯 任务目标

实现安全管理器，提供数据安全和权限控制功能，包括数据加密、访问控制、安全审计和密钥管理。

## 🛠️ 实施步骤

### 步骤1：定义安全管理器接口
**文件**: `src/business/interfaces/ISecurityManager.ts`

```typescript
import { DataResult } from '../../data/types/DataTypes';

export interface ISecurityManager {
  // 认证和授权
  authenticate(credentials: AuthCredentials): Promise<DataResult<AuthResult>>;
  authorize(action: string, resource: string): Promise<DataResult<boolean>>;
  logout(): Promise<void>;

  // 密钥管理
  generateKey(type: KeyType): Promise<DataResult<CryptoKey>>;
  storeKey(keyId: string, key: CryptoKey): Promise<DataResult<boolean>>;
  retrieveKey(keyId: string): Promise<DataResult<CryptoKey | null>>;
  deleteKey(keyId: string): Promise<DataResult<boolean>>;

  // 数据加密
  encryptData(data: any, keyId?: string): Promise<DataResult<EncryptedData>>;
  decryptData(encryptedData: EncryptedData, keyId?: string): Promise<DataResult<any>>;

  // 安全审计
  logSecurityEvent(event: SecurityEvent): Promise<void>;
  getSecurityLogs(filter?: SecurityLogFilter): Promise<DataResult<SecurityEvent[]>>;

  // 安全策略
  setSecurityPolicy(policy: SecurityPolicy): Promise<void>;
  getSecurityPolicy(): Promise<SecurityPolicy>;
  validateSecurityPolicy(): Promise<DataResult<PolicyValidationResult>>;

  // 安全状态
  getSecurityStatus(): Promise<SecurityStatus>;
  performSecurityScan(): Promise<DataResult<SecurityScanResult>>;
}

export interface AuthCredentials {
  type: 'password' | 'biometric' | 'token';
  value: string;
  metadata?: Record<string, any>;
}

export interface AuthResult {
  success: boolean;
  token?: string;
  expiresAt?: Date;
  permissions: string[];
  user: UserInfo;
}

export interface UserInfo {
  id: string;
  name: string;
  email?: string;
  roles: string[];
  lastLogin?: Date;
}

export enum KeyType {
  ENCRYPTION = 'encryption',
  SIGNING = 'signing',
  MASTER = 'master'
}

export interface EncryptedData {
  data: string;
  iv: string;
  keyId: string;
  algorithm: string;
  timestamp: Date;
}

export interface SecurityEvent {
  id: string;
  type: SecurityEventType;
  severity: SecuritySeverity;
  message: string;
  details: Record<string, any>;
  timestamp: Date;
  userId?: string;
  ipAddress?: string;
  userAgent?: string;
}

export enum SecurityEventType {
  LOGIN_SUCCESS = 'login_success',
  LOGIN_FAILURE = 'login_failure',
  LOGOUT = 'logout',
  DATA_ACCESS = 'data_access',
  DATA_MODIFICATION = 'data_modification',
  PERMISSION_DENIED = 'permission_denied',
  KEY_GENERATED = 'key_generated',
  KEY_ACCESSED = 'key_accessed',
  ENCRYPTION_PERFORMED = 'encryption_performed',
  DECRYPTION_PERFORMED = 'decryption_performed',
  SECURITY_VIOLATION = 'security_violation'
}

export enum SecuritySeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

export interface SecurityLogFilter {
  startDate?: Date;
  endDate?: Date;
  eventTypes?: SecurityEventType[];
  severity?: SecuritySeverity;
  userId?: string;
  limit?: number;
}

export interface SecurityPolicy {
  passwordPolicy: PasswordPolicy;
  encryptionPolicy: EncryptionPolicy;
  accessPolicy: AccessPolicy;
  auditPolicy: AuditPolicy;
  sessionPolicy: SessionPolicy;
}

export interface PasswordPolicy {
  minLength: number;
  requireUppercase: boolean;
  requireLowercase: boolean;
  requireNumbers: boolean;
  requireSpecialChars: boolean;
  maxAge: number; // days
  preventReuse: number; // number of previous passwords
}

export interface EncryptionPolicy {
  algorithm: string;
  keySize: number;
  keyRotationInterval: number; // days
  encryptAtRest: boolean;
  encryptInTransit: boolean;
}

export interface AccessPolicy {
  defaultPermissions: string[];
  roleBasedAccess: boolean;
  sessionTimeout: number; // minutes
  maxConcurrentSessions: number;
}

export interface AuditPolicy {
  enabled: boolean;
  logLevel: SecuritySeverity;
  retentionPeriod: number; // days
  realTimeAlerts: boolean;
}

export interface SessionPolicy {
  timeout: number; // minutes
  extendOnActivity: boolean;
  requireReauth: boolean;
  maxIdleTime: number; // minutes
}

export interface PolicyValidationResult {
  valid: boolean;
  warnings: string[];
  errors: string[];
  recommendations: string[];
}

export interface SecurityStatus {
  authenticated: boolean;
  currentUser?: UserInfo;
  sessionExpiresAt?: Date;
  encryptionEnabled: boolean;
  auditingEnabled: boolean;
  lastSecurityScan?: Date;
  threatLevel: ThreatLevel;
}

export enum ThreatLevel {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

export interface SecurityScanResult {
  scanId: string;
  timestamp: Date;
  duration: number;
  threatsFound: SecurityThreat[];
  recommendations: SecurityRecommendation[];
  overallScore: number; // 0-100
}

export interface SecurityThreat {
  id: string;
  type: string;
  severity: SecuritySeverity;
  description: string;
  affected: string[];
  mitigation: string;
}

export interface SecurityRecommendation {
  id: string;
  priority: 'low' | 'medium' | 'high';
  title: string;
  description: string;
  action: string;
}
```

### 步骤2：实现安全管理器
**文件**: `src/business/managers/SecurityManager.ts`

```typescript
import { ISecurityManager, AuthCredentials, AuthResult, SecurityEvent, SecurityPolicy, SecurityStatus } from '../interfaces/ISecurityManager';
import { ICryptoService } from '../../infrastructure/interfaces/ICryptoService';
import { DataResult } from '../../data/types/DataTypes';
import { IEventEmitter } from '../../infrastructure/utils/EventEmitter';
import { logger } from '../../infrastructure/utils/Logger';

export class SecurityManager implements ISecurityManager {
  private currentUser?: any;
  private sessionToken?: string;
  private sessionExpiresAt?: Date;
  private securityLogs: SecurityEvent[] = [];
  private keyStore = new Map<string, CryptoKey>();
  private securityPolicy: SecurityPolicy = this.getDefaultSecurityPolicy();

  constructor(
    private cryptoService: ICryptoService,
    private eventEmitter: IEventEmitter
  ) {}

  async authenticate(credentials: AuthCredentials): Promise<DataResult<AuthResult>> {
    try {
      // 简化的认证实现
      const isValid = await this.validateCredentials(credentials);

      if (isValid) {
        const user = {
          id: 'user_001',
          name: 'Test User',
          email: '<EMAIL>',
          roles: ['user'],
          lastLogin: new Date()
        };

        const token = await this.generateSessionToken();
        const expiresAt = new Date(Date.now() + this.securityPolicy.sessionPolicy.timeout * 60000);

        this.currentUser = user;
        this.sessionToken = token;
        this.sessionExpiresAt = expiresAt;

        const authResult: AuthResult = {
          success: true,
          token,
          expiresAt,
          permissions: ['read', 'write'],
          user
        };

        await this.logSecurityEvent({
          id: this.generateEventId(),
          type: 'login_success' as any,
          severity: 'low' as any,
          message: '用户登录成功',
          details: { userId: user.id },
          timestamp: new Date()
        });

        this.eventEmitter.emit('userAuthenticated', authResult);

        return { success: true, data: authResult };
      } else {
        await this.logSecurityEvent({
          id: this.generateEventId(),
          type: 'login_failure' as any,
          severity: 'medium' as any,
          message: '用户登录失败',
          details: { credentialType: credentials.type },
          timestamp: new Date()
        });

        return {
          success: false,
          error: new Error('认证失败')
        };
      }
    } catch (error) {
      logger.error('认证失败:', error);
      return { success: false, error: error as Error };
    }
  }

  async authorize(action: string, resource: string): Promise<DataResult<boolean>> {
    try {
      if (!this.currentUser || !this.sessionToken) {
        return { success: false, error: new Error('用户未认证') };
      }

      if (this.sessionExpiresAt && new Date() > this.sessionExpiresAt) {
        await this.logout();
        return { success: false, error: new Error('会话已过期') };
      }

      // 简化的授权检查
      const hasPermission = this.checkPermission(action, resource);

      if (!hasPermission) {
        await this.logSecurityEvent({
          id: this.generateEventId(),
          type: 'permission_denied' as any,
          severity: 'medium' as any,
          message: '权限被拒绝',
          details: { action, resource, userId: this.currentUser.id },
          timestamp: new Date()
        });
      }

      return { success: true, data: hasPermission };
    } catch (error) {
      logger.error('授权检查失败:', error);
      return { success: false, error: error as Error };
    }
  }

  async logout(): Promise<void> {
    try {
      if (this.currentUser) {
        await this.logSecurityEvent({
          id: this.generateEventId(),
          type: 'logout' as any,
          severity: 'low' as any,
          message: '用户登出',
          details: { userId: this.currentUser.id },
          timestamp: new Date()
        });

        this.eventEmitter.emit('userLoggedOut', { userId: this.currentUser.id });
      }

      this.currentUser = undefined;
      this.sessionToken = undefined;
      this.sessionExpiresAt = undefined;
    } catch (error) {
      logger.error('登出失败:', error);
    }
  }

  async generateKey(type: any): Promise<DataResult<CryptoKey>> {
    try {
      const key = await this.cryptoService.generateKey();
      const keyId = this.generateKeyId();

      // 在实际实现中，这里需要转换为CryptoKey
      const cryptoKey = key as any; // 简化处理

      await this.storeKey(keyId, cryptoKey);

      await this.logSecurityEvent({
        id: this.generateEventId(),
        type: 'key_generated' as any,
        severity: 'low' as any,
        message: '密钥生成',
        details: { keyType: type, keyId },
        timestamp: new Date()
      });

      return { success: true, data: cryptoKey };
    } catch (error) {
      logger.error('生成密钥失败:', error);
      return { success: false, error: error as Error };
    }
  }

  async storeKey(keyId: string, key: CryptoKey): Promise<DataResult<boolean>> {
    try {
      this.keyStore.set(keyId, key);
      return { success: true, data: true };
    } catch (error) {
      logger.error(`存储密钥失败 [${keyId}]:`, error);
      return { success: false, error: error as Error };
    }
  }

  async retrieveKey(keyId: string): Promise<DataResult<CryptoKey | null>> {
    try {
      const key = this.keyStore.get(keyId) || null;

      if (key) {
        await this.logSecurityEvent({
          id: this.generateEventId(),
          type: 'key_accessed' as any,
          severity: 'low' as any,
          message: '密钥访问',
          details: { keyId },
          timestamp: new Date()
        });
      }

      return { success: true, data: key };
    } catch (error) {
      logger.error(`获取密钥失败 [${keyId}]:`, error);
      return { success: false, error: error as Error };
    }
  }

  async deleteKey(keyId: string): Promise<DataResult<boolean>> {
    try {
      const deleted = this.keyStore.delete(keyId);
      return { success: true, data: deleted };
    } catch (error) {
      logger.error(`删除密钥失败 [${keyId}]:`, error);
      return { success: false, error: error as Error };
    }
  }

  async encryptData(data: any, keyId?: string): Promise<DataResult<any>> {
    try {
      const key = keyId ? await this.retrieveKey(keyId) : await this.generateKey('encryption' as any);

      if (!key.success || !key.data) {
        return { success: false, error: new Error('无法获取加密密钥') };
      }

      const encryptResult = await this.cryptoService.encrypt(JSON.stringify(data));

      const encryptedData: any = {
        data: encryptResult.encryptedData,
        iv: encryptResult.iv,
        keyId: keyId || 'default',
        algorithm: 'AES-GCM',
        timestamp: new Date()
      };

      await this.logSecurityEvent({
        id: this.generateEventId(),
        type: 'encryption_performed' as any,
        severity: 'low' as any,
        message: '数据加密',
        details: { keyId: keyId || 'default' },
        timestamp: new Date()
      });

      return { success: true, data: encryptedData };
    } catch (error) {
      logger.error('数据加密失败:', error);
      return { success: false, error: error as Error };
    }
  }

  async decryptData(encryptedData: any, keyId?: string): Promise<DataResult<any>> {
    try {
      const key = await this.retrieveKey(keyId || encryptedData.keyId);

      if (!key.success || !key.data) {
        return { success: false, error: new Error('无法获取解密密钥') };
      }

      const decryptResult = await this.cryptoService.decrypt(`${encryptedData.iv}:${encryptedData.data}`);
      const decryptedData = JSON.parse(new TextDecoder().decode(decryptResult.decryptedData as ArrayBuffer));

      await this.logSecurityEvent({
        id: this.generateEventId(),
        type: 'decryption_performed' as any,
        severity: 'low' as any,
        message: '数据解密',
        details: { keyId: keyId || encryptedData.keyId },
        timestamp: new Date()
      });

      return { success: true, data: decryptedData };
    } catch (error) {
      logger.error('数据解密失败:', error);
      return { success: false, error: error as Error };
    }
  }

  async logSecurityEvent(event: SecurityEvent): Promise<void> {
    try {
      this.securityLogs.push(event);

      // 限制日志数量
      if (this.securityLogs.length > 1000) {
        this.securityLogs = this.securityLogs.slice(-1000);
      }

      // 发射事件
      this.eventEmitter.emit('securityEvent', event);

      // 检查是否需要实时告警
      if (this.securityPolicy.auditPolicy.realTimeAlerts &&
          (event.severity === 'high' as any || event.severity === 'critical' as any)) {
        this.eventEmitter.emit('securityAlert', event);
      }
    } catch (error) {
      logger.error('记录安全事件失败:', error);
    }
  }

  async getSecurityLogs(filter?: any): Promise<DataResult<SecurityEvent[]>> {
    try {
      let logs = [...this.securityLogs];

      if (filter) {
        logs = logs.filter(log => {
          if (filter.startDate && log.timestamp < filter.startDate) return false;
          if (filter.endDate && log.timestamp > filter.endDate) return false;
          if (filter.eventTypes && !filter.eventTypes.includes(log.type)) return false;
          if (filter.severity && log.severity !== filter.severity) return false;
          if (filter.userId && log.userId !== filter.userId) return false;
          return true;
        });

        if (filter.limit) {
          logs = logs.slice(0, filter.limit);
        }
      }

      return { success: true, data: logs };
    } catch (error) {
      logger.error('获取安全日志失败:', error);
      return { success: false, error: error as Error };
    }
  }

  async setSecurityPolicy(policy: SecurityPolicy): Promise<void> {
    this.securityPolicy = { ...this.securityPolicy, ...policy };
    // 保存到存储
    localStorage.setItem('securityPolicy', JSON.stringify(this.securityPolicy));
  }

  async getSecurityPolicy(): Promise<SecurityPolicy> {
    return { ...this.securityPolicy };
  }

  async validateSecurityPolicy(): Promise<DataResult<any>> {
    try {
      const warnings: string[] = [];
      const errors: string[] = [];
      const recommendations: string[] = [];

      // 验证密码策略
      if (this.securityPolicy.passwordPolicy.minLength < 8) {
        warnings.push('密码最小长度建议至少8位');
      }

      // 验证加密策略
      if (!this.securityPolicy.encryptionPolicy.encryptAtRest) {
        recommendations.push('建议启用静态数据加密');
      }

      // 验证会话策略
      if (this.securityPolicy.sessionPolicy.timeout > 480) { // 8小时
        warnings.push('会话超时时间过长，建议缩短');
      }

      const result: any = {
        valid: errors.length === 0,
        warnings,
        errors,
        recommendations
      };

      return { success: true, data: result };
    } catch (error) {
      logger.error('验证安全策略失败:', error);
      return { success: false, error: error as Error };
    }
  }

  async getSecurityStatus(): Promise<SecurityStatus> {
    return {
      authenticated: !!this.currentUser,
      currentUser: this.currentUser,
      sessionExpiresAt: this.sessionExpiresAt,
      encryptionEnabled: this.securityPolicy.encryptionPolicy.encryptAtRest,
      auditingEnabled: this.securityPolicy.auditPolicy.enabled,
      lastSecurityScan: undefined,
      threatLevel: 'low' as any
    };
  }

  async performSecurityScan(): Promise<DataResult<any>> {
    try {
      const scanId = this.generateScanId();
      const startTime = Date.now();

      // 模拟安全扫描
      const threats: any[] = [];
      const recommendations: any[] = [];

      // 检查密码策略
      if (this.securityPolicy.passwordPolicy.minLength < 12) {
        recommendations.push({
          id: 'pwd_001',
          priority: 'medium',
          title: '增强密码策略',
          description: '当前密码最小长度较短',
          action: '将密码最小长度设置为12位或更多'
        });
      }

      // 检查会话配置
      if (this.securityPolicy.sessionPolicy.timeout > 240) {
        recommendations.push({
          id: 'session_001',
          priority: 'low',
          title: '优化会话超时',
          description: '会话超时时间较长',
          action: '考虑缩短会话超时时间'
        });
      }

      const scanResult: any = {
        scanId,
        timestamp: new Date(),
        duration: Date.now() - startTime,
        threatsFound: threats,
        recommendations,
        overallScore: 85 // 0-100
      };

      await this.logSecurityEvent({
        id: this.generateEventId(),
        type: 'security_violation' as any, // 使用现有的事件类型
        severity: 'low' as any,
        message: '安全扫描完成',
        details: { scanId, score: scanResult.overallScore },
        timestamp: new Date()
      });

      return { success: true, data: scanResult };
    } catch (error) {
      logger.error('安全扫描失败:', error);
      return { success: false, error: error as Error };
    }
  }

  // 私有辅助方法
  private async validateCredentials(credentials: AuthCredentials): Promise<boolean> {
    // 简化的凭证验证
    switch (credentials.type) {
      case 'password':
        return credentials.value === 'password123'; // 仅用于演示
      case 'token':
        return credentials.value.length > 10;
      default:
        return false;
    }
  }

  private async generateSessionToken(): Promise<string> {
    const randomBytes = new Uint8Array(32);
    crypto.getRandomValues(randomBytes);
    return Array.from(randomBytes, byte => byte.toString(16).padStart(2, '0')).join('');
  }

  private checkPermission(action: string, resource: string): boolean {
    // 简化的权限检查
    if (!this.currentUser) return false;

    // 管理员拥有所有权限
    if (this.currentUser.roles.includes('admin')) return true;

    // 基础权限检查
    const allowedActions = ['read', 'write'];
    return allowedActions.includes(action);
  }

  private getDefaultSecurityPolicy(): SecurityPolicy {
    return {
      passwordPolicy: {
        minLength: 8,
        requireUppercase: true,
        requireLowercase: true,
        requireNumbers: true,
        requireSpecialChars: false,
        maxAge: 90,
        preventReuse: 5
      },
      encryptionPolicy: {
        algorithm: 'AES-256-GCM',
        keySize: 256,
        keyRotationInterval: 30,
        encryptAtRest: true,
        encryptInTransit: true
      },
      accessPolicy: {
        defaultPermissions: ['read'],
        roleBasedAccess: true,
        sessionTimeout: 240,
        maxConcurrentSessions: 3
      },
      auditPolicy: {
        enabled: true,
        logLevel: 'medium' as any,
        retentionPeriod: 90,
        realTimeAlerts: true
      },
      sessionPolicy: {
        timeout: 240,
        extendOnActivity: true,
        requireReauth: false,
        maxIdleTime: 30
      }
    };
  }

  private generateEventId(): string {
    return `event_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateKeyId(): string {
    return `key_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateScanId(): string {
    return `scan_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}
```

## ✅ 验收标准

### 功能验收
- [ ] ISecurityManager接口定义完整
- [ ] SecurityManager实现所有接口方法
- [ ] 支持认证和授权功能
- [ ] 支持密钥管理和数据加密
- [ ] 支持安全审计和日志记录
- [ ] 支持安全策略管理

### 质量验收
- [ ] TypeScript编译无错误
- [ ] 安全功能正确实现
- [ ] 错误处理完善
- [ ] 安全事件记录完整

### 安全验收
- [ ] 密钥安全存储
- [ ] 数据加密解密正确
- [ ] 权限控制有效
- [ ] 安全审计完整

## 🔄 幂等性保证

### 执行前检查
```bash
#!/bin/bash
echo "🔍 检查任务3.6执行状态..."

if [ ! -f "src/business/managers/MigrationManager.ts" ]; then
  echo "❌ 依赖任务Task-3.5未完成，请先执行Task-3.5"
  exit 1
fi

if [ -f "src/business/managers/SecurityManager.ts" ]; then
  echo "⚠️  安全管理器已存在，任务可能已完成"
  read -p "是否继续？(y/N): " confirm
  if [ "$confirm" != "y" ]; then
    exit 1
  fi
fi

echo "✅ 前置检查完成，可以执行任务3.6"
```

### 执行后验证
```bash
#!/bin/bash
echo "🔍 验证任务3.6执行结果..."

files=(
  "src/business/interfaces/ISecurityManager.ts"
  "src/business/managers/SecurityManager.ts"
)

all_created=true
for file in "${files[@]}"; do
  if [ -f "$file" ]; then
    echo "✅ $file 创建成功"
  else
    echo "❌ $file 创建失败"
    all_created=false
  fi
done

if npm run type-check > /dev/null 2>&1; then
  echo "✅ TypeScript编译检查通过"
else
  echo "❌ TypeScript编译检查失败"
  all_created=false
fi

if [ "$all_created" = true ]; then
  echo "🎉 任务3.6执行成功！"
  exit 0
else
  echo "💥 任务3.6执行失败，请检查错误信息"
  exit 1
fi
```

## 📝 完成确认

任务完成后，请在检查清单中标记：
- [ ] 任务3.6已完成
- [ ] 安全管理器接口定义完成
- [ ] 安全管理器实现完成
- [ ] 安全功能测试通过
- [ ] 准备执行任务3.7

## 🔗 相关链接

- [上一个任务：Task-3.5 实现迁移管理器](./task-3.5-migration-manager.md)
- [下一个任务：Task-3.7 创建业务层测试页面](./task-3.7-business-layer-test-page.md)
- [重构检查清单](../00-refactoring-checklist.md)