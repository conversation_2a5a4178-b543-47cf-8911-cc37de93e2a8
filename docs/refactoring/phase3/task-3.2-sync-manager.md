# 任务3.2：实现同步管理器

## 📋 任务概述

**任务ID**: Task-3.2  
**任务名称**: 实现同步管理器  
**所属阶段**: 阶段3 - 业务层重构  
**预计时间**: 3小时  
**依赖任务**: Task-3.1 (实现内存管理器)  
**后续任务**: Task-3.3 (实现配置管理器)  

## 🎯 任务目标

实现同步管理器，提供数据同步功能，支持多设备间的数据一致性和冲突解决。

## 🛠️ 实施步骤

### 步骤1：定义同步管理器接口
**文件**: `src/business/interfaces/ISyncManager.ts`

```typescript
export interface ISyncManager {
  // 同步操作
  startSync(): Promise<SyncResult>;
  stopSync(): void;
  forcSync(): Promise<SyncResult>;
  
  // 同步状态
  getSyncStatus(): SyncStatus;
  getLastSyncTime(): Date | null;
  
  // 冲突解决
  resolveConflict(conflictId: string, resolution: ConflictResolution): Promise<void>;
  getPendingConflicts(): Promise<SyncConflict[]>;
  
  // 同步配置
  setSyncInterval(interval: number): void;
  enableAutoSync(enabled: boolean): void;
}

export interface SyncResult {
  success: boolean;
  syncedCount: number;
  conflictCount: number;
  errors: SyncError[];
  duration: number;
}

export interface SyncStatus {
  isActive: boolean;
  progress: number;
  currentOperation: string;
  lastSyncTime?: Date;
  nextSyncTime?: Date;
}

export interface SyncConflict {
  id: string;
  entityId: string;
  entityType: string;
  localVersion: any;
  remoteVersion: any;
  conflictType: ConflictType;
  timestamp: Date;
}

export enum ConflictType {
  UPDATE_UPDATE = 'update_update',
  UPDATE_DELETE = 'update_delete',
  DELETE_UPDATE = 'delete_update'
}

export enum ConflictResolution {
  USE_LOCAL = 'use_local',
  USE_REMOTE = 'use_remote',
  MERGE = 'merge',
  SKIP = 'skip'
}

export interface SyncError {
  code: string;
  message: string;
  entityId?: string;
  details?: any;
}
```

### 步骤2：实现同步管理器
**文件**: `src/business/managers/SyncManager.ts`

```typescript
import { ISyncManager, SyncResult, SyncStatus, SyncConflict, ConflictResolution } from '../interfaces/ISyncManager';
import { IEventEmitter } from '../../infrastructure/utils/EventEmitter';
import { logger } from '../../infrastructure/utils/Logger';

export class SyncManager implements ISyncManager {
  private isActive = false;
  private syncInterval: number = 300000; // 5分钟
  private autoSyncEnabled = true;
  private syncTimer?: NodeJS.Timeout;
  private lastSyncTime?: Date;
  private pendingConflicts: SyncConflict[] = [];

  constructor(
    private eventEmitter: IEventEmitter
  ) {}

  async startSync(): Promise<SyncResult> {
    if (this.isActive) {
      return {
        success: false,
        syncedCount: 0,
        conflictCount: 0,
        errors: [{ code: 'SYNC_ALREADY_ACTIVE', message: '同步已在进行中' }],
        duration: 0
      };
    }

    this.isActive = true;
    const startTime = Date.now();
    
    try {
      this.eventEmitter.emit('syncStarted');
      
      // 执行同步逻辑
      const result = await this.performSync();
      
      this.lastSyncTime = new Date();
      this.scheduleNextSync();
      
      this.eventEmitter.emit('syncCompleted', result);
      
      return {
        ...result,
        duration: Date.now() - startTime
      };
    } catch (error) {
      logger.error('同步失败:', error);
      return {
        success: false,
        syncedCount: 0,
        conflictCount: 0,
        errors: [{ code: 'SYNC_ERROR', message: error.message }],
        duration: Date.now() - startTime
      };
    } finally {
      this.isActive = false;
    }
  }

  stopSync(): void {
    this.isActive = false;
    if (this.syncTimer) {
      clearTimeout(this.syncTimer);
      this.syncTimer = undefined;
    }
    this.eventEmitter.emit('syncStopped');
  }

  async forcSync(): Promise<SyncResult> {
    this.stopSync();
    return await this.startSync();
  }

  getSyncStatus(): SyncStatus {
    return {
      isActive: this.isActive,
      progress: this.isActive ? 50 : 0, // 简化实现
      currentOperation: this.isActive ? '正在同步...' : '空闲',
      lastSyncTime: this.lastSyncTime,
      nextSyncTime: this.getNextSyncTime()
    };
  }

  getLastSyncTime(): Date | null {
    return this.lastSyncTime || null;
  }

  async resolveConflict(conflictId: string, resolution: ConflictResolution): Promise<void> {
    const conflictIndex = this.pendingConflicts.findIndex(c => c.id === conflictId);
    if (conflictIndex === -1) {
      throw new Error(`冲突不存在: ${conflictId}`);
    }

    const conflict = this.pendingConflicts[conflictIndex];
    
    // 根据解决方案处理冲突
    switch (resolution) {
      case ConflictResolution.USE_LOCAL:
        // 使用本地版本
        break;
      case ConflictResolution.USE_REMOTE:
        // 使用远程版本
        break;
      case ConflictResolution.MERGE:
        // 合并版本
        break;
      case ConflictResolution.SKIP:
        // 跳过冲突
        break;
    }

    // 移除已解决的冲突
    this.pendingConflicts.splice(conflictIndex, 1);
    
    this.eventEmitter.emit('conflictResolved', { conflictId, resolution });
    logger.info(`冲突已解决: ${conflictId} - ${resolution}`);
  }

  async getPendingConflicts(): Promise<SyncConflict[]> {
    return [...this.pendingConflicts];
  }

  setSyncInterval(interval: number): void {
    this.syncInterval = interval;
    if (this.autoSyncEnabled) {
      this.scheduleNextSync();
    }
  }

  enableAutoSync(enabled: boolean): void {
    this.autoSyncEnabled = enabled;
    if (enabled) {
      this.scheduleNextSync();
    } else {
      this.stopSync();
    }
  }

  private async performSync(): Promise<Omit<SyncResult, 'duration'>> {
    // 简化的同步实现
    let syncedCount = 0;
    let conflictCount = 0;
    const errors: any[] = [];

    try {
      // 1. 获取本地变更
      // 2. 获取远程变更
      // 3. 检测冲突
      // 4. 应用变更
      // 5. 上传本地变更

      syncedCount = 10; // 模拟同步的项目数
      
    } catch (error) {
      errors.push({
        code: 'SYNC_PERFORM_ERROR',
        message: error.message
      });
    }

    return {
      success: errors.length === 0,
      syncedCount,
      conflictCount,
      errors
    };
  }

  private scheduleNextSync(): void {
    if (this.syncTimer) {
      clearTimeout(this.syncTimer);
    }

    if (this.autoSyncEnabled) {
      this.syncTimer = setTimeout(() => {
        this.startSync();
      }, this.syncInterval);
    }
  }

  private getNextSyncTime(): Date | undefined {
    if (!this.autoSyncEnabled || !this.lastSyncTime) {
      return undefined;
    }
    
    return new Date(this.lastSyncTime.getTime() + this.syncInterval);
  }
}
```

## ✅ 验收标准

### 功能验收
- [ ] ISyncManager接口定义完整
- [ ] SyncManager实现所有接口方法
- [ ] 支持自动和手动同步
- [ ] 支持冲突检测和解决
- [ ] 支持同步状态监控

### 质量验收
- [ ] TypeScript编译无错误
- [ ] 同步逻辑正确
- [ ] 错误处理完善
- [ ] 事件发射机制正常

## 🔄 幂等性保证

### 执行前检查
```bash
#!/bin/bash
echo "🔍 检查任务3.2执行状态..."

if [ ! -f "src/business/managers/MemoryManager.ts" ]; then
  echo "❌ 依赖任务Task-3.1未完成，请先执行Task-3.1"
  exit 1
fi

if [ -f "src/business/managers/SyncManager.ts" ]; then
  echo "⚠️  同步管理器已存在，任务可能已完成"
  read -p "是否继续？(y/N): " confirm
  if [ "$confirm" != "y" ]; then
    exit 1
  fi
fi

echo "✅ 前置检查完成，可以执行任务3.2"
```

### 执行后验证
```bash
#!/bin/bash
echo "🔍 验证任务3.2执行结果..."

files=(
  "src/business/interfaces/ISyncManager.ts"
  "src/business/managers/SyncManager.ts"
)

all_created=true
for file in "${files[@]}"; do
  if [ -f "$file" ]; then
    echo "✅ $file 创建成功"
  else
    echo "❌ $file 创建失败"
    all_created=false
  fi
done

if npm run type-check > /dev/null 2>&1; then
  echo "✅ TypeScript编译检查通过"
else
  echo "❌ TypeScript编译检查失败"
  all_created=false
fi

if [ "$all_created" = true ]; then
  echo "🎉 任务3.2执行成功！"
  exit 0
else
  echo "💥 任务3.2执行失败，请检查错误信息"
  exit 1
fi
```

## 📝 完成确认

任务完成后，请在检查清单中标记：
- [ ] 任务3.2已完成
- [ ] 同步管理器接口定义完成
- [ ] 同步管理器实现完成
- [ ] 同步功能测试通过
- [ ] 准备执行任务3.3

## 🔗 相关链接

- [上一个任务：Task-3.1 实现内存管理器](./task-3.1-memory-manager.md)
- [下一个任务：Task-3.3 实现配置管理器](./task-3.3-config-manager.md)
- [重构检查清单](../00-refactoring-checklist.md)
