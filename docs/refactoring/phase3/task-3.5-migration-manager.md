# 任务3.5：实现迁移管理器

## 📋 任务概述

**任务ID**: Task-3.5
**任务名称**: 实现迁移管理器
**所属阶段**: 阶段3 - 业务层重构
**预计时间**: 4小时
**依赖任务**: Task-3.4 (实现搜索管理器)
**后续任务**: Task-3.6 (实现安全管理器)

## 🎯 任务目标

实现迁移管理器，提供数据迁移和备份功能，支持存储提供者间的数据迁移、进度监控和错误恢复。

## 🛠️ 实施步骤

### 步骤1：定义迁移管理器接口
**文件**: `src/business/interfaces/IMigrationManager.ts`

```typescript
import { IStorageProvider } from '../../infrastructure/interfaces/IStorageProvider';
import { DataResult } from '../../data/types/DataTypes';

export interface IMigrationManager {
  // 迁移操作
  startMigration(request: MigrationRequest): Promise<DataResult<MigrationSession>>;
  pauseMigration(sessionId: string): Promise<DataResult<boolean>>;
  resumeMigration(sessionId: string): Promise<DataResult<boolean>>;
  cancelMigration(sessionId: string): Promise<DataResult<boolean>>;

  // 迁移状态
  getMigrationStatus(sessionId: string): Promise<DataResult<MigrationStatus>>;
  getActiveMigrations(): Promise<DataResult<MigrationSession[]>>;
  getMigrationHistory(): Promise<DataResult<MigrationHistoryItem[]>>;

  // 备份操作
  createBackup(request: BackupRequest): Promise<DataResult<BackupSession>>;
  restoreBackup(backupId: string, options?: RestoreOptions): Promise<DataResult<RestoreSession>>;

  // 备份管理
  listBackups(): Promise<DataResult<BackupInfo[]>>;
  deleteBackup(backupId: string): Promise<DataResult<boolean>>;
  validateBackup(backupId: string): Promise<DataResult<ValidationResult>>;

  // 迁移配置
  setMigrationConfig(config: MigrationConfig): Promise<void>;
  getMigrationConfig(): Promise<MigrationConfig>;
}

export interface MigrationRequest {
  sourceProvider: IStorageProvider;
  targetProvider: IStorageProvider;
  options: MigrationOptions;
  filters?: MigrationFilter[];
}

export interface MigrationOptions {
  chunkSize: number;
  concurrency: number;
  retryCount: number;
  timeout: number;
  skipDuplicates: boolean;
  verifyIntegrity: boolean;
  deleteSource: boolean;
  resumable: boolean;
}

export interface MigrationFilter {
  type: 'include' | 'exclude';
  pattern: string;
  field: 'path' | 'size' | 'date' | 'type';
}

export interface MigrationSession {
  id: string;
  request: MigrationRequest;
  status: MigrationSessionStatus;
  progress: MigrationProgress;
  startTime: Date;
  endTime?: Date;
  error?: string;
}

export enum MigrationSessionStatus {
  PENDING = 'pending',
  RUNNING = 'running',
  PAUSED = 'paused',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled'
}

export interface MigrationProgress {
  totalFiles: number;
  processedFiles: number;
  totalSize: number;
  processedSize: number;
  currentFile?: string;
  speed: number; // bytes per second
  estimatedTimeRemaining: number; // seconds
  errors: MigrationError[];
}

export interface MigrationStatus {
  session: MigrationSession;
  currentOperation: string;
  lastUpdate: Date;
}

export interface MigrationError {
  file: string;
  error: string;
  timestamp: Date;
  retryCount: number;
}

export interface MigrationHistoryItem {
  sessionId: string;
  sourceProvider: string;
  targetProvider: string;
  startTime: Date;
  endTime: Date;
  status: MigrationSessionStatus;
  filesProcessed: number;
  totalSize: number;
  duration: number;
}

export interface BackupRequest {
  provider: IStorageProvider;
  name: string;
  description?: string;
  compression: boolean;
  encryption: boolean;
  password?: string;
  filters?: MigrationFilter[];
}

export interface BackupSession {
  id: string;
  request: BackupRequest;
  status: BackupStatus;
  progress: BackupProgress;
  startTime: Date;
  endTime?: Date;
}

export enum BackupStatus {
  CREATING = 'creating',
  COMPLETED = 'completed',
  FAILED = 'failed'
}

export interface BackupProgress {
  totalFiles: number;
  processedFiles: number;
  totalSize: number;
  processedSize: number;
  currentFile?: string;
}

export interface BackupInfo {
  id: string;
  name: string;
  description?: string;
  createdAt: Date;
  size: number;
  fileCount: number;
  compressed: boolean;
  encrypted: boolean;
  checksum: string;
}

export interface RestoreOptions {
  overwrite: boolean;
  targetPath?: string;
  password?: string;
}

export interface RestoreSession {
  id: string;
  backupId: string;
  status: RestoreStatus;
  progress: RestoreProgress;
  startTime: Date;
  endTime?: Date;
}

export enum RestoreStatus {
  RESTORING = 'restoring',
  COMPLETED = 'completed',
  FAILED = 'failed'
}

export interface RestoreProgress {
  totalFiles: number;
  restoredFiles: number;
  totalSize: number;
  restoredSize: number;
  currentFile?: string;
}

export interface ValidationResult {
  valid: boolean;
  errors: string[];
  warnings: string[];
  fileCount: number;
  totalSize: number;
}

export interface MigrationConfig {
  defaultChunkSize: number;
  defaultConcurrency: number;
  defaultRetryCount: number;
  defaultTimeout: number;
  maxBackupRetention: number;
  compressionLevel: number;
  encryptionAlgorithm: string;
}
```

### 步骤2：实现迁移管理器
**文件**: `src/business/managers/MigrationManager.ts`

```typescript
import { IMigrationManager, MigrationRequest, MigrationSession, MigrationStatus, BackupRequest, BackupSession, MigrationConfig } from '../interfaces/IMigrationManager';
import { IStorageProvider } from '../../infrastructure/interfaces/IStorageProvider';
import { ICryptoService } from '../../infrastructure/interfaces/ICryptoService';
import { DataResult } from '../../data/types/DataTypes';
import { IEventEmitter } from '../../infrastructure/utils/EventEmitter';
import { logger } from '../../infrastructure/utils/Logger';

export class MigrationManager implements IMigrationManager {
  private activeSessions = new Map<string, MigrationSession>();
  private migrationHistory: any[] = [];
  private config: MigrationConfig = {
    defaultChunkSize: 1048576, // 1MB
    defaultConcurrency: 3,
    defaultRetryCount: 3,
    defaultTimeout: 30000,
    maxBackupRetention: 10,
    compressionLevel: 6,
    encryptionAlgorithm: 'AES-256-GCM'
  };

  constructor(
    private cryptoService: ICryptoService,
    private eventEmitter: IEventEmitter
  ) {}

  async startMigration(request: MigrationRequest): Promise<DataResult<MigrationSession>> {
    try {
      const sessionId = this.generateSessionId();

      const session: MigrationSession = {
        id: sessionId,
        request,
        status: 'pending' as any,
        progress: {
          totalFiles: 0,
          processedFiles: 0,
          totalSize: 0,
          processedSize: 0,
          speed: 0,
          estimatedTimeRemaining: 0,
          errors: []
        },
        startTime: new Date()
      };

      this.activeSessions.set(sessionId, session);

      // 异步执行迁移
      this.executeMigration(session).catch(error => {
        logger.error(`迁移执行失败 [${sessionId}]:`, error);
        session.status = 'failed' as any;
        session.error = error.message;
        session.endTime = new Date();
      });

      this.eventEmitter.emit('migrationStarted', session);

      return { success: true, data: session };
    } catch (error) {
      logger.error('启动迁移失败:', error);
      return { success: false, error: error as Error };
    }
  }

  async pauseMigration(sessionId: string): Promise<DataResult<boolean>> {
    try {
      const session = this.activeSessions.get(sessionId);
      if (!session) {
        return { success: false, error: new Error(`迁移会话不存在: ${sessionId}`) };
      }

      if (session.status === 'running' as any) {
        session.status = 'paused' as any;
        this.eventEmitter.emit('migrationPaused', session);
        return { success: true, data: true };
      }

      return { success: false, error: new Error('只能暂停正在运行的迁移') };
    } catch (error) {
      logger.error(`暂停迁移失败 [${sessionId}]:`, error);
      return { success: false, error: error as Error };
    }
  }

  async resumeMigration(sessionId: string): Promise<DataResult<boolean>> {
    try {
      const session = this.activeSessions.get(sessionId);
      if (!session) {
        return { success: false, error: new Error(`迁移会话不存在: ${sessionId}`) };
      }

      if (session.status === 'paused' as any) {
        session.status = 'running' as any;

        // 继续执行迁移
        this.executeMigration(session).catch(error => {
          logger.error(`恢复迁移执行失败 [${sessionId}]:`, error);
          session.status = 'failed' as any;
          session.error = error.message;
          session.endTime = new Date();
        });

        this.eventEmitter.emit('migrationResumed', session);
        return { success: true, data: true };
      }

      return { success: false, error: new Error('只能恢复已暂停的迁移') };
    } catch (error) {
      logger.error(`恢复迁移失败 [${sessionId}]:`, error);
      return { success: false, error: error as Error };
    }
  }

  async cancelMigration(sessionId: string): Promise<DataResult<boolean>> {
    try {
      const session = this.activeSessions.get(sessionId);
      if (!session) {
        return { success: false, error: new Error(`迁移会话不存在: ${sessionId}`) };
      }

      session.status = 'cancelled' as any;
      session.endTime = new Date();

      this.activeSessions.delete(sessionId);
      this.addToHistory(session);

      this.eventEmitter.emit('migrationCancelled', session);

      return { success: true, data: true };
    } catch (error) {
      logger.error(`取消迁移失败 [${sessionId}]:`, error);
      return { success: false, error: error as Error };
    }
  }

  async getMigrationStatus(sessionId: string): Promise<DataResult<MigrationStatus>> {
    try {
      const session = this.activeSessions.get(sessionId);
      if (!session) {
        return { success: false, error: new Error(`迁移会话不存在: ${sessionId}`) };
      }

      const status: MigrationStatus = {
        session,
        currentOperation: this.getCurrentOperation(session),
        lastUpdate: new Date()
      };

      return { success: true, data: status };
    } catch (error) {
      logger.error(`获取迁移状态失败 [${sessionId}]:`, error);
      return { success: false, error: error as Error };
    }
  }

  async getActiveMigrations(): Promise<DataResult<MigrationSession[]>> {
    return { success: true, data: Array.from(this.activeSessions.values()) };
  }

  async getMigrationHistory(): Promise<DataResult<any[]>> {
    return { success: true, data: [...this.migrationHistory] };
  }

  async createBackup(request: BackupRequest): Promise<DataResult<BackupSession>> {
    try {
      const sessionId = this.generateSessionId();

      const session: BackupSession = {
        id: sessionId,
        request,
        status: 'creating' as any,
        progress: {
          totalFiles: 0,
          processedFiles: 0,
          totalSize: 0,
          processedSize: 0
        },
        startTime: new Date()
      };

      // 异步执行备份
      this.executeBackup(session).catch(error => {
        logger.error(`备份执行失败 [${sessionId}]:`, error);
        session.status = 'failed' as any;
        session.endTime = new Date();
      });

      this.eventEmitter.emit('backupStarted', session);

      return { success: true, data: session };
    } catch (error) {
      logger.error('创建备份失败:', error);
      return { success: false, error: error as Error };
    }
  }

  async restoreBackup(backupId: string, options?: any): Promise<DataResult<any>> {
    try {
      const sessionId = this.generateSessionId();

      const session: any = {
        id: sessionId,
        backupId,
        status: 'restoring',
        progress: {
          totalFiles: 0,
          restoredFiles: 0,
          totalSize: 0,
          restoredSize: 0
        },
        startTime: new Date()
      };

      // 异步执行恢复
      this.executeRestore(session, options).catch(error => {
        logger.error(`恢复执行失败 [${sessionId}]:`, error);
        session.status = 'failed';
        session.endTime = new Date();
      });

      this.eventEmitter.emit('restoreStarted', session);

      return { success: true, data: session };
    } catch (error) {
      logger.error('恢复备份失败:', error);
      return { success: false, error: error as Error };
    }
  }

  async listBackups(): Promise<DataResult<any[]>> {
    try {
      // 从存储中获取备份列表
      const backups: any[] = []; // 简化实现
      return { success: true, data: backups };
    } catch (error) {
      logger.error('获取备份列表失败:', error);
      return { success: false, error: error as Error };
    }
  }

  async deleteBackup(backupId: string): Promise<DataResult<boolean>> {
    try {
      // 删除备份文件
      logger.info(`删除备份: ${backupId}`);
      return { success: true, data: true };
    } catch (error) {
      logger.error(`删除备份失败 [${backupId}]:`, error);
      return { success: false, error: error as Error };
    }
  }

  async validateBackup(backupId: string): Promise<DataResult<any>> {
    try {
      // 验证备份完整性
      const result: any = {
        valid: true,
        errors: [],
        warnings: [],
        fileCount: 0,
        totalSize: 0
      };

      return { success: true, data: result };
    } catch (error) {
      logger.error(`验证备份失败 [${backupId}]:`, error);
      return { success: false, error: error as Error };
    }
  }

  async setMigrationConfig(config: MigrationConfig): Promise<void> {
    this.config = { ...this.config, ...config };
    // 保存配置
    localStorage.setItem('migrationConfig', JSON.stringify(this.config));
  }

  async getMigrationConfig(): Promise<MigrationConfig> {
    return { ...this.config };
  }

  // 私有方法
  private async executeMigration(session: MigrationSession): Promise<void> {
    try {
      session.status = 'running' as any;

      // 1. 获取源文件列表
      const sourceFiles = await this.getSourceFiles(session.request.sourceProvider, session.request.filters);
      session.progress.totalFiles = sourceFiles.length;
      session.progress.totalSize = sourceFiles.reduce((sum, file) => sum + file.size, 0);

      // 2. 执行迁移
      for (const file of sourceFiles) {
        if (session.status === 'cancelled' as any) break;

        while (session.status === 'paused' as any) {
          await this.sleep(1000);
        }

        try {
          await this.migrateFile(session, file);
          session.progress.processedFiles++;
          session.progress.processedSize += file.size;

          // 更新进度
          this.updateProgress(session);
          this.eventEmitter.emit('migrationProgress', session);
        } catch (error) {
          session.progress.errors.push({
            file: file.path,
            error: error.message,
            timestamp: new Date(),
            retryCount: 0
          });
        }
      }

      // 3. 完成迁移
      if (session.status !== 'cancelled' as any) {
        session.status = 'completed' as any;
        session.endTime = new Date();

        this.activeSessions.delete(session.id);
        this.addToHistory(session);

        this.eventEmitter.emit('migrationCompleted', session);
      }
    } catch (error) {
      session.status = 'failed' as any;
      session.error = error.message;
      session.endTime = new Date();

      this.activeSessions.delete(session.id);
      this.addToHistory(session);

      this.eventEmitter.emit('migrationFailed', session);
      throw error;
    }
  }

  private async executeBackup(session: BackupSession): Promise<void> {
    try {
      // 备份实现
      session.status = 'completed' as any;
      session.endTime = new Date();

      this.eventEmitter.emit('backupCompleted', session);
    } catch (error) {
      session.status = 'failed' as any;
      session.endTime = new Date();

      this.eventEmitter.emit('backupFailed', session);
      throw error;
    }
  }

  private async executeRestore(session: any, options?: any): Promise<void> {
    try {
      // 恢复实现
      session.status = 'completed';
      session.endTime = new Date();

      this.eventEmitter.emit('restoreCompleted', session);
    } catch (error) {
      session.status = 'failed';
      session.endTime = new Date();

      this.eventEmitter.emit('restoreFailed', session);
      throw error;
    }
  }

  private async getSourceFiles(provider: IStorageProvider, filters?: any[]): Promise<any[]> {
    // 获取源文件列表
    const result = await provider.list('');
    if (!result.success) {
      throw new Error('获取源文件列表失败: ' + result.error?.message);
    }

    let files = (result.data || []).map(path => ({
      path,
      size: 0 // 简化实现
    }));

    // 应用过滤器
    if (filters) {
      files = this.applyFilters(files, filters);
    }

    return files;
  }

  private async migrateFile(session: MigrationSession, file: any): Promise<void> {
    const { sourceProvider, targetProvider, options } = session.request;

    // 检查是否跳过重复文件
    if (options.skipDuplicates) {
      const exists = await targetProvider.exists(file.path);
      if (exists.success && exists.data) {
        return;
      }
    }

    // 下载文件
    const downloadResult = await sourceProvider.get(file.path);
    if (!downloadResult.success) {
      throw new Error(`下载文件失败: ${downloadResult.error?.message}`);
    }

    // 上传文件
    const uploadResult = await targetProvider.put(file.path, downloadResult.data);
    if (!uploadResult.success) {
      throw new Error(`上传文件失败: ${uploadResult.error?.message}`);
    }

    // 验证完整性
    if (options.verifyIntegrity) {
      await this.verifyFileIntegrity(sourceProvider, targetProvider, file.path);
    }

    // 删除源文件
    if (options.deleteSource) {
      await sourceProvider.delete(file.path);
    }
  }

  private async verifyFileIntegrity(sourceProvider: IStorageProvider, targetProvider: IStorageProvider, path: string): Promise<void> {
    // 简化实现：比较文件大小
    const sourceExists = await sourceProvider.exists(path);
    const targetExists = await targetProvider.exists(path);

    if (!sourceExists.data || !targetExists.data) {
      throw new Error('文件完整性验证失败：文件不存在');
    }
  }

  private applyFilters(files: any[], filters: any[]): any[] {
    return files.filter(file => {
      return filters.every(filter => {
        // 简化的过滤器实现
        if (filter.type === 'include') {
          return file.path.includes(filter.pattern);
        } else {
          return !file.path.includes(filter.pattern);
        }
      });
    });
  }

  private updateProgress(session: MigrationSession): void {
    const { progress } = session;

    if (progress.totalSize > 0) {
      const elapsedTime = (Date.now() - session.startTime.getTime()) / 1000;
      progress.speed = progress.processedSize / elapsedTime;

      const remainingSize = progress.totalSize - progress.processedSize;
      progress.estimatedTimeRemaining = progress.speed > 0 ? remainingSize / progress.speed : 0;
    }
  }

  private getCurrentOperation(session: MigrationSession): string {
    switch (session.status) {
      case 'pending': return '准备中...';
      case 'running': return `正在迁移: ${session.progress.currentFile || ''}`;
      case 'paused': return '已暂停';
      case 'completed': return '已完成';
      case 'failed': return '失败';
      case 'cancelled': return '已取消';
      default: return '未知状态';
    }
  }

  private addToHistory(session: MigrationSession): void {
    const historyItem: any = {
      sessionId: session.id,
      sourceProvider: 'source', // 简化
      targetProvider: 'target', // 简化
      startTime: session.startTime,
      endTime: session.endTime || new Date(),
      status: session.status,
      filesProcessed: session.progress.processedFiles,
      totalSize: session.progress.totalSize,
      duration: (session.endTime || new Date()).getTime() - session.startTime.getTime()
    };

    this.migrationHistory.unshift(historyItem);

    // 限制历史记录数量
    if (this.migrationHistory.length > 100) {
      this.migrationHistory = this.migrationHistory.slice(0, 100);
    }
  }

  private generateSessionId(): string {
    return `migration_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
```

## ✅ 验收标准

### 功能验收
- [ ] IMigrationManager接口定义完整
- [ ] MigrationManager实现所有接口方法
- [ ] 支持数据迁移和备份功能
- [ ] 支持迁移进度监控和控制
- [ ] 支持错误处理和恢复机制
- [ ] 支持备份验证和恢复功能

### 质量验收
- [ ] TypeScript编译无错误
- [ ] 迁移性能良好
- [ ] 错误处理完善
- [ ] 进度监控准确

## 🔄 幂等性保证

### 执行前检查
```bash
#!/bin/bash
echo "🔍 检查任务3.5执行状态..."

if [ ! -f "src/business/managers/SearchManager.ts" ]; then
  echo "❌ 依赖任务Task-3.4未完成，请先执行Task-3.4"
  exit 1
fi

if [ -f "src/business/managers/MigrationManager.ts" ]; then
  echo "⚠️  迁移管理器已存在，任务可能已完成"
  read -p "是否继续？(y/N): " confirm
  if [ "$confirm" != "y" ]; then
    exit 1
  fi
fi

echo "✅ 前置检查完成，可以执行任务3.5"
```

### 执行后验证
```bash
#!/bin/bash
echo "🔍 验证任务3.5执行结果..."

files=(
  "src/business/interfaces/IMigrationManager.ts"
  "src/business/managers/MigrationManager.ts"
)

all_created=true
for file in "${files[@]}"; do
  if [ -f "$file" ]; then
    echo "✅ $file 创建成功"
  else
    echo "❌ $file 创建失败"
    all_created=false
  fi
done

if npm run type-check > /dev/null 2>&1; then
  echo "✅ TypeScript编译检查通过"
else
  echo "❌ TypeScript编译检查失败"
  all_created=false
fi

if [ "$all_created" = true ]; then
  echo "🎉 任务3.5执行成功！"
  exit 0
else
  echo "💥 任务3.5执行失败，请检查错误信息"
  exit 1
fi
```

## 📝 完成确认

任务完成后，请在检查清单中标记：
- [ ] 任务3.5已完成
- [ ] 迁移管理器接口定义完成
- [ ] 迁移管理器实现完成
- [ ] 迁移功能测试通过
- [ ] 准备执行任务3.6

## 🔗 相关链接

- [上一个任务：Task-3.4 实现搜索管理器](./task-3.4-search-manager.md)
- [下一个任务：Task-3.6 实现安全管理器](./task-3.6-security-manager.md)
- [重构检查清单](../00-refactoring-checklist.md)