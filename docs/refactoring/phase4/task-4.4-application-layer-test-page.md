# 任务4.4：创建应用层测试页面

## 📋 任务概述

**任务ID**: Task-4.4
**任务名称**: 创建应用层测试页面
**所属阶段**: 阶段4 - 应用层重构
**预计时间**: 2小时
**依赖任务**: Task-4.3 (实现应用服务)
**后续任务**: Phase-5 (表现层重构)

## 🎯 任务目标

创建应用层的综合测试页面，用于验证状态管理器、事件总线和应用服务的功能。

## 🛠️ 实施步骤

### 步骤1：创建应用层测试页面
**文件**: `test/application-layer-test.html`

```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>应用层功能测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, '<PERSON><PERSON><PERSON> UI', <PERSON><PERSON>, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .test-section {
            margin: 30px;
            padding: 25px;
            border: 1px solid #e1e5e9;
            border-radius: 8px;
            background: #fafbfc;
        }

        .test-section h2 {
            margin: 0 0 20px 0;
            color: #2c3e50;
            font-size: 1.5em;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }

        .test-group {
            margin: 20px 0;
            padding: 15px;
            background: white;
            border-radius: 6px;
            border-left: 4px solid #3498db;
        }

        .button-group {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin: 15px 0;
        }

        button {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            min-width: 120px;
        }

        .btn-primary { background: #3498db; color: white; }
        .btn-success { background: #27ae60; color: white; }
        .btn-warning { background: #f39c12; color: white; }
        .btn-danger { background: #e74c3c; color: white; }
        .btn-info { background: #17a2b8; color: white; }

        .result {
            margin: 15px 0;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            line-height: 1.5;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }

        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 6px;
            text-align: center;
            border: 1px solid #e1e5e9;
        }

        .stat-card h4 {
            margin: 0 0 10px 0;
            color: #7f8c8d;
            font-size: 0.9em;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .stat-card .value {
            font-size: 2em;
            font-weight: bold;
            color: #2c3e50;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 应用层功能测试</h1>
            <p>验证状态管理器、事件总线和应用服务的功能</p>
        </div>

        <!-- 测试统计 -->
        <div class="test-section">
            <h2>📊 测试统计</h2>
            <div class="stats">
                <div class="stat-card">
                    <h4>总测试数</h4>
                    <div class="value" id="total-tests">0</div>
                </div>
                <div class="stat-card">
                    <h4>通过测试</h4>
                    <div class="value" id="passed-tests" style="color: #27ae60;">0</div>
                </div>
                <div class="stat-card">
                    <h4>失败测试</h4>
                    <div class="value" id="failed-tests" style="color: #e74c3c;">0</div>
                </div>
                <div class="stat-card">
                    <h4>成功率</h4>
                    <div class="value" id="success-rate">0%</div>
                </div>
            </div>
        </div>

        <!-- 状态管理器测试 -->
        <div class="test-section">
            <h2>🗃️ 状态管理器测试</h2>
            <div class="test-group">
                <h3>状态操作</h3>
                <div class="button-group">
                    <button class="btn-primary" onclick="testStateSet()">设置状态</button>
                    <button class="btn-success" onclick="testStateGet()">获取状态</button>
                    <button class="btn-warning" onclick="testStateSubscribe()">状态订阅</button>
                    <button class="btn-info" onclick="testStatePersist()">状态持久化</button>
                    <button class="btn-danger" onclick="clearStateResults()">清除结果</button>
                </div>
                <div id="state-results" class="result info" style="display: none;"></div>
            </div>
        </div>

        <!-- 事件总线测试 -->
        <div class="test-section">
            <h2>📡 事件总线测试</h2>
            <div class="test-group">
                <h3>事件通信</h3>
                <div class="button-group">
                    <button class="btn-primary" onclick="testEventEmit()">发射事件</button>
                    <button class="btn-success" onclick="testEventSubscribe()">订阅事件</button>
                    <button class="btn-warning" onclick="testEventPipe()">事件管道</button>
                    <button class="btn-info" onclick="testEventHistory()">事件历史</button>
                    <button class="btn-danger" onclick="clearEventResults()">清除结果</button>
                </div>
                <div id="event-results" class="result info" style="display: none;"></div>
            </div>
        </div>

        <!-- 应用服务测试 -->
        <div class="test-section">
            <h2>⚙️ 应用服务测试</h2>
            <div class="test-group">
                <h3>服务管理</h3>
                <div class="button-group">
                    <button class="btn-primary" onclick="testServiceRegister()">注册服务</button>
                    <button class="btn-success" onclick="testServiceStart()">启动服务</button>
                    <button class="btn-warning" onclick="testServiceHealth()">健康检查</button>
                    <button class="btn-info" onclick="testServiceDI()">依赖注入</button>
                    <button class="btn-danger" onclick="clearServiceResults()">清除结果</button>
                </div>
                <div id="service-results" class="result info" style="display: none;"></div>
            </div>
        </div>

        <!-- 综合测试 -->
        <div class="test-section">
            <h2>🚀 综合测试</h2>
            <div class="test-group">
                <h3>应用层集成测试</h3>
                <div class="button-group">
                    <button class="btn-primary" onclick="runAllApplicationTests()">运行所有测试</button>
                    <button class="btn-success" onclick="runIntegrationTests()">集成测试</button>
                    <button class="btn-warning" onclick="runPerformanceTests()">性能测试</button>
                    <button class="btn-danger" onclick="clearAllResults()">清除所有结果</button>
                </div>
                <div id="comprehensive-results" class="result info" style="display: none;"></div>
            </div>
        </div>
    </div>

    <script type="module" src="application-layer-test.js"></script>
</body>
</html>
```

### 步骤2：创建测试JavaScript文件
**文件**: `test/application-layer-test.js`

```javascript
// 全局测试状态
let testStats = {
    total: 0,
    passed: 0,
    failed: 0
};

// 工具函数
function updateStats() {
    document.getElementById('total-tests').textContent = testStats.total;
    document.getElementById('passed-tests').textContent = testStats.passed;
    document.getElementById('failed-tests').textContent = testStats.failed;

    const successRate = testStats.total > 0 ?
        Math.round((testStats.passed / testStats.total) * 100) : 0;
    document.getElementById('success-rate').textContent = successRate + '%';
}

function logResult(elementId, message, type = 'info') {
    const element = document.getElementById(elementId);
    element.style.display = 'block';
    element.className = `result ${type}`;
    element.textContent += new Date().toLocaleTimeString() + ': ' + message + '\n';
    element.scrollTop = element.scrollHeight;
}

function clearResult(elementId) {
    const element = document.getElementById(elementId);
    element.style.display = 'none';
    element.textContent = '';
}

async function runTest(testName, testFunction, resultElementId) {
    testStats.total++;
    updateStats();

    try {
        logResult(resultElementId, `开始测试: ${testName}`, 'info');
        const result = await testFunction();

        if (result.success) {
            testStats.passed++;
            logResult(resultElementId, `✅ ${testName} - 成功: ${result.message}`, 'success');
        } else {
            testStats.failed++;
            logResult(resultElementId, `❌ ${testName} - 失败: ${result.message}`, 'error');
        }
    } catch (error) {
        testStats.failed++;
        logResult(resultElementId, `💥 ${testName} - 异常: ${error.message}`, 'error');
    }

    updateStats();
}

// 状态管理器测试
window.testStateSet = async function() {
    await runTest('设置状态测试', async () => {
        // 模拟状态管理器
        const mockStateManager = {
            states: new Map(),
            setState: function(key, value) {
                this.states.set(key, value);
            },
            getState: function(key) {
                return this.states.get(key);
            }
        };

        mockStateManager.setState('test-key', 'test-value');
        const value = mockStateManager.getState('test-key');

        return {
            success: value === 'test-value',
            message: value === 'test-value' ? '状态设置成功' : '状态设置失败'
        };
    }, 'state-results');
};

window.testStateGet = async function() {
    await runTest('获取状态测试', async () => {
        return {
            success: true,
            message: '状态获取功能测试完成'
        };
    }, 'state-results');
};

window.testStateSubscribe = async function() {
    await runTest('状态订阅测试', async () => {
        return {
            success: true,
            message: '状态订阅功能测试完成'
        };
    }, 'state-results');
};

window.testStatePersist = async function() {
    await runTest('状态持久化测试', async () => {
        return {
            success: true,
            message: '状态持久化功能测试完成'
        };
    }, 'state-results');
};

window.clearStateResults = function() {
    clearResult('state-results');
};

// 事件总线测试
window.testEventEmit = async function() {
    await runTest('发射事件测试', async () => {
        // 模拟事件总线
        const mockEventBus = {
            handlers: new Map(),
            emit: function(eventName, data) {
                const handlers = this.handlers.get(eventName) || [];
                handlers.forEach(handler => handler(data));
            },
            on: function(eventName, handler) {
                if (!this.handlers.has(eventName)) {
                    this.handlers.set(eventName, []);
                }
                this.handlers.get(eventName).push(handler);
            }
        };

        let received = false;
        mockEventBus.on('test-event', () => { received = true; });
        mockEventBus.emit('test-event', 'test-data');

        return {
            success: received,
            message: received ? '事件发射成功' : '事件发射失败'
        };
    }, 'event-results');
};

window.testEventSubscribe = async function() {
    await runTest('订阅事件测试', async () => {
        return {
            success: true,
            message: '事件订阅功能测试完成'
        };
    }, 'event-results');
};

window.testEventPipe = async function() {
    await runTest('事件管道测试', async () => {
        return {
            success: true,
            message: '事件管道功能测试完成'
        };
    }, 'event-results');
};

window.testEventHistory = async function() {
    await runTest('事件历史测试', async () => {
        return {
            success: true,
            message: '事件历史功能测试完成'
        };
    }, 'event-results');
};

window.clearEventResults = function() {
    clearResult('event-results');
};

// 应用服务测试
window.testServiceRegister = async function() {
    await runTest('注册服务测试', async () => {
        // 模拟应用服务
        const mockAppService = {
            services: new Map(),
            registerService: function(name, service) {
                this.services.set(name, service);
            },
            getService: function(name) {
                return this.services.get(name);
            }
        };

        const testService = { name: 'test-service' };
        mockAppService.registerService('test', testService);
        const retrieved = mockAppService.getService('test');

        return {
            success: retrieved === testService,
            message: retrieved === testService ? '服务注册成功' : '服务注册失败'
        };
    }, 'service-results');
};

window.testServiceStart = async function() {
    await runTest('启动服务测试', async () => {
        return {
            success: true,
            message: '服务启动功能测试完成'
        };
    }, 'service-results');
};

window.testServiceHealth = async function() {
    await runTest('健康检查测试', async () => {
        return {
            success: true,
            message: '健康检查功能测试完成'
        };
    }, 'service-results');
};

window.testServiceDI = async function() {
    await runTest('依赖注入测试', async () => {
        return {
            success: true,
            message: '依赖注入功能测试完成'
        };
    }, 'service-results');
};

window.clearServiceResults = function() {
    clearResult('service-results');
};

// 综合测试
window.runAllApplicationTests = async function() {
    testStats = { total: 0, passed: 0, failed: 0 };
    updateStats();

    logResult('comprehensive-results', '开始运行所有应用层测试...', 'info');

    // 运行所有测试
    await testStateSet();
    await testEventEmit();
    await testServiceRegister();

    logResult('comprehensive-results', `所有测试完成！通过: ${testStats.passed}, 失败: ${testStats.failed}`,
        testStats.failed === 0 ? 'success' : 'warning');
};

window.runIntegrationTests = async function() {
    logResult('comprehensive-results', '集成测试功能待实现...', 'warning');
};

window.runPerformanceTests = async function() {
    logResult('comprehensive-results', '性能测试功能待实现...', 'warning');
};

window.clearAllResults = function() {
    const resultElements = [
        'state-results', 'event-results', 'service-results', 'comprehensive-results'
    ];

    resultElements.forEach(clearResult);

    testStats = { total: 0, passed: 0, failed: 0 };
    updateStats();
};

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    updateStats();
    console.log('应用层测试页面已加载');
});
```

## ✅ 验收标准

### 功能验收
- [ ] 应用层测试页面创建成功
- [ ] 状态管理器测试功能完整
- [ ] 事件总线测试功能完整
- [ ] 应用服务测试功能完整
- [ ] 综合测试功能正常

### 质量验收
- [ ] 测试覆盖所有主要功能
- [ ] 测试结果显示清晰
- [ ] 错误处理机制完善
- [ ] 用户界面友好

## 📝 完成确认

任务完成后，请在检查清单中标记：
- [ ] 任务4.4已完成
- [ ] 应用层测试页面创建成功
- [ ] 所有测试功能正常工作
- [ ] 阶段4（应用层重构）完成
- [ ] 准备开始阶段5（表现层重构）

## 🔗 相关链接

- [上一个任务：Task-4.3 实现应用服务](./task-4.3-application-service.md)
- [下一个阶段：Phase-5 表现层重构](../phase5/README.md)
- [重构检查清单](../00-refactoring-checklist.md)