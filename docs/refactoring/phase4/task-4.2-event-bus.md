# 任务4.2：实现事件总线

## 📋 任务概述

**任务ID**: Task-4.2  
**任务名称**: 实现事件总线  
**所属阶段**: 阶段4 - 应用层重构  
**预计时间**: 2小时  
**依赖任务**: Task-4.1 (实现状态管理器)  
**后续任务**: Task-4.3 (实现应用服务)  

## 🎯 任务目标

实现应用级事件总线，提供组件间通信功能，包括事件发布订阅、事件路由、事件持久化和事件重放。

## 🛠️ 实施步骤

### 步骤1：定义事件总线接口
**文件**: `src/application/interfaces/IEventBus.ts`

```typescript
export interface IEventBus {
  // 事件发布
  emit<T = any>(eventName: string, data?: T, options?: EmitOptions): void;
  emitAsync<T = any>(eventName: string, data?: T, options?: EmitOptions): Promise<void>;
  
  // 事件订阅
  on<T = any>(eventName: string, handler: EventHandler<T>, options?: SubscribeOptions): EventSubscription;
  once<T = any>(eventName: string, handler: EventHandler<T>, options?: SubscribeOptions): EventSubscription;
  off(eventName: string, handler?: EventHandler): void;
  
  // 事件管道
  pipe(eventName: string, targetEventName: string, transformer?: EventTransformer): EventPipe;
  unpipe(eventName: string, targetEventName?: string): void;
  
  // 事件过滤
  filter(eventName: string, predicate: EventPredicate): EventFilter;
  unfilter(eventName: string): void;
  
  // 事件历史
  enableHistory(eventName?: string, maxHistory?: number): void;
  disableHistory(eventName?: string): void;
  getHistory(eventName?: string): EventHistoryItem[];
  clearHistory(eventName?: string): void;
  
  // 事件重放
  replay(eventName: string, fromTime?: Date, toTime?: Date): void;
  replayAll(fromTime?: Date, toTime?: Date): void;
  
  // 事件统计
  getEventStats(): EventStats;
  getEventInfo(eventName: string): EventInfo;
  
  // 事件管理
  listEvents(): string[];
  hasListeners(eventName: string): boolean;
  removeAllListeners(eventName?: string): void;
  
  // 事件调试
  enableDebug(eventName?: string): void;
  disableDebug(eventName?: string): void;
  
  // 事件持久化
  enablePersistence(eventName?: string): void;
  disablePersistence(eventName?: string): void;
  persistEvents(): Promise<void>;
  restoreEvents(): Promise<void>;
}

export type EventHandler<T = any> = (data: T, event: EventContext) => void | Promise<void>;
export type EventTransformer<T = any, R = any> = (data: T, event: EventContext) => R;
export type EventPredicate<T = any> = (data: T, event: EventContext) => boolean;

export interface EventContext {
  eventName: string;
  timestamp: Date;
  id: string;
  source?: string;
  metadata?: Record<string, any>;
}

export interface EmitOptions {
  source?: string;
  metadata?: Record<string, any>;
  delay?: number;
  priority?: EventPriority;
  persistent?: boolean;
}

export interface SubscribeOptions {
  priority?: EventPriority;
  once?: boolean;
  filter?: EventPredicate;
  debounce?: number;
  throttle?: number;
}

export enum EventPriority {
  LOW = 0,
  NORMAL = 1,
  HIGH = 2,
  CRITICAL = 3
}

export interface EventSubscription {
  id: string;
  eventName: string;
  handler: EventHandler;
  options: SubscribeOptions;
  unsubscribe(): void;
}

export interface EventPipe {
  id: string;
  sourceEvent: string;
  targetEvent: string;
  transformer?: EventTransformer;
  remove(): void;
}

export interface EventFilter {
  id: string;
  eventName: string;
  predicate: EventPredicate;
  remove(): void;
}

export interface EventHistoryItem {
  id: string;
  eventName: string;
  data: any;
  context: EventContext;
  timestamp: Date;
}

export interface EventStats {
  totalEvents: number;
  totalEmissions: number;
  totalSubscriptions: number;
  eventCounts: Record<string, number>;
  averageHandlers: number;
  memoryUsage: number;
}

export interface EventInfo {
  name: string;
  handlerCount: number;
  emissionCount: number;
  lastEmission?: Date;
  averageEmissionInterval: number;
  pipes: string[];
  filters: number;
}

export interface EventBusConfig {
  maxHistory: number;
  enablePersistence: boolean;
  persistenceKey: string;
  debugMode: boolean;
  maxListeners: number;
  enableMetrics: boolean;
}
```

### 步骤2：实现事件总线
**文件**: `src/application/events/EventBus.ts`

```typescript
import { IEventBus, EventHandler, EventContext, EmitOptions, SubscribeOptions, EventSubscription, EventPipe, EventFilter, EventHistoryItem, EventStats, EventInfo, EventBusConfig } from '../interfaces/IEventBus';
import { logger } from '../../infrastructure/utils/Logger';

export class EventBus implements IEventBus {
  private handlers = new Map<string, Set<EventHandlerWrapper>>();
  private pipes = new Map<string, Set<EventPipe>>();
  private filters = new Map<string, EventFilter>();
  private history = new Map<string, EventHistoryItem[]>();
  private stats = new Map<string, EventMetrics>();
  private config: EventBusConfig = {
    maxHistory: 100,
    enablePersistence: false,
    persistenceKey: 'event_bus_history',
    debugMode: false,
    maxListeners: 50,
    enableMetrics: true
  };
  private globalStats = {
    totalEmissions: 0,
    totalSubscriptions: 0
  };

  emit<T = any>(eventName: string, data?: T, options: EmitOptions = {}): void {
    const context = this.createEventContext(eventName, options);
    
    if (options.delay && options.delay > 0) {
      setTimeout(() => this.doEmit(eventName, data, context), options.delay);
    } else {
      this.doEmit(eventName, data, context);
    }
  }

  async emitAsync<T = any>(eventName: string, data?: T, options: EmitOptions = {}): Promise<void> {
    const context = this.createEventContext(eventName, options);
    
    if (options.delay && options.delay > 0) {
      await new Promise(resolve => setTimeout(resolve, options.delay));
    }
    
    await this.doEmitAsync(eventName, data, context);
  }

  on<T = any>(eventName: string, handler: EventHandler<T>, options: SubscribeOptions = {}): EventSubscription {
    return this.addHandler(eventName, handler, options);
  }

  once<T = any>(eventName: string, handler: EventHandler<T>, options: SubscribeOptions = {}): EventSubscription {
    const onceOptions = { ...options, once: true };
    return this.addHandler(eventName, handler, onceOptions);
  }

  off(eventName: string, handler?: EventHandler): void {
    const eventHandlers = this.handlers.get(eventName);
    if (!eventHandlers) return;

    if (handler) {
      // 移除特定处理器
      for (const wrapper of eventHandlers) {
        if (wrapper.originalHandler === handler) {
          eventHandlers.delete(wrapper);
          break;
        }
      }
    } else {
      // 移除所有处理器
      eventHandlers.clear();
    }

    if (eventHandlers.size === 0) {
      this.handlers.delete(eventName);
    }
  }

  pipe(eventName: string, targetEventName: string, transformer?: any): EventPipe {
    const pipe: EventPipe = {
      id: this.generateId(),
      sourceEvent: eventName,
      targetEvent: targetEventName,
      transformer,
      remove: () => this.unpipe(eventName, targetEventName)
    };

    if (!this.pipes.has(eventName)) {
      this.pipes.set(eventName, new Set());
    }
    
    this.pipes.get(eventName)!.add(pipe);

    // 添加处理器来执行管道
    this.on(eventName, (data, context) => {
      const transformedData = transformer ? transformer(data, context) : data;
      this.emit(targetEventName, transformedData, {
        source: `pipe:${eventName}`,
        metadata: { ...context.metadata, pipeId: pipe.id }
      });
    });

    return pipe;
  }

  unpipe(eventName: string, targetEventName?: string): void {
    const eventPipes = this.pipes.get(eventName);
    if (!eventPipes) return;

    if (targetEventName) {
      for (const pipe of eventPipes) {
        if (pipe.targetEvent === targetEventName) {
          eventPipes.delete(pipe);
          break;
        }
      }
    } else {
      eventPipes.clear();
    }

    if (eventPipes.size === 0) {
      this.pipes.delete(eventName);
    }
  }

  filter(eventName: string, predicate: any): EventFilter {
    const filter: EventFilter = {
      id: this.generateId(),
      eventName,
      predicate,
      remove: () => this.unfilter(eventName)
    };

    this.filters.set(eventName, filter);
    return filter;
  }

  unfilter(eventName: string): void {
    this.filters.delete(eventName);
  }

  enableHistory(eventName?: string, maxHistory: number = this.config.maxHistory): void {
    if (eventName) {
      if (!this.history.has(eventName)) {
        this.history.set(eventName, []);
      }
    } else {
      // 为所有事件启用历史
      this.config.maxHistory = maxHistory;
    }
  }

  disableHistory(eventName?: string): void {
    if (eventName) {
      this.history.delete(eventName);
    } else {
      this.history.clear();
    }
  }

  getHistory(eventName?: string): EventHistoryItem[] {
    if (eventName) {
      return this.history.get(eventName) || [];
    } else {
      // 返回所有历史
      const allHistory: EventHistoryItem[] = [];
      this.history.forEach(items => allHistory.push(...items));
      return allHistory.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
    }
  }

  clearHistory(eventName?: string): void {
    if (eventName) {
      const eventHistory = this.history.get(eventName);
      if (eventHistory) {
        eventHistory.length = 0;
      }
    } else {
      this.history.forEach(items => items.length = 0);
    }
  }

  replay(eventName: string, fromTime?: Date, toTime?: Date): void {
    const eventHistory = this.history.get(eventName) || [];
    
    const filteredHistory = eventHistory.filter(item => {
      if (fromTime && item.timestamp < fromTime) return false;
      if (toTime && item.timestamp > toTime) return false;
      return true;
    });

    filteredHistory.forEach(item => {
      this.emit(item.eventName, item.data, {
        source: 'replay',
        metadata: { ...item.context.metadata, replayedFrom: item.id }
      });
    });
  }

  replayAll(fromTime?: Date, toTime?: Date): void {
    const allHistory = this.getHistory();
    
    const filteredHistory = allHistory.filter(item => {
      if (fromTime && item.timestamp < fromTime) return false;
      if (toTime && item.timestamp > toTime) return false;
      return true;
    });

    filteredHistory.forEach(item => {
      this.emit(item.eventName, item.data, {
        source: 'replay',
        metadata: { ...item.context.metadata, replayedFrom: item.id }
      });
    });
  }

  getEventStats(): EventStats {
    const eventCounts: Record<string, number> = {};
    let totalHandlers = 0;

    this.stats.forEach((metrics, eventName) => {
      eventCounts[eventName] = metrics.emissionCount;
    });

    this.handlers.forEach(handlers => {
      totalHandlers += handlers.size;
    });

    return {
      totalEvents: this.handlers.size,
      totalEmissions: this.globalStats.totalEmissions,
      totalSubscriptions: this.globalStats.totalSubscriptions,
      eventCounts,
      averageHandlers: this.handlers.size > 0 ? totalHandlers / this.handlers.size : 0,
      memoryUsage: this.estimateMemoryUsage()
    };
  }

  getEventInfo(eventName: string): EventInfo {
    const handlers = this.handlers.get(eventName);
    const metrics = this.stats.get(eventName);
    const pipes = this.pipes.get(eventName);

    return {
      name: eventName,
      handlerCount: handlers ? handlers.size : 0,
      emissionCount: metrics ? metrics.emissionCount : 0,
      lastEmission: metrics ? metrics.lastEmission : undefined,
      averageEmissionInterval: metrics ? metrics.averageInterval : 0,
      pipes: pipes ? Array.from(pipes).map(p => p.targetEvent) : [],
      filters: this.filters.has(eventName) ? 1 : 0
    };
  }

  listEvents(): string[] {
    return Array.from(this.handlers.keys());
  }

  hasListeners(eventName: string): boolean {
    const handlers = this.handlers.get(eventName);
    return handlers ? handlers.size > 0 : false;
  }

  removeAllListeners(eventName?: string): void {
    if (eventName) {
      this.handlers.delete(eventName);
    } else {
      this.handlers.clear();
    }
  }

  enableDebug(eventName?: string): void {
    if (eventName) {
      // 为特定事件启用调试
      this.on(eventName, (data, context) => {
        console.log(`[EventBus Debug] ${eventName}:`, { data, context });
      });
    } else {
      this.config.debugMode = true;
    }
  }

  disableDebug(eventName?: string): void {
    if (!eventName) {
      this.config.debugMode = false;
    }
    // 特定事件的调试禁用需要更复杂的实现
  }

  enablePersistence(eventName?: string): void {
    this.config.enablePersistence = true;
    if (eventName) {
      this.enableHistory(eventName);
    }
  }

  disablePersistence(eventName?: string): void {
    if (!eventName) {
      this.config.enablePersistence = false;
    }
  }

  async persistEvents(): Promise<void> {
    if (!this.config.enablePersistence) return;

    try {
      const persistData = {
        history: Object.fromEntries(this.history.entries()),
        stats: Object.fromEntries(this.stats.entries()),
        timestamp: new Date().toISOString()
      };

      localStorage.setItem(this.config.persistenceKey, JSON.stringify(persistData));
      logger.info('事件历史已持久化');
    } catch (error) {
      logger.error('事件持久化失败:', error);
    }
  }

  async restoreEvents(): Promise<void> {
    if (!this.config.enablePersistence) return;

    try {
      const persistedData = localStorage.getItem(this.config.persistenceKey);
      if (!persistedData) return;

      const { history, stats } = JSON.parse(persistedData);
      
      // 恢复历史
      Object.entries(history).forEach(([eventName, items]: [string, any]) => {
        this.history.set(eventName, items.map((item: any) => ({
          ...item,
          timestamp: new Date(item.timestamp)
        })));
      });

      // 恢复统计
      Object.entries(stats).forEach(([eventName, metrics]: [string, any]) => {
        this.stats.set(eventName, {
          ...metrics,
          lastEmission: metrics.lastEmission ? new Date(metrics.lastEmission) : undefined
        });
      });

      logger.info('事件历史已恢复');
    } catch (error) {
      logger.error('事件恢复失败:', error);
    }
  }

  // 私有方法
  private doEmit<T = any>(eventName: string, data: T, context: EventContext): void {
    // 更新统计
    this.updateStats(eventName);

    // 记录历史
    this.recordHistory(eventName, data, context);

    // 调试输出
    if (this.config.debugMode) {
      console.log(`[EventBus] ${eventName}:`, { data, context });
    }

    // 应用过滤器
    const filter = this.filters.get(eventName);
    if (filter && !filter.predicate(data, context)) {
      return;
    }

    // 获取处理器并按优先级排序
    const handlers = this.handlers.get(eventName);
    if (!handlers) return;

    const sortedHandlers = Array.from(handlers).sort((a, b) => 
      (b.options.priority || 1) - (a.options.priority || 1)
    );

    // 执行处理器
    sortedHandlers.forEach(wrapper => {
      try {
        // 应用防抖和节流
        if (this.shouldSkipHandler(wrapper)) return;

        const result = wrapper.handler(data, context);
        
        // 处理异步结果
        if (result instanceof Promise) {
          result.catch(error => {
            logger.error(`异步事件处理器失败 [${eventName}]:`, error);
          });
        }

        // 一次性处理器
        if (wrapper.options.once) {
          handlers.delete(wrapper);
        }

        // 更新处理器统计
        wrapper.callCount++;
        wrapper.lastCall = new Date();
      } catch (error) {
        logger.error(`事件处理器失败 [${eventName}]:`, error);
      }
    });
  }

  private async doEmitAsync<T = any>(eventName: string, data: T, context: EventContext): Promise<void> {
    // 更新统计
    this.updateStats(eventName);

    // 记录历史
    this.recordHistory(eventName, data, context);

    // 应用过滤器
    const filter = this.filters.get(eventName);
    if (filter && !filter.predicate(data, context)) {
      return;
    }

    // 获取处理器并按优先级排序
    const handlers = this.handlers.get(eventName);
    if (!handlers) return;

    const sortedHandlers = Array.from(handlers).sort((a, b) => 
      (b.options.priority || 1) - (a.options.priority || 1)
    );

    // 并行执行所有处理器
    const promises = sortedHandlers.map(async wrapper => {
      try {
        if (this.shouldSkipHandler(wrapper)) return;

        await wrapper.handler(data, context);

        // 一次性处理器
        if (wrapper.options.once) {
          handlers.delete(wrapper);
        }

        // 更新处理器统计
        wrapper.callCount++;
        wrapper.lastCall = new Date();
      } catch (error) {
        logger.error(`异步事件处理器失败 [${eventName}]:`, error);
      }
    });

    await Promise.all(promises);
  }

  private addHandler<T = any>(eventName: string, handler: EventHandler<T>, options: SubscribeOptions): EventSubscription {
    if (!this.handlers.has(eventName)) {
      this.handlers.set(eventName, new Set());
    }

    const handlers = this.handlers.get(eventName)!;
    
    // 检查监听器数量限制
    if (handlers.size >= this.config.maxListeners) {
      throw new Error(`事件 ${eventName} 的监听器数量已达到上限 ${this.config.maxListeners}`);
    }

    const wrapper: EventHandlerWrapper = {
      id: this.generateId(),
      originalHandler: handler,
      handler: options.filter ? 
        (data, context) => {
          if (options.filter!(data, context)) {
            handler(data, context);
          }
        } : handler,
      options,
      callCount: 0,
      createdAt: new Date()
    };

    handlers.add(wrapper);
    this.globalStats.totalSubscriptions++;

    const subscription: EventSubscription = {
      id: wrapper.id,
      eventName,
      handler,
      options,
      unsubscribe: () => {
        handlers.delete(wrapper);
        if (handlers.size === 0) {
          this.handlers.delete(eventName);
        }
      }
    };

    return subscription;
  }

  private createEventContext(eventName: string, options: EmitOptions): EventContext {
    return {
      eventName,
      timestamp: new Date(),
      id: this.generateId(),
      source: options.source,
      metadata: options.metadata
    };
  }

  private updateStats(eventName: string): void {
    if (!this.config.enableMetrics) return;

    if (!this.stats.has(eventName)) {
      this.stats.set(eventName, {
        emissionCount: 0,
        lastEmission: undefined,
        averageInterval: 0,
        intervals: []
      });
    }

    const metrics = this.stats.get(eventName)!;
    const now = new Date();
    
    if (metrics.lastEmission) {
      const interval = now.getTime() - metrics.lastEmission.getTime();
      metrics.intervals.push(interval);
      
      // 保持最近100个间隔
      if (metrics.intervals.length > 100) {
        metrics.intervals.shift();
      }
      
      metrics.averageInterval = metrics.intervals.reduce((sum, i) => sum + i, 0) / metrics.intervals.length;
    }

    metrics.emissionCount++;
    metrics.lastEmission = now;
    this.globalStats.totalEmissions++;
  }

  private recordHistory<T = any>(eventName: string, data: T, context: EventContext): void {
    const eventHistory = this.history.get(eventName);
    if (!eventHistory) return;

    const historyItem: EventHistoryItem = {
      id: context.id,
      eventName,
      data,
      context,
      timestamp: context.timestamp
    };

    eventHistory.push(historyItem);

    // 限制历史记录大小
    if (eventHistory.length > this.config.maxHistory) {
      eventHistory.shift();
    }
  }

  private shouldSkipHandler(wrapper: EventHandlerWrapper): boolean {
    const now = Date.now();
    
    // 防抖检查
    if (wrapper.options.debounce && wrapper.lastCall) {
      const timeSinceLastCall = now - wrapper.lastCall.getTime();
      if (timeSinceLastCall < wrapper.options.debounce) {
        return true;
      }
    }

    // 节流检查
    if (wrapper.options.throttle && wrapper.lastCall) {
      const timeSinceLastCall = now - wrapper.lastCall.getTime();
      if (timeSinceLastCall < wrapper.options.throttle) {
        return true;
      }
    }

    return false;
  }

  private generateId(): string {
    return `event_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private estimateMemoryUsage(): number {
    let usage = 0;
    
    // 估算处理器内存使用
    this.handlers.forEach(handlers => {
      usage += handlers.size * 200; // 每个处理器大约200字节
    });

    // 估算历史记录内存使用
    this.history.forEach(items => {
      items.forEach(item => {
        usage += JSON.stringify(item).length * 2;
      });
    });

    return usage;
  }
}

interface EventHandlerWrapper {
  id: string;
  originalHandler: EventHandler;
  handler: EventHandler;
  options: SubscribeOptions;
  callCount: number;
  createdAt: Date;
  lastCall?: Date;
}

interface EventMetrics {
  emissionCount: number;
  lastEmission?: Date;
  averageInterval: number;
  intervals: number[];
}
```

## ✅ 验收标准

### 功能验收
- [ ] IEventBus接口定义完整
- [ ] EventBus实现所有接口方法
- [ ] 支持事件发布和订阅
- [ ] 支持事件管道和过滤
- [ ] 支持事件历史和重放
- [ ] 支持事件统计和调试

### 质量验收
- [ ] TypeScript编译无错误
- [ ] 事件处理性能良好
- [ ] 错误处理完善
- [ ] 内存使用合理

### 功能验收
- [ ] 事件传递及时准确
- [ ] 事件过滤有效
- [ ] 事件历史记录正确

## 🔄 幂等性保证

### 执行前检查
```bash
#!/bin/bash
echo "🔍 检查任务4.2执行状态..."

if [ ! -f "src/application/state/StateManager.ts" ]; then
  echo "❌ 依赖任务Task-4.1未完成，请先执行Task-4.1"
  exit 1
fi

if [ -f "src/application/events/EventBus.ts" ]; then
  echo "⚠️  事件总线已存在，任务可能已完成"
  read -p "是否继续？(y/N): " confirm
  if [ "$confirm" != "y" ]; then
    exit 1
  fi
fi

echo "✅ 前置检查完成，可以执行任务4.2"
```

### 执行后验证
```bash
#!/bin/bash
echo "🔍 验证任务4.2执行结果..."

files=(
  "src/application/interfaces/IEventBus.ts"
  "src/application/events/EventBus.ts"
)

all_created=true
for file in "${files[@]}"; do
  if [ -f "$file" ]; then
    echo "✅ $file 创建成功"
  else
    echo "❌ $file 创建失败"
    all_created=false
  fi
done

if npm run type-check > /dev/null 2>&1; then
  echo "✅ TypeScript编译检查通过"
else
  echo "❌ TypeScript编译检查失败"
  all_created=false
fi

if [ "$all_created" = true ]; then
  echo "🎉 任务4.2执行成功！"
  exit 0
else
  echo "💥 任务4.2执行失败，请检查错误信息"
  exit 1
fi
```

## 📝 完成确认

任务完成后，请在检查清单中标记：
- [ ] 任务4.2已完成
- [ ] 事件总线接口定义完成
- [ ] 事件总线实现完成
- [ ] 事件通信功能测试通过
- [ ] 准备执行任务4.3

## 🔗 相关链接

- [上一个任务：Task-4.1 实现状态管理器](./task-4.1-state-manager.md)
- [下一个任务：Task-4.3 实现应用服务](./task-4.3-application-service.md)
- [重构检查清单](../00-refactoring-checklist.md)
