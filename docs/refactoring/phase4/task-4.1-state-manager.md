# 任务4.1：实现状态管理器

## 📋 任务概述

**任务ID**: Task-4.1  
**任务名称**: 实现状态管理器  
**所属阶段**: 阶段4 - 应用层重构  
**预计时间**: 3小时  
**依赖任务**: Phase-3 (业务层重构完成)  
**后续任务**: Task-4.2 (实现事件总线)  

## 🎯 任务目标

实现应用状态管理器，提供全局状态管理功能，包括状态存储、状态变更、状态持久化和状态同步。

## 🛠️ 实施步骤

### 步骤1：定义状态管理器接口
**文件**: `src/application/interfaces/IStateManager.ts`

```typescript
export interface IStateManager {
  // 状态操作
  getState<T = any>(key: string): T | undefined;
  setState<T = any>(key: string, value: T): void;
  updateState<T = any>(key: string, updater: (current: T) => T): void;
  removeState(key: string): void;
  
  // 批量操作
  getStates(keys: string[]): Record<string, any>;
  setStates(states: Record<string, any>): void;
  
  // 状态订阅
  subscribe<T = any>(key: string, callback: StateChangeCallback<T>): () => void;
  subscribeAll(callback: GlobalStateChangeCallback): () => void;
  
  // 状态持久化
  persist(keys?: string[]): Promise<void>;
  restore(keys?: string[]): Promise<void>;
  
  // 状态快照
  createSnapshot(): StateSnapshot;
  restoreSnapshot(snapshot: StateSnapshot): void;
  
  // 状态历史
  enableHistory(key: string, maxHistory?: number): void;
  disableHistory(key: string): void;
  getHistory(key: string): StateHistory[];
  undo(key: string): boolean;
  redo(key: string): boolean;
  
  // 状态验证
  setValidator<T = any>(key: string, validator: StateValidator<T>): void;
  removeValidator(key: string): void;
  
  // 状态计算
  setComputed<T = any>(key: string, computer: StateComputer<T>): void;
  removeComputed(key: string): void;
  
  // 状态管理
  clear(): void;
  reset(keys?: string[]): void;
  getStateInfo(): StateInfo;
}

export type StateChangeCallback<T = any> = (newValue: T, oldValue: T, key: string) => void;
export type GlobalStateChangeCallback = (changes: StateChange[]) => void;
export type StateValidator<T = any> = (value: T) => ValidationResult;
export type StateComputer<T = any> = (state: Record<string, any>) => T;

export interface StateChange {
  key: string;
  newValue: any;
  oldValue: any;
  timestamp: Date;
  type: StateChangeType;
}

export enum StateChangeType {
  SET = 'set',
  UPDATE = 'update',
  REMOVE = 'remove',
  RESTORE = 'restore'
}

export interface ValidationResult {
  valid: boolean;
  errors: string[];
}

export interface StateSnapshot {
  id: string;
  timestamp: Date;
  states: Record<string, any>;
  metadata: Record<string, any>;
}

export interface StateHistory {
  value: any;
  timestamp: Date;
  action: string;
}

export interface StateInfo {
  totalKeys: number;
  persistedKeys: number;
  subscribedKeys: number;
  computedKeys: number;
  memoryUsage: number;
  lastPersist?: Date;
  lastRestore?: Date;
}

export interface StateConfig {
  persistenceEnabled: boolean;
  persistenceKey: string;
  autoSave: boolean;
  autoSaveInterval: number;
  maxHistorySize: number;
  enableDevTools: boolean;
}
```

### 步骤2：实现状态管理器
**文件**: `src/application/state/StateManager.ts`

```typescript
import { IStateManager, StateChangeCallback, GlobalStateChangeCallback, StateSnapshot, StateHistory, StateInfo, StateConfig } from '../interfaces/IStateManager';
import { IEventEmitter } from '../../infrastructure/utils/EventEmitter';
import { logger } from '../../infrastructure/utils/Logger';

export class StateManager implements IStateManager {
  private states = new Map<string, any>();
  private subscribers = new Map<string, Set<StateChangeCallback>>();
  private globalSubscribers = new Set<GlobalStateChangeCallback>();
  private validators = new Map<string, any>();
  private computers = new Map<string, any>();
  private histories = new Map<string, StateHistory[]>();
  private historyPointers = new Map<string, number>();
  private config: StateConfig = {
    persistenceEnabled: true,
    persistenceKey: 'app_state',
    autoSave: true,
    autoSaveInterval: 30000, // 30秒
    maxHistorySize: 50,
    enableDevTools: false
  };
  private autoSaveTimer?: NodeJS.Timeout;

  constructor(private eventEmitter: IEventEmitter) {
    this.initializeAutoSave();
  }

  getState<T = any>(key: string): T | undefined {
    return this.states.get(key) as T;
  }

  setState<T = any>(key: string, value: T): void {
    const oldValue = this.states.get(key);
    
    // 验证新值
    if (this.validators.has(key)) {
      const validator = this.validators.get(key);
      const validationResult = validator(value);
      if (!validationResult.valid) {
        throw new Error(`状态验证失败 [${key}]: ${validationResult.errors.join(', ')}`);
      }
    }

    // 记录历史
    if (this.histories.has(key)) {
      this.addToHistory(key, oldValue, 'set');
    }

    // 设置状态
    this.states.set(key, value);

    // 触发订阅者
    this.notifySubscribers(key, value, oldValue);
    this.notifyGlobalSubscribers([{
      key,
      newValue: value,
      oldValue,
      timestamp: new Date(),
      type: 'set' as any
    }]);

    // 更新计算状态
    this.updateComputedStates();

    logger.debug(`状态已设置: ${key}`, { oldValue, newValue: value });
  }

  updateState<T = any>(key: string, updater: (current: T) => T): void {
    const currentValue = this.getState<T>(key);
    const newValue = updater(currentValue);
    this.setState(key, newValue);
  }

  removeState(key: string): void {
    const oldValue = this.states.get(key);
    
    if (this.states.has(key)) {
      // 记录历史
      if (this.histories.has(key)) {
        this.addToHistory(key, oldValue, 'remove');
      }

      this.states.delete(key);

      // 触发订阅者
      this.notifySubscribers(key, undefined, oldValue);
      this.notifyGlobalSubscribers([{
        key,
        newValue: undefined,
        oldValue,
        timestamp: new Date(),
        type: 'remove' as any
      }]);

      logger.debug(`状态已移除: ${key}`, { oldValue });
    }
  }

  getStates(keys: string[]): Record<string, any> {
    const result: Record<string, any> = {};
    keys.forEach(key => {
      if (this.states.has(key)) {
        result[key] = this.states.get(key);
      }
    });
    return result;
  }

  setStates(states: Record<string, any>): void {
    const changes: any[] = [];
    
    Object.entries(states).forEach(([key, value]) => {
      const oldValue = this.states.get(key);
      
      // 验证
      if (this.validators.has(key)) {
        const validator = this.validators.get(key);
        const validationResult = validator(value);
        if (!validationResult.valid) {
          throw new Error(`状态验证失败 [${key}]: ${validationResult.errors.join(', ')}`);
        }
      }

      // 记录历史
      if (this.histories.has(key)) {
        this.addToHistory(key, oldValue, 'set');
      }

      this.states.set(key, value);
      
      changes.push({
        key,
        newValue: value,
        oldValue,
        timestamp: new Date(),
        type: 'set' as any
      });

      // 触发单个订阅者
      this.notifySubscribers(key, value, oldValue);
    });

    // 触发全局订阅者
    this.notifyGlobalSubscribers(changes);

    // 更新计算状态
    this.updateComputedStates();
  }

  subscribe<T = any>(key: string, callback: StateChangeCallback<T>): () => void {
    if (!this.subscribers.has(key)) {
      this.subscribers.set(key, new Set());
    }
    
    this.subscribers.get(key)!.add(callback);
    
    return () => {
      const keySubscribers = this.subscribers.get(key);
      if (keySubscribers) {
        keySubscribers.delete(callback);
        if (keySubscribers.size === 0) {
          this.subscribers.delete(key);
        }
      }
    };
  }

  subscribeAll(callback: GlobalStateChangeCallback): () => void {
    this.globalSubscribers.add(callback);
    
    return () => {
      this.globalSubscribers.delete(callback);
    };
  }

  async persist(keys?: string[]): Promise<void> {
    try {
      const statesToPersist = keys ? 
        this.getStates(keys) : 
        Object.fromEntries(this.states.entries());

      const persistData = {
        states: statesToPersist,
        timestamp: new Date().toISOString(),
        version: '1.0.0'
      };

      localStorage.setItem(this.config.persistenceKey, JSON.stringify(persistData));
      
      this.eventEmitter.emit('statePersisted', { keys: keys || Array.from(this.states.keys()) });
      logger.info('状态已持久化', { keys: keys || 'all' });
    } catch (error) {
      logger.error('状态持久化失败:', error);
      throw error;
    }
  }

  async restore(keys?: string[]): Promise<void> {
    try {
      const persistedData = localStorage.getItem(this.config.persistenceKey);
      if (!persistedData) {
        logger.warn('没有找到持久化的状态数据');
        return;
      }

      const { states } = JSON.parse(persistedData);
      const statesToRestore = keys ? 
        Object.fromEntries(Object.entries(states).filter(([key]) => keys.includes(key))) :
        states;

      this.setStates(statesToRestore);
      
      this.eventEmitter.emit('stateRestored', { keys: Object.keys(statesToRestore) });
      logger.info('状态已恢复', { keys: Object.keys(statesToRestore) });
    } catch (error) {
      logger.error('状态恢复失败:', error);
      throw error;
    }
  }

  createSnapshot(): StateSnapshot {
    const snapshot: StateSnapshot = {
      id: this.generateSnapshotId(),
      timestamp: new Date(),
      states: Object.fromEntries(this.states.entries()),
      metadata: {
        subscriberCount: this.subscribers.size,
        computedCount: this.computers.size
      }
    };

    logger.debug('状态快照已创建', { id: snapshot.id });
    return snapshot;
  }

  restoreSnapshot(snapshot: StateSnapshot): void {
    // 清除当前状态
    this.states.clear();
    
    // 恢复快照状态
    Object.entries(snapshot.states).forEach(([key, value]) => {
      this.states.set(key, value);
    });

    // 触发全局变更通知
    const changes = Object.entries(snapshot.states).map(([key, value]) => ({
      key,
      newValue: value,
      oldValue: undefined,
      timestamp: new Date(),
      type: 'restore' as any
    }));

    this.notifyGlobalSubscribers(changes);
    this.updateComputedStates();

    logger.info('状态快照已恢复', { id: snapshot.id });
  }

  enableHistory(key: string, maxHistory: number = this.config.maxHistorySize): void {
    if (!this.histories.has(key)) {
      this.histories.set(key, []);
      this.historyPointers.set(key, -1);
    }
  }

  disableHistory(key: string): void {
    this.histories.delete(key);
    this.historyPointers.delete(key);
  }

  getHistory(key: string): StateHistory[] {
    return this.histories.get(key) || [];
  }

  undo(key: string): boolean {
    const history = this.histories.get(key);
    const pointer = this.historyPointers.get(key);
    
    if (!history || pointer === undefined || pointer <= 0) {
      return false;
    }

    const newPointer = pointer - 1;
    const targetState = history[newPointer];
    
    this.historyPointers.set(key, newPointer);
    this.states.set(key, targetState.value);
    
    // 触发订阅者（不记录到历史）
    this.notifySubscribers(key, targetState.value, this.states.get(key));
    
    return true;
  }

  redo(key: string): boolean {
    const history = this.histories.get(key);
    const pointer = this.historyPointers.get(key);
    
    if (!history || pointer === undefined || pointer >= history.length - 1) {
      return false;
    }

    const newPointer = pointer + 1;
    const targetState = history[newPointer];
    
    this.historyPointers.set(key, newPointer);
    this.states.set(key, targetState.value);
    
    // 触发订阅者（不记录到历史）
    this.notifySubscribers(key, targetState.value, this.states.get(key));
    
    return true;
  }

  setValidator<T = any>(key: string, validator: any): void {
    this.validators.set(key, validator);
  }

  removeValidator(key: string): void {
    this.validators.delete(key);
  }

  setComputed<T = any>(key: string, computer: any): void {
    this.computers.set(key, computer);
    this.updateComputedState(key);
  }

  removeComputed(key: string): void {
    this.computers.delete(key);
    this.states.delete(key);
  }

  clear(): void {
    this.states.clear();
    this.histories.clear();
    this.historyPointers.clear();
    
    this.notifyGlobalSubscribers([]);
    logger.info('所有状态已清除');
  }

  reset(keys?: string[]): void {
    if (keys) {
      keys.forEach(key => this.removeState(key));
    } else {
      this.clear();
    }
  }

  getStateInfo(): StateInfo {
    return {
      totalKeys: this.states.size,
      persistedKeys: this.states.size, // 简化实现
      subscribedKeys: this.subscribers.size,
      computedKeys: this.computers.size,
      memoryUsage: this.estimateMemoryUsage(),
      lastPersist: undefined,
      lastRestore: undefined
    };
  }

  // 私有方法
  private notifySubscribers(key: string, newValue: any, oldValue: any): void {
    const keySubscribers = this.subscribers.get(key);
    if (keySubscribers) {
      keySubscribers.forEach(callback => {
        try {
          callback(newValue, oldValue, key);
        } catch (error) {
          logger.error(`状态订阅者执行失败 [${key}]:`, error);
        }
      });
    }
  }

  private notifyGlobalSubscribers(changes: any[]): void {
    this.globalSubscribers.forEach(callback => {
      try {
        callback(changes);
      } catch (error) {
        logger.error('全局状态订阅者执行失败:', error);
      }
    });
  }

  private updateComputedStates(): void {
    this.computers.forEach((computer, key) => {
      this.updateComputedState(key);
    });
  }

  private updateComputedState(key: string): void {
    const computer = this.computers.get(key);
    if (computer) {
      try {
        const allStates = Object.fromEntries(this.states.entries());
        const computedValue = computer(allStates);
        
        // 直接设置，避免触发验证和历史记录
        const oldValue = this.states.get(key);
        this.states.set(key, computedValue);
        
        this.notifySubscribers(key, computedValue, oldValue);
      } catch (error) {
        logger.error(`计算状态失败 [${key}]:`, error);
      }
    }
  }

  private addToHistory(key: string, value: any, action: string): void {
    const history = this.histories.get(key);
    if (history) {
      const historyItem: StateHistory = {
        value,
        timestamp: new Date(),
        action
      };

      history.push(historyItem);
      
      // 限制历史记录大小
      if (history.length > this.config.maxHistorySize) {
        history.shift();
      }

      this.historyPointers.set(key, history.length - 1);
    }
  }

  private initializeAutoSave(): void {
    if (this.config.autoSave && this.config.persistenceEnabled) {
      this.autoSaveTimer = setInterval(() => {
        this.persist().catch(error => {
          logger.error('自动保存失败:', error);
        });
      }, this.config.autoSaveInterval);
    }
  }

  private generateSnapshotId(): string {
    return `snapshot_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private estimateMemoryUsage(): number {
    let usage = 0;
    this.states.forEach((value, key) => {
      usage += key.length * 2; // 键的大小
      usage += JSON.stringify(value).length * 2; // 值的大小估算
    });
    return usage;
  }
}
```

## ✅ 验收标准

### 功能验收
- [ ] IStateManager接口定义完整
- [ ] StateManager实现所有接口方法
- [ ] 支持状态的CRUD操作
- [ ] 支持状态订阅和通知机制
- [ ] 支持状态持久化和恢复
- [ ] 支持状态历史和撤销重做
- [ ] 支持状态验证和计算

### 质量验收
- [ ] TypeScript编译无错误
- [ ] 状态管理性能良好
- [ ] 错误处理完善
- [ ] 内存使用合理

### 功能验收
- [ ] 状态变更通知及时
- [ ] 状态持久化可靠
- [ ] 状态验证有效

## 🔄 幂等性保证

### 执行前检查
```bash
#!/bin/bash
echo "🔍 检查任务4.1执行状态..."

if [ ! -f "test/business-layer-test.html" ]; then
  echo "❌ 依赖任务Phase-3未完成，请先完成业务层重构"
  exit 1
fi

if [ -f "src/application/state/StateManager.ts" ]; then
  echo "⚠️  状态管理器已存在，任务可能已完成"
  read -p "是否继续？(y/N): " confirm
  if [ "$confirm" != "y" ]; then
    exit 1
  fi
fi

echo "✅ 前置检查完成，可以执行任务4.1"
```

### 执行后验证
```bash
#!/bin/bash
echo "🔍 验证任务4.1执行结果..."

files=(
  "src/application/interfaces/IStateManager.ts"
  "src/application/state/StateManager.ts"
)

all_created=true
for file in "${files[@]}"; do
  if [ -f "$file" ]; then
    echo "✅ $file 创建成功"
  else
    echo "❌ $file 创建失败"
    all_created=false
  fi
done

if npm run type-check > /dev/null 2>&1; then
  echo "✅ TypeScript编译检查通过"
else
  echo "❌ TypeScript编译检查失败"
  all_created=false
fi

if [ "$all_created" = true ]; then
  echo "🎉 任务4.1执行成功！"
  exit 0
else
  echo "💥 任务4.1执行失败，请检查错误信息"
  exit 1
fi
```

## 📝 完成确认

任务完成后，请在检查清单中标记：
- [ ] 任务4.1已完成
- [ ] 状态管理器接口定义完成
- [ ] 状态管理器实现完成
- [ ] 状态管理功能测试通过
- [ ] 准备执行任务4.2

## 🔗 相关链接

- [上一个阶段：Phase-3 业务层重构](../phase3/task-3.7-business-layer-test-page.md)
- [下一个任务：Task-4.2 实现事件总线](./task-4.2-event-bus.md)
- [重构检查清单](../00-refactoring-checklist.md)
