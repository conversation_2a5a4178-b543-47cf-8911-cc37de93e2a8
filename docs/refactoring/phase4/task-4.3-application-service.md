# 任务4.3：实现应用服务

## 📋 任务概述

**任务ID**: Task-4.3  
**任务名称**: 实现应用服务  
**所属阶段**: 阶段4 - 应用层重构  
**预计时间**: 3小时  
**依赖任务**: Task-4.2 (实现事件总线)  
**后续任务**: Task-4.4 (创建应用层测试页面)  

## 🎯 任务目标

实现应用服务层，协调业务层和表现层，提供服务编排、依赖注入、生命周期管理和错误处理功能。

## 🛠️ 实施步骤

### 步骤1：定义应用服务接口
**文件**: `src/application/interfaces/IApplicationService.ts`

```typescript
import { DataResult } from '../../data/types/DataTypes';

export interface IApplicationService {
  // 服务生命周期
  initialize(): Promise<void>;
  start(): Promise<void>;
  stop(): Promise<void>;
  restart(): Promise<void>;
  
  // 服务状态
  getStatus(): ServiceStatus;
  isRunning(): boolean;
  getHealth(): Promise<HealthStatus>;
  
  // 服务管理
  registerService<T>(name: string, service: T, config?: ServiceConfig): void;
  unregisterService(name: string): void;
  getService<T>(name: string): T | undefined;
  listServices(): ServiceInfo[];
  
  // 依赖注入
  inject<T>(target: any, dependencies: DependencyMap): T;
  resolve<T>(token: string | symbol): T;
  
  // 错误处理
  handleError(error: Error, context?: ErrorContext): void;
  getErrors(): ApplicationError[];
  clearErrors(): void;
  
  // 配置管理
  configure(config: ApplicationConfig): void;
  getConfig(): ApplicationConfig;
  
  // 事件处理
  onServiceEvent(event: ServiceEvent): void;
  emitServiceEvent(event: ServiceEvent): void;
}

export interface ServiceStatus {
  name: string;
  status: ServiceState;
  startTime?: Date;
  uptime: number;
  version: string;
  dependencies: string[];
  health: HealthLevel;
}

export enum ServiceState {
  STOPPED = 'stopped',
  STARTING = 'starting',
  RUNNING = 'running',
  STOPPING = 'stopping',
  ERROR = 'error'
}

export enum HealthLevel {
  HEALTHY = 'healthy',
  WARNING = 'warning',
  UNHEALTHY = 'unhealthy',
  CRITICAL = 'critical'
}

export interface HealthStatus {
  overall: HealthLevel;
  services: Record<string, ServiceHealth>;
  timestamp: Date;
  uptime: number;
}

export interface ServiceHealth {
  status: HealthLevel;
  message?: string;
  metrics?: Record<string, any>;
  lastCheck: Date;
}

export interface ServiceConfig {
  autoStart?: boolean;
  dependencies?: string[];
  timeout?: number;
  retryCount?: number;
  healthCheck?: HealthCheckConfig;
}

export interface HealthCheckConfig {
  enabled: boolean;
  interval: number;
  timeout: number;
  retries: number;
}

export interface ServiceInfo {
  name: string;
  type: string;
  status: ServiceState;
  config: ServiceConfig;
  metadata: Record<string, any>;
}

export interface DependencyMap {
  [key: string]: string | symbol;
}

export interface ErrorContext {
  service?: string;
  operation?: string;
  metadata?: Record<string, any>;
}

export interface ApplicationError {
  id: string;
  error: Error;
  context?: ErrorContext;
  timestamp: Date;
  resolved: boolean;
}

export interface ServiceEvent {
  type: ServiceEventType;
  service: string;
  data?: any;
  timestamp: Date;
}

export enum ServiceEventType {
  REGISTERED = 'registered',
  UNREGISTERED = 'unregistered',
  STARTED = 'started',
  STOPPED = 'stopped',
  ERROR = 'error',
  HEALTH_CHANGED = 'health_changed'
}

export interface ApplicationConfig {
  name: string;
  version: string;
  environment: string;
  services: Record<string, ServiceConfig>;
  logging: LoggingConfig;
  monitoring: MonitoringConfig;
  security: SecurityConfig;
}

export interface LoggingConfig {
  level: string;
  format: string;
  outputs: string[];
}

export interface MonitoringConfig {
  enabled: boolean;
  interval: number;
  metrics: string[];
}

export interface SecurityConfig {
  authentication: boolean;
  authorization: boolean;
  encryption: boolean;
}
```

### 步骤2：实现应用服务
**文件**: `src/application/services/ApplicationService.ts`

```typescript
import { IApplicationService, ServiceStatus, ServiceState, HealthStatus, HealthLevel, ServiceConfig, ServiceInfo, ApplicationError, ServiceEvent, ApplicationConfig } from '../interfaces/IApplicationService';
import { IStateManager } from '../interfaces/IStateManager';
import { IEventBus } from '../interfaces/IEventBus';
import { logger } from '../../infrastructure/utils/Logger';

export class ApplicationService implements IApplicationService {
  private services = new Map<string, ServiceWrapper>();
  private dependencies = new Map<string, any>();
  private errors: ApplicationError[] = [];
  private status: ServiceState = ServiceState.STOPPED;
  private startTime?: Date;
  private config: ApplicationConfig = this.getDefaultConfig();
  private healthCheckTimer?: NodeJS.Timeout;

  constructor(
    private stateManager: IStateManager,
    private eventBus: IEventBus
  ) {
    this.setupEventHandlers();
  }

  async initialize(): Promise<void> {
    try {
      logger.info('初始化应用服务...');
      
      // 初始化核心服务
      this.registerCoreServices();
      
      // 加载配置
      await this.loadConfiguration();
      
      // 初始化依赖注入容器
      this.initializeDependencyContainer();
      
      logger.info('应用服务初始化完成');
    } catch (error) {
      logger.error('应用服务初始化失败:', error);
      throw error;
    }
  }

  async start(): Promise<void> {
    try {
      if (this.status === ServiceState.RUNNING) {
        logger.warn('应用服务已在运行中');
        return;
      }

      this.status = ServiceState.STARTING;
      this.startTime = new Date();
      
      logger.info('启动应用服务...');
      
      // 按依赖顺序启动服务
      await this.startServicesInOrder();
      
      // 启动健康检查
      this.startHealthCheck();
      
      this.status = ServiceState.RUNNING;
      
      this.emitServiceEvent({
        type: 'started' as any,
        service: 'application',
        timestamp: new Date()
      });
      
      logger.info('应用服务启动完成');
    } catch (error) {
      this.status = ServiceState.ERROR;
      this.handleError(error as Error, { service: 'application', operation: 'start' });
      throw error;
    }
  }

  async stop(): Promise<void> {
    try {
      if (this.status === ServiceState.STOPPED) {
        logger.warn('应用服务已停止');
        return;
      }

      this.status = ServiceState.STOPPING;
      
      logger.info('停止应用服务...');
      
      // 停止健康检查
      this.stopHealthCheck();
      
      // 按反向依赖顺序停止服务
      await this.stopServicesInOrder();
      
      this.status = ServiceState.STOPPED;
      this.startTime = undefined;
      
      this.emitServiceEvent({
        type: 'stopped' as any,
        service: 'application',
        timestamp: new Date()
      });
      
      logger.info('应用服务已停止');
    } catch (error) {
      this.status = ServiceState.ERROR;
      this.handleError(error as Error, { service: 'application', operation: 'stop' });
      throw error;
    }
  }

  async restart(): Promise<void> {
    await this.stop();
    await this.start();
  }

  getStatus(): ServiceStatus {
    return {
      name: 'application',
      status: this.status,
      startTime: this.startTime,
      uptime: this.startTime ? Date.now() - this.startTime.getTime() : 0,
      version: this.config.version,
      dependencies: Array.from(this.services.keys()),
      health: this.getOverallHealth()
    };
  }

  isRunning(): boolean {
    return this.status === ServiceState.RUNNING;
  }

  async getHealth(): Promise<HealthStatus> {
    const serviceHealths: Record<string, any> = {};
    
    for (const [name, wrapper] of this.services) {
      serviceHealths[name] = await this.checkServiceHealth(wrapper);
    }

    const overallHealth = this.calculateOverallHealth(serviceHealths);

    return {
      overall: overallHealth,
      services: serviceHealths,
      timestamp: new Date(),
      uptime: this.startTime ? Date.now() - this.startTime.getTime() : 0
    };
  }

  registerService<T>(name: string, service: T, config: ServiceConfig = {}): void {
    if (this.services.has(name)) {
      throw new Error(`服务已存在: ${name}`);
    }

    const wrapper: ServiceWrapper = {
      name,
      service,
      config: { ...this.getDefaultServiceConfig(), ...config },
      status: ServiceState.STOPPED,
      health: HealthLevel.HEALTHY,
      lastHealthCheck: new Date()
    };

    this.services.set(name, wrapper);
    this.dependencies.set(name, service);

    this.emitServiceEvent({
      type: 'registered' as any,
      service: name,
      data: { config },
      timestamp: new Date()
    });

    logger.info(`服务已注册: ${name}`);
  }

  unregisterService(name: string): void {
    const wrapper = this.services.get(name);
    if (!wrapper) {
      logger.warn(`服务不存在: ${name}`);
      return;
    }

    // 停止服务
    if (wrapper.status === ServiceState.RUNNING) {
      this.stopService(wrapper);
    }

    this.services.delete(name);
    this.dependencies.delete(name);

    this.emitServiceEvent({
      type: 'unregistered' as any,
      service: name,
      timestamp: new Date()
    });

    logger.info(`服务已注销: ${name}`);
  }

  getService<T>(name: string): T | undefined {
    return this.dependencies.get(name) as T;
  }

  listServices(): ServiceInfo[] {
    return Array.from(this.services.values()).map(wrapper => ({
      name: wrapper.name,
      type: typeof wrapper.service,
      status: wrapper.status,
      config: wrapper.config,
      metadata: {
        health: wrapper.health,
        lastHealthCheck: wrapper.lastHealthCheck
      }
    }));
  }

  inject<T>(target: any, dependencies: any): T {
    // 简化的依赖注入实现
    Object.entries(dependencies).forEach(([property, token]) => {
      const dependency = this.resolve(token as string);
      if (dependency) {
        target[property] = dependency;
      }
    });
    
    return target;
  }

  resolve<T>(token: string | symbol): T {
    const tokenStr = typeof token === 'symbol' ? token.toString() : token;
    return this.dependencies.get(tokenStr) as T;
  }

  handleError(error: Error, context?: any): void {
    const appError: ApplicationError = {
      id: this.generateErrorId(),
      error,
      context,
      timestamp: new Date(),
      resolved: false
    };

    this.errors.push(appError);
    
    // 限制错误数量
    if (this.errors.length > 100) {
      this.errors.shift();
    }

    // 发射错误事件
    this.emitServiceEvent({
      type: 'error' as any,
      service: context?.service || 'application',
      data: { error: error.message, context },
      timestamp: new Date()
    });

    logger.error('应用错误:', error, context);
  }

  getErrors(): ApplicationError[] {
    return [...this.errors];
  }

  clearErrors(): void {
    this.errors.length = 0;
  }

  configure(config: ApplicationConfig): void {
    this.config = { ...this.config, ...config };
    
    // 应用服务配置
    Object.entries(config.services || {}).forEach(([name, serviceConfig]) => {
      const wrapper = this.services.get(name);
      if (wrapper) {
        wrapper.config = { ...wrapper.config, ...serviceConfig };
      }
    });

    logger.info('应用配置已更新');
  }

  getConfig(): ApplicationConfig {
    return { ...this.config };
  }

  onServiceEvent(event: ServiceEvent): void {
    // 处理服务事件
    switch (event.type) {
      case 'error':
        this.handleServiceError(event);
        break;
      case 'health_changed':
        this.handleHealthChange(event);
        break;
    }
  }

  emitServiceEvent(event: ServiceEvent): void {
    this.eventBus.emit('service:event', event);
  }

  // 私有方法
  private setupEventHandlers(): void {
    this.eventBus.on('service:event', (event: ServiceEvent) => {
      this.onServiceEvent(event);
    });
  }

  private registerCoreServices(): void {
    // 注册核心服务
    this.registerService('stateManager', this.stateManager, {
      autoStart: true,
      dependencies: []
    });

    this.registerService('eventBus', this.eventBus, {
      autoStart: true,
      dependencies: []
    });
  }

  private async loadConfiguration(): Promise<void> {
    try {
      // 从状态管理器加载配置
      const savedConfig = this.stateManager.getState<ApplicationConfig>('app:config');
      if (savedConfig) {
        this.config = { ...this.config, ...savedConfig };
      }
    } catch (error) {
      logger.warn('加载配置失败，使用默认配置:', error);
    }
  }

  private initializeDependencyContainer(): void {
    // 初始化依赖注入容器
    this.dependencies.set('logger', logger);
    this.dependencies.set('config', this.config);
  }

  private async startServicesInOrder(): Promise<void> {
    const sortedServices = this.topologicalSort();
    
    for (const wrapper of sortedServices) {
      if (wrapper.config.autoStart) {
        await this.startService(wrapper);
      }
    }
  }

  private async stopServicesInOrder(): Promise<void> {
    const sortedServices = this.topologicalSort().reverse();
    
    for (const wrapper of sortedServices) {
      if (wrapper.status === ServiceState.RUNNING) {
        await this.stopService(wrapper);
      }
    }
  }

  private async startService(wrapper: ServiceWrapper): Promise<void> {
    try {
      wrapper.status = ServiceState.STARTING;
      
      // 检查依赖
      await this.checkDependencies(wrapper);
      
      // 启动服务
      if (typeof wrapper.service === 'object' && 'start' in wrapper.service) {
        await (wrapper.service as any).start();
      }
      
      wrapper.status = ServiceState.RUNNING;
      
      logger.info(`服务已启动: ${wrapper.name}`);
    } catch (error) {
      wrapper.status = ServiceState.ERROR;
      throw error;
    }
  }

  private async stopService(wrapper: ServiceWrapper): Promise<void> {
    try {
      wrapper.status = ServiceState.STOPPING;
      
      // 停止服务
      if (typeof wrapper.service === 'object' && 'stop' in wrapper.service) {
        await (wrapper.service as any).stop();
      }
      
      wrapper.status = ServiceState.STOPPED;
      
      logger.info(`服务已停止: ${wrapper.name}`);
    } catch (error) {
      wrapper.status = ServiceState.ERROR;
      throw error;
    }
  }

  private async checkDependencies(wrapper: ServiceWrapper): Promise<void> {
    if (!wrapper.config.dependencies) return;

    for (const depName of wrapper.config.dependencies) {
      const depWrapper = this.services.get(depName);
      if (!depWrapper || depWrapper.status !== ServiceState.RUNNING) {
        throw new Error(`依赖服务未运行: ${depName}`);
      }
    }
  }

  private topologicalSort(): ServiceWrapper[] {
    // 简化的拓扑排序实现
    const visited = new Set<string>();
    const result: ServiceWrapper[] = [];
    
    const visit = (wrapper: ServiceWrapper) => {
      if (visited.has(wrapper.name)) return;
      
      visited.add(wrapper.name);
      
      // 先访问依赖
      if (wrapper.config.dependencies) {
        for (const depName of wrapper.config.dependencies) {
          const depWrapper = this.services.get(depName);
          if (depWrapper) {
            visit(depWrapper);
          }
        }
      }
      
      result.push(wrapper);
    };

    this.services.forEach(wrapper => visit(wrapper));
    
    return result;
  }

  private startHealthCheck(): void {
    if (this.config.monitoring?.enabled) {
      this.healthCheckTimer = setInterval(() => {
        this.performHealthCheck();
      }, this.config.monitoring.interval || 30000);
    }
  }

  private stopHealthCheck(): void {
    if (this.healthCheckTimer) {
      clearInterval(this.healthCheckTimer);
      this.healthCheckTimer = undefined;
    }
  }

  private async performHealthCheck(): Promise<void> {
    for (const wrapper of this.services.values()) {
      if (wrapper.config.healthCheck?.enabled) {
        const health = await this.checkServiceHealth(wrapper);
        
        if (health.status !== wrapper.health) {
          wrapper.health = health.status;
          
          this.emitServiceEvent({
            type: 'health_changed' as any,
            service: wrapper.name,
            data: { oldHealth: wrapper.health, newHealth: health.status },
            timestamp: new Date()
          });
        }
      }
    }
  }

  private async checkServiceHealth(wrapper: ServiceWrapper): Promise<any> {
    try {
      // 简化的健康检查
      if (typeof wrapper.service === 'object' && 'getHealth' in wrapper.service) {
        return await (wrapper.service as any).getHealth();
      }
      
      return {
        status: wrapper.status === ServiceState.RUNNING ? HealthLevel.HEALTHY : HealthLevel.UNHEALTHY,
        message: `Service is ${wrapper.status}`,
        lastCheck: new Date()
      };
    } catch (error) {
      return {
        status: HealthLevel.UNHEALTHY,
        message: error.message,
        lastCheck: new Date()
      };
    }
  }

  private getOverallHealth(): HealthLevel {
    const healths = Array.from(this.services.values()).map(w => w.health);
    
    if (healths.includes(HealthLevel.CRITICAL)) return HealthLevel.CRITICAL;
    if (healths.includes(HealthLevel.UNHEALTHY)) return HealthLevel.UNHEALTHY;
    if (healths.includes(HealthLevel.WARNING)) return HealthLevel.WARNING;
    
    return HealthLevel.HEALTHY;
  }

  private calculateOverallHealth(serviceHealths: Record<string, any>): HealthLevel {
    const healths = Object.values(serviceHealths).map(h => h.status);
    
    if (healths.includes(HealthLevel.CRITICAL)) return HealthLevel.CRITICAL;
    if (healths.includes(HealthLevel.UNHEALTHY)) return HealthLevel.UNHEALTHY;
    if (healths.includes(HealthLevel.WARNING)) return HealthLevel.WARNING;
    
    return HealthLevel.HEALTHY;
  }

  private handleServiceError(event: ServiceEvent): void {
    logger.error(`服务错误 [${event.service}]:`, event.data);
  }

  private handleHealthChange(event: ServiceEvent): void {
    logger.info(`服务健康状态变更 [${event.service}]:`, event.data);
  }

  private getDefaultConfig(): ApplicationConfig {
    return {
      name: 'MemoryKeeper',
      version: '1.0.0',
      environment: 'development',
      services: {},
      logging: {
        level: 'info',
        format: 'json',
        outputs: ['console']
      },
      monitoring: {
        enabled: true,
        interval: 30000,
        metrics: ['health', 'performance']
      },
      security: {
        authentication: false,
        authorization: false,
        encryption: true
      }
    };
  }

  private getDefaultServiceConfig(): ServiceConfig {
    return {
      autoStart: false,
      dependencies: [],
      timeout: 30000,
      retryCount: 3,
      healthCheck: {
        enabled: true,
        interval: 30000,
        timeout: 5000,
        retries: 3
      }
    };
  }

  private generateErrorId(): string {
    return `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

interface ServiceWrapper {
  name: string;
  service: any;
  config: ServiceConfig;
  status: ServiceState;
  health: HealthLevel;
  lastHealthCheck: Date;
}
```

## ✅ 验收标准

### 功能验收
- [ ] IApplicationService接口定义完整
- [ ] ApplicationService实现所有接口方法
- [ ] 支持服务注册和生命周期管理
- [ ] 支持依赖注入和服务解析
- [ ] 支持健康检查和监控
- [ ] 支持错误处理和事件管理

### 质量验收
- [ ] TypeScript编译无错误
- [ ] 服务管理功能正确
- [ ] 错误处理完善
- [ ] 性能监控有效

### 集成验收
- [ ] 与状态管理器集成正常
- [ ] 与事件总线集成正常
- [ ] 服务依赖解析正确

## 🔄 幂等性保证

### 执行前检查
```bash
#!/bin/bash
echo "🔍 检查任务4.3执行状态..."

if [ ! -f "src/application/events/EventBus.ts" ]; then
  echo "❌ 依赖任务Task-4.2未完成，请先执行Task-4.2"
  exit 1
fi

if [ -f "src/application/services/ApplicationService.ts" ]; then
  echo "⚠️  应用服务已存在，任务可能已完成"
  read -p "是否继续？(y/N): " confirm
  if [ "$confirm" != "y" ]; then
    exit 1
  fi
fi

echo "✅ 前置检查完成，可以执行任务4.3"
```

### 执行后验证
```bash
#!/bin/bash
echo "🔍 验证任务4.3执行结果..."

files=(
  "src/application/interfaces/IApplicationService.ts"
  "src/application/services/ApplicationService.ts"
)

all_created=true
for file in "${files[@]}"; do
  if [ -f "$file" ]; then
    echo "✅ $file 创建成功"
  else
    echo "❌ $file 创建失败"
    all_created=false
  fi
done

if npm run type-check > /dev/null 2>&1; then
  echo "✅ TypeScript编译检查通过"
else
  echo "❌ TypeScript编译检查失败"
  all_created=false
fi

if [ "$all_created" = true ]; then
  echo "🎉 任务4.3执行成功！"
  exit 0
else
  echo "💥 任务4.3执行失败，请检查错误信息"
  exit 1
fi
```

## 📝 完成确认

任务完成后，请在检查清单中标记：
- [ ] 任务4.3已完成
- [ ] 应用服务接口定义完成
- [ ] 应用服务实现完成
- [ ] 服务管理功能测试通过
- [ ] 准备执行任务4.4

## 🔗 相关链接

- [上一个任务：Task-4.2 实现事件总线](./task-4.2-event-bus.md)
- [下一个任务：Task-4.4 创建应用层测试页面](./task-4.4-application-layer-test-page.md)
- [重构检查清单](../00-refactoring-checklist.md)
