# 任务2.2：实现内存Repository

## 📋 任务概述

**任务ID**: Task-2.2  
**任务名称**: 实现内存Repository  
**所属阶段**: 阶段2 - 数据层重构  
**预计时间**: 3小时  
**依赖任务**: Task-2.1 (创建Repository接口)  
**后续任务**: Task-2.3 (实现配置Repository)  

## 🎯 任务目标

实现Memory实体的Repository，提供内存数据的CRUD操作，支持缓存、搜索和批量操作功能。

## 📋 前置条件检查

### 依赖检查
- [ ] Task-2.1 已完成
- [ ] Repository接口已定义
- [ ] 基础设施层可用

### 文件检查
- [ ] `src/data/interfaces/IRepository.ts` 存在
- [ ] `src/data/repositories/BaseRepository.ts` 存在

### 状态检查
```bash
# 检查内存Repository是否已存在
if [ -f "src/data/repositories/MemoryRepository.ts" ]; then
  echo "⚠️  内存Repository已存在，请确认是否需要重新创建"
  exit 1
else
  echo "✅ 可以开始创建内存Repository"
fi
```

## 🛠️ 实施步骤

### 步骤1：定义Memory实体
**文件**: `src/data/entities/Memory.ts`

```typescript
import { BaseEntity } from '../types/DataTypes';

/**
 * 内存实体
 */
export interface Memory extends BaseEntity {
  id: string;
  title: string;
  content: string;
  type: MemoryType;
  tags: string[];
  categoryId?: string;
  attachments: MemoryAttachment[];
  metadata: MemoryMetadata;
  isPrivate: boolean;
  isFavorite: boolean;
  isArchived: boolean;
  historicalDate?: Date;
  createdAt: Date;
  updatedAt: Date;
  version: number;
}

/**
 * 内存类型
 */
export enum MemoryType {
  TEXT = 'text',
  IMAGE = 'image',
  VIDEO = 'video',
  AUDIO = 'audio',
  MIXED = 'mixed'
}

/**
 * 内存附件
 */
export interface MemoryAttachment {
  id: string;
  name: string;
  type: string;
  size: number;
  url: string;
  thumbnailUrl?: string;
  metadata?: Record<string, any>;
}

/**
 * 内存元数据
 */
export interface MemoryMetadata {
  location?: {
    latitude: number;
    longitude: number;
    address?: string;
  };
  weather?: {
    temperature: number;
    condition: string;
    humidity: number;
  };
  mood?: string;
  people?: string[];
  events?: string[];
  customFields?: Record<string, any>;
}

/**
 * 内存搜索条件
 */
export interface MemorySearchCriteria {
  keyword?: string;
  type?: MemoryType;
  tags?: string[];
  categoryId?: string;
  isPrivate?: boolean;
  isFavorite?: boolean;
  isArchived?: boolean;
  dateRange?: {
    start: Date;
    end: Date;
  };
  location?: {
    latitude: number;
    longitude: number;
    radius: number; // 公里
  };
}
```

### 步骤2：实现内存Repository
**文件**: `src/data/repositories/MemoryRepository.ts`

```typescript
import { BaseRepository } from './BaseRepository';
import { ICacheableRepository } from '../interfaces/IRepository';
import { Memory, MemorySearchCriteria, MemoryType } from '../entities/Memory';
import { 
  SearchCriteria, 
  PageRequest, 
  DataResult, 
  FilterOperator 
} from '../types/DataTypes';
import { IStorageProvider } from '../../infrastructure/interfaces/IStorageProvider';
import { ICacheManager } from '../interfaces/ICacheManager';
import { IDataMapper } from '../interfaces/IDataMapper';
import { logger } from '../../infrastructure/utils/Logger';
import { validator } from '../../infrastructure/utils/Validator';

/**
 * 内存Repository实现
 */
export class MemoryRepository extends BaseRepository<Memory> implements ICacheableRepository<Memory> {
  private readonly STORAGE_PREFIX = 'memories/';
  private readonly CACHE_PREFIX = 'memory:';
  private readonly CACHE_TTL = 3600; // 1小时

  constructor(
    private storageProvider: IStorageProvider,
    private cacheManager: ICacheManager,
    private dataMapper: IDataMapper<Memory>
  ) {
    super('Memory');
  }

  /**
   * 根据ID查找内存
   */
  async findById(id: string): Promise<DataResult<Memory | null>> {
    try {
      // 先从缓存查找
      const cacheKey = `${this.CACHE_PREFIX}${id}`;
      const cached = await this.cacheManager.get<Memory>(cacheKey);
      
      if (cached) {
        logger.debug(`从缓存获取内存: ${id}`);
        return { success: true, data: cached };
      }

      // 从存储查找
      const storageKey = `${this.STORAGE_PREFIX}${id}.json`;
      const result = await this.storageProvider.get(storageKey);
      
      if (!result.success) {
        return { success: false, error: result.error };
      }

      if (!result.data) {
        return { success: true, data: null };
      }

      // 数据映射和验证
      const memory = this.dataMapper.fromStorage(result.data);
      const validationResult = this.validateMemory(memory);
      
      if (!validationResult.valid) {
        return { 
          success: false, 
          error: new Error(`内存数据验证失败: ${validationResult.errors.join(', ')}`) 
        };
      }

      // 更新缓存
      await this.cacheManager.set(cacheKey, memory, this.CACHE_TTL);
      
      return { success: true, data: memory };
    } catch (error) {
      logger.error(`查找内存失败 [${id}]:`, error);
      return { success: false, error: error as Error };
    }
  }

  /**
   * 查找所有内存
   */
  async findAll(criteria?: SearchCriteria<Memory>): Promise<DataResult<Memory[]>> {
    try {
      // 获取所有内存文件列表
      const listResult = await this.storageProvider.list(this.STORAGE_PREFIX);
      
      if (!listResult.success) {
        return { success: false, error: listResult.error };
      }

      const keys = listResult.data || [];
      const memories: Memory[] = [];

      // 批量获取内存数据
      for (const key of keys) {
        if (key.endsWith('.json')) {
          const id = key.replace(this.STORAGE_PREFIX, '').replace('.json', '');
          const memoryResult = await this.findById(id);
          
          if (memoryResult.success && memoryResult.data) {
            memories.push(memoryResult.data);
          }
        }
      }

      // 应用搜索条件
      const filteredMemories = this.applySearchCriteria(memories, criteria);
      
      return { success: true, data: filteredMemories };
    } catch (error) {
      logger.error('查找所有内存失败:', error);
      return { success: false, error: error as Error };
    }
  }

  /**
   * 保存内存
   */
  async save(memory: Memory): Promise<DataResult<Memory>> {
    try {
      // 验证内存数据
      const validationResult = this.validateMemory(memory);
      if (!validationResult.valid) {
        return { 
          success: false, 
          error: new Error(`内存数据验证失败: ${validationResult.errors.join(', ')}`) 
        };
      }

      // 设置时间戳
      const now = new Date();
      const isNew = !memory.id;
      
      if (isNew) {
        memory.id = this.generateId();
        memory.createdAt = now;
        memory.version = 1;
      }
      
      memory.updatedAt = now;

      // 保存到存储
      const storageKey = `${this.STORAGE_PREFIX}${memory.id}.json`;
      const storageData = this.dataMapper.toStorage(memory);
      
      const saveResult = await this.storageProvider.put(storageKey, storageData, {
        contentType: 'application/json'
      });

      if (!saveResult.success) {
        return { success: false, error: saveResult.error };
      }

      // 更新缓存
      const cacheKey = `${this.CACHE_PREFIX}${memory.id}`;
      await this.cacheManager.set(cacheKey, memory, this.CACHE_TTL);

      // 发射事件
      if (isNew) {
        this.emitEntityCreated(memory);
      } else {
        // 这里应该获取旧的内存数据，简化处理
        this.emitEntityUpdated(memory, memory);
      }

      logger.info(`内存保存成功: ${memory.id}`);
      return { success: true, data: memory };
    } catch (error) {
      logger.error(`保存内存失败 [${memory.id}]:`, error);
      return { success: false, error: error as Error };
    }
  }

  /**
   * 更新内存
   */
  async update(id: string, updates: Partial<Memory>): Promise<DataResult<Memory>> {
    try {
      // 获取现有内存
      const existingResult = await this.findById(id);
      if (!existingResult.success) {
        return existingResult;
      }

      if (!existingResult.data) {
        return { 
          success: false, 
          error: new Error(`内存不存在: ${id}`) 
        };
      }

      const oldMemory = existingResult.data;
      
      // 合并更新
      const updatedMemory: Memory = {
        ...oldMemory,
        ...updates,
        id, // 确保ID不被更改
        createdAt: oldMemory.createdAt, // 确保创建时间不被更改
        updatedAt: new Date(),
        version: oldMemory.version + 1
      };

      // 保存更新后的内存
      const saveResult = await this.save(updatedMemory);
      
      if (saveResult.success) {
        this.emitEntityUpdated(updatedMemory, oldMemory);
      }

      return saveResult;
    } catch (error) {
      logger.error(`更新内存失败 [${id}]:`, error);
      return { success: false, error: error as Error };
    }
  }

  /**
   * 删除内存
   */
  async delete(id: string): Promise<DataResult<boolean>> {
    try {
      // 检查内存是否存在
      const existsResult = await this.exists(id);
      if (!existsResult.success) {
        return { success: false, error: existsResult.error };
      }

      if (!existsResult.data) {
        return { success: true, data: false }; // 内存不存在，视为删除成功
      }

      // 从存储删除
      const storageKey = `${this.STORAGE_PREFIX}${id}.json`;
      const deleteResult = await this.storageProvider.delete(storageKey);

      if (!deleteResult.success) {
        return { success: false, error: deleteResult.error };
      }

      // 从缓存删除
      const cacheKey = `${this.CACHE_PREFIX}${id}`;
      await this.cacheManager.delete(cacheKey);

      // 发射事件
      this.emitEntityDeleted(id);

      logger.info(`内存删除成功: ${id}`);
      return { success: true, data: true };
    } catch (error) {
      logger.error(`删除内存失败 [${id}]:`, error);
      return { success: false, error: error as Error };
    }
  }

  /**
   * 统计内存数量
   */
  async count(criteria?: SearchCriteria<Memory>): Promise<DataResult<number>> {
    try {
      const allResult = await this.findAll(criteria);
      if (!allResult.success) {
        return { success: false, error: allResult.error };
      }

      return { success: true, data: allResult.data?.length || 0 };
    } catch (error) {
      logger.error('统计内存数量失败:', error);
      return { success: false, error: error as Error };
    }
  }

  /**
   * 分页查询实现
   */
  protected async findAllWithPagination(
    criteria: SearchCriteria<Memory>, 
    page: PageRequest
  ): Promise<DataResult<Memory[]>> {
    try {
      const allResult = await this.findAll(criteria);
      if (!allResult.success) {
        return allResult;
      }

      const memories = allResult.data || [];
      
      // 应用排序
      const sortedMemories = this.applySorting(memories, page.sort || criteria.sort);
      
      // 应用分页
      const startIndex = page.page * page.size;
      const endIndex = startIndex + page.size;
      const pagedMemories = sortedMemories.slice(startIndex, endIndex);

      return { success: true, data: pagedMemories };
    } catch (error) {
      logger.error('分页查询内存失败:', error);
      return { success: false, error: error as Error };
    }
  }

  // 缓存管理方法
  async clearCache(): Promise<void> {
    await this.cacheManager.clear(`${this.CACHE_PREFIX}*`);
  }

  async clearCacheFor(id: string): Promise<void> {
    const cacheKey = `${this.CACHE_PREFIX}${id}`;
    await this.cacheManager.delete(cacheKey);
  }

  async clearCacheBy(criteria: SearchCriteria<Memory>): Promise<void> {
    // 简化实现：清除所有内存缓存
    await this.clearCache();
  }

  async preloadCache(ids: string[]): Promise<void> {
    for (const id of ids) {
      await this.findById(id); // 这会自动加载到缓存
    }
  }

  async preloadCacheBy(criteria: SearchCriteria<Memory>): Promise<void> {
    const result = await this.findAll(criteria);
    if (result.success && result.data) {
      for (const memory of result.data) {
        const cacheKey = `${this.CACHE_PREFIX}${memory.id}`;
        await this.cacheManager.set(cacheKey, memory, this.CACHE_TTL);
      }
    }
  }

  async getCacheStats(): Promise<{
    hitCount: number;
    missCount: number;
    hitRate: number;
    size: number;
  }> {
    // 这里需要缓存管理器支持统计功能
    return {
      hitCount: 0,
      missCount: 0,
      hitRate: 0,
      size: 0
    };
  }

  // 私有辅助方法
  private validateMemory(memory: Memory): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!memory.title || memory.title.trim().length === 0) {
      errors.push('标题不能为空');
    }

    if (!memory.content || memory.content.trim().length === 0) {
      errors.push('内容不能为空');
    }

    if (!Object.values(MemoryType).includes(memory.type)) {
      errors.push('无效的内存类型');
    }

    if (!Array.isArray(memory.tags)) {
      errors.push('标签必须是数组');
    }

    if (!Array.isArray(memory.attachments)) {
      errors.push('附件必须是数组');
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  private generateId(): string {
    return `mem_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private applySearchCriteria(memories: Memory[], criteria?: SearchCriteria<Memory>): Memory[] {
    if (!criteria) {
      return memories;
    }

    let filtered = memories;

    // 应用过滤条件
    if (criteria.filters) {
      for (const filter of criteria.filters) {
        filtered = filtered.filter(memory => {
          const value = memory[filter.property as keyof Memory];
          return this.matchesFilter(value, filter.operator, filter.value, filter.values);
        });
      }
    }

    // 应用搜索关键词
    if (criteria.search) {
      const keyword = criteria.search.toLowerCase();
      filtered = filtered.filter(memory => 
        memory.title.toLowerCase().includes(keyword) ||
        memory.content.toLowerCase().includes(keyword) ||
        memory.tags.some(tag => tag.toLowerCase().includes(keyword))
      );
    }

    return filtered;
  }

  private matchesFilter(value: any, operator: FilterOperator, filterValue: any, filterValues?: any[]): boolean {
    switch (operator) {
      case FilterOperator.EQUALS:
        return value === filterValue;
      case FilterOperator.NOT_EQUALS:
        return value !== filterValue;
      case FilterOperator.GREATER_THAN:
        return value > filterValue;
      case FilterOperator.GREATER_THAN_OR_EQUAL:
        return value >= filterValue;
      case FilterOperator.LESS_THAN:
        return value < filterValue;
      case FilterOperator.LESS_THAN_OR_EQUAL:
        return value <= filterValue;
      case FilterOperator.LIKE:
        return typeof value === 'string' && value.toLowerCase().includes(filterValue.toLowerCase());
      case FilterOperator.IN:
        return filterValues ? filterValues.includes(value) : false;
      case FilterOperator.NOT_IN:
        return filterValues ? !filterValues.includes(value) : true;
      case FilterOperator.IS_NULL:
        return value === null || value === undefined;
      case FilterOperator.IS_NOT_NULL:
        return value !== null && value !== undefined;
      case FilterOperator.BETWEEN:
        return filterValues && filterValues.length === 2 && 
               value >= filterValues[0] && value <= filterValues[1];
      default:
        return true;
    }
  }

  private applySorting(memories: Memory[], sortCriteria?: any[]): Memory[] {
    if (!sortCriteria || sortCriteria.length === 0) {
      // 默认按更新时间倒序排列
      return memories.sort((a, b) => b.updatedAt.getTime() - a.updatedAt.getTime());
    }

    return memories.sort((a, b) => {
      for (const sort of sortCriteria) {
        const aValue = a[sort.property as keyof Memory];
        const bValue = b[sort.property as keyof Memory];
        
        let comparison = 0;
        if (aValue < bValue) comparison = -1;
        else if (aValue > bValue) comparison = 1;
        
        if (comparison !== 0) {
          return sort.direction === 'DESC' ? -comparison : comparison;
        }
      }
      return 0;
    });
  }
}
```

## ✅ 验收标准

### 功能验收
- [ ] MemoryRepository类创建成功
- [ ] 实现了所有Repository接口方法
- [ ] 支持CRUD操作和批量操作
- [ ] 支持缓存功能
- [ ] 支持搜索和分页功能
- [ ] 数据验证机制完善

### 质量验收
- [ ] TypeScript编译无错误
- [ ] 代码符合项目编码规范
- [ ] 错误处理机制完善
- [ ] 日志记录完整

### 性能验收
- [ ] 缓存机制工作正常
- [ ] 批量操作性能良好
- [ ] 内存使用合理

## 🔄 幂等性保证

### 执行前检查
```bash
#!/bin/bash
echo "🔍 检查任务2.2执行状态..."

# 检查依赖任务
if [ ! -f "src/data/interfaces/IRepository.ts" ]; then
  echo "❌ 依赖任务Task-2.1未完成，请先执行Task-2.1"
  exit 1
fi

# 检查是否已存在MemoryRepository
if [ -f "src/data/repositories/MemoryRepository.ts" ]; then
  echo "⚠️  MemoryRepository已存在，任务可能已完成"
  echo "请确认是否需要重新执行此任务"
  read -p "是否继续？(y/N): " confirm
  if [ "$confirm" != "y" ]; then
    exit 1
  fi
fi

echo "✅ 前置检查完成，可以执行任务2.2"
```

### 执行后验证
```bash
#!/bin/bash
echo "🔍 验证任务2.2执行结果..."

# 检查文件是否创建成功
files=(
  "src/data/entities/Memory.ts"
  "src/data/repositories/MemoryRepository.ts"
)

all_created=true
for file in "${files[@]}"; do
  if [ -f "$file" ]; then
    echo "✅ $file 创建成功"
  else
    echo "❌ $file 创建失败"
    all_created=false
  fi
done

# TypeScript编译检查
if npm run type-check > /dev/null 2>&1; then
  echo "✅ TypeScript编译检查通过"
else
  echo "❌ TypeScript编译检查失败"
  all_created=false
fi

if [ "$all_created" = true ]; then
  echo "🎉 任务2.2执行成功！"
  exit 0
else
  echo "💥 任务2.2执行失败，请检查错误信息"
  exit 1
fi
```

## 📝 完成确认

任务完成后，请在检查清单中标记：
- [ ] 任务2.2已完成
- [ ] Memory实体定义完成
- [ ] MemoryRepository实现完成
- [ ] 缓存功能正常工作
- [ ] 准备执行任务2.3

## 🔗 相关链接

- [上一个任务：Task-2.1 创建Repository接口](./task-2.1-repository-interfaces.md)
- [下一个任务：Task-2.3 实现配置Repository](./task-2.3-config-repository.md)
- [重构检查清单](../00-refactoring-checklist.md)
