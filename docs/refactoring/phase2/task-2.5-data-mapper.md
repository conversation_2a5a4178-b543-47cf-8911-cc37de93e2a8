# 任务2.5：实现数据映射器

## 📋 任务概述

**任务ID**: Task-2.5  
**任务名称**: 实现数据映射器  
**所属阶段**: 阶段2 - 数据层重构  
**预计时间**: 2小时  
**依赖任务**: Task-2.4 (重构缓存管理器)  
**后续任务**: Task-2.6 (创建数据层测试页面)  

## 🎯 任务目标

实现数据映射器，提供实体对象与存储数据之间的转换功能，支持数据验证、格式转换和版本兼容。

## 🛠️ 实施步骤

### 步骤1：定义数据映射器接口
**文件**: `src/data/interfaces/IDataMapper.ts`

```typescript
/**
 * 数据映射器接口
 */
export interface IDataMapper<T> {
  // 数据转换
  toStorage(entity: T): any;
  fromStorage(data: any): T;
  
  // 批量转换
  toStorageBatch(entities: T[]): any[];
  fromStorageBatch(dataArray: any[]): T[];
  
  // 验证
  validate(entity: T): ValidationResult;
  validateStorage(data: any): ValidationResult;
  
  // 版本兼容
  migrate(data: any, fromVersion: string, toVersion: string): any;
  getCurrentVersion(): string;
}

export interface ValidationResult {
  valid: boolean;
  errors: ValidationError[];
}

export interface ValidationError {
  field: string;
  message: string;
  code: string;
}

export interface MigrationRule {
  fromVersion: string;
  toVersion: string;
  transform: (data: any) => any;
}
```

### 步骤2：实现内存数据映射器
**文件**: `src/data/mappers/MemoryDataMapper.ts`

```typescript
import { IDataMapper, ValidationResult, MigrationRule } from '../interfaces/IDataMapper';
import { Memory, MemoryType } from '../entities/Memory';
import { validator } from '../../infrastructure/utils/Validator';
import { logger } from '../../infrastructure/utils/Logger';

export class MemoryDataMapper implements IDataMapper<Memory> {
  private readonly CURRENT_VERSION = '1.0.0';
  private migrationRules: MigrationRule[] = [];

  constructor() {
    this.initializeMigrationRules();
  }

  toStorage(memory: Memory): any {
    return {
      id: memory.id,
      title: memory.title,
      content: memory.content,
      type: memory.type,
      tags: memory.tags,
      categoryId: memory.categoryId,
      attachments: memory.attachments.map(att => ({
        id: att.id,
        name: att.name,
        type: att.type,
        size: att.size,
        url: att.url,
        thumbnailUrl: att.thumbnailUrl,
        metadata: att.metadata
      })),
      metadata: {
        location: memory.metadata.location,
        weather: memory.metadata.weather,
        mood: memory.metadata.mood,
        people: memory.metadata.people,
        events: memory.metadata.events,
        customFields: memory.metadata.customFields
      },
      isPrivate: memory.isPrivate,
      isFavorite: memory.isFavorite,
      isArchived: memory.isArchived,
      historicalDate: memory.historicalDate?.toISOString(),
      createdAt: memory.createdAt.toISOString(),
      updatedAt: memory.updatedAt.toISOString(),
      version: memory.version,
      _dataVersion: this.CURRENT_VERSION
    };
  }

  fromStorage(data: any): Memory {
    // 数据迁移
    const migratedData = this.migrateIfNeeded(data);
    
    return {
      id: migratedData.id,
      title: migratedData.title || '',
      content: migratedData.content || '',
      type: migratedData.type || MemoryType.TEXT,
      tags: migratedData.tags || [],
      categoryId: migratedData.categoryId,
      attachments: (migratedData.attachments || []).map((att: any) => ({
        id: att.id,
        name: att.name,
        type: att.type,
        size: att.size || 0,
        url: att.url,
        thumbnailUrl: att.thumbnailUrl,
        metadata: att.metadata || {}
      })),
      metadata: {
        location: migratedData.metadata?.location,
        weather: migratedData.metadata?.weather,
        mood: migratedData.metadata?.mood,
        people: migratedData.metadata?.people || [],
        events: migratedData.metadata?.events || [],
        customFields: migratedData.metadata?.customFields || {}
      },
      isPrivate: migratedData.isPrivate || false,
      isFavorite: migratedData.isFavorite || false,
      isArchived: migratedData.isArchived || false,
      historicalDate: migratedData.historicalDate ? new Date(migratedData.historicalDate) : undefined,
      createdAt: new Date(migratedData.createdAt),
      updatedAt: new Date(migratedData.updatedAt),
      version: migratedData.version || 1
    };
  }

  toStorageBatch(memories: Memory[]): any[] {
    return memories.map(memory => this.toStorage(memory));
  }

  fromStorageBatch(dataArray: any[]): Memory[] {
    return dataArray.map(data => this.fromStorage(data));
  }

  validate(memory: Memory): ValidationResult {
    const errors: any[] = [];

    // 基础字段验证
    if (!memory.id) errors.push({ field: 'id', message: 'ID不能为空', code: 'REQUIRED' });
    if (!memory.title?.trim()) errors.push({ field: 'title', message: '标题不能为空', code: 'REQUIRED' });
    if (!memory.content?.trim()) errors.push({ field: 'content', message: '内容不能为空', code: 'REQUIRED' });
    
    // 类型验证
    if (!Object.values(MemoryType).includes(memory.type)) {
      errors.push({ field: 'type', message: '无效的内存类型', code: 'INVALID_TYPE' });
    }

    // 标签验证
    if (!Array.isArray(memory.tags)) {
      errors.push({ field: 'tags', message: '标签必须是数组', code: 'INVALID_FORMAT' });
    }

    // 附件验证
    if (!Array.isArray(memory.attachments)) {
      errors.push({ field: 'attachments', message: '附件必须是数组', code: 'INVALID_FORMAT' });
    } else {
      memory.attachments.forEach((att, index) => {
        if (!att.id) errors.push({ field: `attachments[${index}].id`, message: '附件ID不能为空', code: 'REQUIRED' });
        if (!att.name) errors.push({ field: `attachments[${index}].name`, message: '附件名称不能为空', code: 'REQUIRED' });
        if (!att.url) errors.push({ field: `attachments[${index}].url`, message: '附件URL不能为空', code: 'REQUIRED' });
      });
    }

    // 日期验证
    if (!(memory.createdAt instanceof Date) || isNaN(memory.createdAt.getTime())) {
      errors.push({ field: 'createdAt', message: '创建时间格式无效', code: 'INVALID_DATE' });
    }
    
    if (!(memory.updatedAt instanceof Date) || isNaN(memory.updatedAt.getTime())) {
      errors.push({ field: 'updatedAt', message: '更新时间格式无效', code: 'INVALID_DATE' });
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  validateStorage(data: any): ValidationResult {
    const errors: any[] = [];

    if (!data) {
      errors.push({ field: 'data', message: '数据不能为空', code: 'REQUIRED' });
      return { valid: false, errors };
    }

    // 基础字段验证
    if (!data.id) errors.push({ field: 'id', message: 'ID不能为空', code: 'REQUIRED' });
    if (!data.title) errors.push({ field: 'title', message: '标题不能为空', code: 'REQUIRED' });
    if (!data.content) errors.push({ field: 'content', message: '内容不能为空', code: 'REQUIRED' });
    if (!data.type) errors.push({ field: 'type', message: '类型不能为空', code: 'REQUIRED' });
    if (!data.createdAt) errors.push({ field: 'createdAt', message: '创建时间不能为空', code: 'REQUIRED' });
    if (!data.updatedAt) errors.push({ field: 'updatedAt', message: '更新时间不能为空', code: 'REQUIRED' });

    return {
      valid: errors.length === 0,
      errors
    };
  }

  migrate(data: any, fromVersion: string, toVersion: string): any {
    let migratedData = { ...data };

    // 查找适用的迁移规则
    const applicableRules = this.migrationRules.filter(rule => 
      rule.fromVersion === fromVersion && rule.toVersion === toVersion
    );

    for (const rule of applicableRules) {
      try {
        migratedData = rule.transform(migratedData);
        logger.info(`数据迁移成功: ${fromVersion} -> ${toVersion}`);
      } catch (error) {
        logger.error(`数据迁移失败: ${fromVersion} -> ${toVersion}`, error);
        throw error;
      }
    }

    return migratedData;
  }

  getCurrentVersion(): string {
    return this.CURRENT_VERSION;
  }

  private migrateIfNeeded(data: any): any {
    const dataVersion = data._dataVersion || '0.9.0';
    
    if (dataVersion === this.CURRENT_VERSION) {
      return data;
    }

    try {
      return this.migrate(data, dataVersion, this.CURRENT_VERSION);
    } catch (error) {
      logger.warn(`数据迁移失败，使用原始数据: ${dataVersion} -> ${this.CURRENT_VERSION}`, error);
      return data;
    }
  }

  private initializeMigrationRules(): void {
    // 从 0.9.0 到 1.0.0 的迁移规则
    this.migrationRules.push({
      fromVersion: '0.9.0',
      toVersion: '1.0.0',
      transform: (data: any) => ({
        ...data,
        // 添加新字段的默认值
        isArchived: data.isArchived || false,
        metadata: {
          ...data.metadata,
          customFields: data.metadata?.customFields || {}
        },
        _dataVersion: '1.0.0'
      })
    });
  }
}
```

### 步骤3：实现配置数据映射器
**文件**: `src/data/mappers/ConfigDataMapper.ts`

```typescript
import { IDataMapper, ValidationResult } from '../interfaces/IDataMapper';
import { Configuration, ConfigurationType, ConfigurationCategory } from '../entities/Configuration';

export class ConfigDataMapper implements IDataMapper<Configuration> {
  private readonly CURRENT_VERSION = '1.0.0';

  toStorage(config: Configuration): any {
    return {
      id: config.id,
      key: config.key,
      value: config.value,
      type: config.type,
      category: config.category,
      description: config.description,
      isEncrypted: config.isEncrypted,
      isReadonly: config.isReadonly,
      validationRules: config.validationRules,
      defaultValue: config.defaultValue,
      createdAt: config.createdAt.toISOString(),
      updatedAt: config.updatedAt.toISOString(),
      version: config.version,
      _dataVersion: this.CURRENT_VERSION
    };
  }

  fromStorage(data: any): Configuration {
    return {
      id: data.id,
      key: data.key,
      value: data.value,
      type: data.type || ConfigurationType.STRING,
      category: data.category || ConfigurationCategory.USER,
      description: data.description,
      isEncrypted: data.isEncrypted || false,
      isReadonly: data.isReadonly || false,
      validationRules: data.validationRules || [],
      defaultValue: data.defaultValue,
      createdAt: new Date(data.createdAt),
      updatedAt: new Date(data.updatedAt),
      version: data.version || 1
    };
  }

  toStorageBatch(configs: Configuration[]): any[] {
    return configs.map(config => this.toStorage(config));
  }

  fromStorageBatch(dataArray: any[]): Configuration[] {
    return dataArray.map(data => this.fromStorage(data));
  }

  validate(config: Configuration): ValidationResult {
    const errors: any[] = [];

    if (!config.id) errors.push({ field: 'id', message: 'ID不能为空', code: 'REQUIRED' });
    if (!config.key?.trim()) errors.push({ field: 'key', message: '配置键不能为空', code: 'REQUIRED' });
    
    if (!Object.values(ConfigurationType).includes(config.type)) {
      errors.push({ field: 'type', message: '无效的配置类型', code: 'INVALID_TYPE' });
    }
    
    if (!Object.values(ConfigurationCategory).includes(config.category)) {
      errors.push({ field: 'category', message: '无效的配置分类', code: 'INVALID_CATEGORY' });
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  validateStorage(data: any): ValidationResult {
    const errors: any[] = [];

    if (!data) {
      errors.push({ field: 'data', message: '数据不能为空', code: 'REQUIRED' });
      return { valid: false, errors };
    }

    if (!data.id) errors.push({ field: 'id', message: 'ID不能为空', code: 'REQUIRED' });
    if (!data.key) errors.push({ field: 'key', message: '配置键不能为空', code: 'REQUIRED' });
    if (!data.createdAt) errors.push({ field: 'createdAt', message: '创建时间不能为空', code: 'REQUIRED' });
    if (!data.updatedAt) errors.push({ field: 'updatedAt', message: '更新时间不能为空', code: 'REQUIRED' });

    return {
      valid: errors.length === 0,
      errors
    };
  }

  migrate(data: any, fromVersion: string, toVersion: string): any {
    // 配置数据迁移逻辑
    return data;
  }

  getCurrentVersion(): string {
    return this.CURRENT_VERSION;
  }
}
```

### 步骤4：创建数据映射器工厂
**文件**: `src/data/mappers/DataMapperFactory.ts`

```typescript
import { IDataMapper } from '../interfaces/IDataMapper';
import { Memory } from '../entities/Memory';
import { Configuration } from '../entities/Configuration';
import { MemoryDataMapper } from './MemoryDataMapper';
import { ConfigDataMapper } from './ConfigDataMapper';

export class DataMapperFactory {
  private static mappers = new Map<string, IDataMapper<any>>();

  static getMemoryMapper(): IDataMapper<Memory> {
    if (!this.mappers.has('memory')) {
      this.mappers.set('memory', new MemoryDataMapper());
    }
    return this.mappers.get('memory')!;
  }

  static getConfigMapper(): IDataMapper<Configuration> {
    if (!this.mappers.has('config')) {
      this.mappers.set('config', new ConfigDataMapper());
    }
    return this.mappers.get('config')!;
  }

  static clear(): void {
    this.mappers.clear();
  }
}
```

## ✅ 验收标准

### 功能验收
- [ ] IDataMapper接口定义完整
- [ ] MemoryDataMapper实现所有接口方法
- [ ] ConfigDataMapper实现所有接口方法
- [ ] 支持数据验证和迁移功能
- [ ] DataMapperFactory工厂模式实现

### 质量验收
- [ ] TypeScript编译无错误
- [ ] 数据转换准确无误
- [ ] 验证逻辑完善
- [ ] 错误处理机制完善

## 🔄 幂等性保证

### 执行前检查
```bash
#!/bin/bash
echo "🔍 检查任务2.5执行状态..."

if [ ! -f "src/data/cache/MemoryCacheManager.ts" ]; then
  echo "❌ 依赖任务Task-2.4未完成，请先执行Task-2.4"
  exit 1
fi

if [ -f "src/data/mappers/MemoryDataMapper.ts" ]; then
  echo "⚠️  数据映射器已存在，任务可能已完成"
  read -p "是否继续？(y/N): " confirm
  if [ "$confirm" != "y" ]; then
    exit 1
  fi
fi

echo "✅ 前置检查完成，可以执行任务2.5"
```

### 执行后验证
```bash
#!/bin/bash
echo "🔍 验证任务2.5执行结果..."

files=(
  "src/data/interfaces/IDataMapper.ts"
  "src/data/mappers/MemoryDataMapper.ts"
  "src/data/mappers/ConfigDataMapper.ts"
  "src/data/mappers/DataMapperFactory.ts"
)

all_created=true
for file in "${files[@]}"; do
  if [ -f "$file" ]; then
    echo "✅ $file 创建成功"
  else
    echo "❌ $file 创建失败"
    all_created=false
  fi
done

if npm run type-check > /dev/null 2>&1; then
  echo "✅ TypeScript编译检查通过"
else
  echo "❌ TypeScript编译检查失败"
  all_created=false
fi

if [ "$all_created" = true ]; then
  echo "🎉 任务2.5执行成功！"
  exit 0
else
  echo "💥 任务2.5执行失败，请检查错误信息"
  exit 1
fi
```

## 📝 完成确认

任务完成后，请在检查清单中标记：
- [ ] 任务2.5已完成
- [ ] 数据映射器接口定义完成
- [ ] 内存和配置数据映射器实现完成
- [ ] 数据映射器工厂创建完成
- [ ] 准备执行任务2.6

## 🔗 相关链接

- [上一个任务：Task-2.4 重构缓存管理器](./task-2.4-cache-manager.md)
- [下一个任务：Task-2.6 创建数据层测试页面](./task-2.6-data-layer-test-page.md)
- [重构检查清单](../00-refactoring-checklist.md)
