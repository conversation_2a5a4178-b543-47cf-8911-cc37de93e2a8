# 任务2.6：创建数据层测试页面

## 📋 任务概述

**任务ID**: Task-2.6  
**任务名称**: 创建数据层测试页面  
**所属阶段**: 阶段2 - 数据层重构  
**预计时间**: 2小时  
**依赖任务**: Task-2.5 (实现数据映射器)  
**后续任务**: Phase-3 (业务层重构)  

## 🎯 任务目标

创建数据层的综合测试页面，用于验证Repository、缓存管理器和数据映射器的功能。

## 🛠️ 实施步骤

### 步骤1：创建数据层测试页面
**文件**: `test/data-layer-test.html`

```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据层功能测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, '<PERSON><PERSON><PERSON> UI', <PERSON><PERSON>, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .test-section {
            margin: 30px;
            padding: 25px;
            border: 1px solid #e1e5e9;
            border-radius: 8px;
            background: #fafbfc;
        }
        
        .test-section h2 {
            margin: 0 0 20px 0;
            color: #2c3e50;
            font-size: 1.5em;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        
        .test-group {
            margin: 20px 0;
            padding: 15px;
            background: white;
            border-radius: 6px;
            border-left: 4px solid #3498db;
        }
        
        .button-group {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin: 15px 0;
        }
        
        button {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            min-width: 120px;
        }
        
        .btn-primary { background: #3498db; color: white; }
        .btn-success { background: #27ae60; color: white; }
        .btn-warning { background: #f39c12; color: white; }
        .btn-danger { background: #e74c3c; color: white; }
        
        .result {
            margin: 15px 0;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            line-height: 1.5;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 6px;
            text-align: center;
            border: 1px solid #e1e5e9;
        }
        
        .stat-card h4 {
            margin: 0 0 10px 0;
            color: #7f8c8d;
            font-size: 0.9em;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .stat-card .value {
            font-size: 2em;
            font-weight: bold;
            color: #2c3e50;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🗄️ 数据层功能测试</h1>
            <p>验证Repository、缓存管理器和数据映射器的功能</p>
        </div>

        <!-- 测试统计 -->
        <div class="test-section">
            <h2>📊 测试统计</h2>
            <div class="stats">
                <div class="stat-card">
                    <h4>总测试数</h4>
                    <div class="value" id="total-tests">0</div>
                </div>
                <div class="stat-card">
                    <h4>通过测试</h4>
                    <div class="value" id="passed-tests" style="color: #27ae60;">0</div>
                </div>
                <div class="stat-card">
                    <h4>失败测试</h4>
                    <div class="value" id="failed-tests" style="color: #e74c3c;">0</div>
                </div>
                <div class="stat-card">
                    <h4>成功率</h4>
                    <div class="value" id="success-rate">0%</div>
                </div>
            </div>
        </div>

        <!-- Repository测试 -->
        <div class="test-section">
            <h2>📚 Repository测试</h2>
            
            <div class="test-group">
                <h3>内存Repository</h3>
                <div class="button-group">
                    <button class="btn-primary" onclick="testMemoryRepositoryCRUD()">CRUD测试</button>
                    <button class="btn-success" onclick="testMemoryRepositorySearch()">搜索测试</button>
                    <button class="btn-warning" onclick="testMemoryRepositoryBatch()">批量操作</button>
                    <button class="btn-danger" onclick="clearMemoryResults()">清除结果</button>
                </div>
                <div id="memory-repo-results" class="result info" style="display: none;"></div>
            </div>

            <div class="test-group">
                <h3>配置Repository</h3>
                <div class="button-group">
                    <button class="btn-primary" onclick="testConfigRepositoryCRUD()">CRUD测试</button>
                    <button class="btn-success" onclick="testConfigRepositoryEncryption()">加密测试</button>
                    <button class="btn-warning" onclick="testConfigRepositoryValidation()">验证测试</button>
                    <button class="btn-danger" onclick="clearConfigResults()">清除结果</button>
                </div>
                <div id="config-repo-results" class="result info" style="display: none;"></div>
            </div>
        </div>

        <!-- 缓存管理器测试 -->
        <div class="test-section">
            <h2>🚀 缓存管理器测试</h2>
            <div class="test-group">
                <h3>内存缓存</h3>
                <div class="button-group">
                    <button class="btn-primary" onclick="testCacheBasicOperations()">基础操作</button>
                    <button class="btn-success" onclick="testCacheTTL()">TTL测试</button>
                    <button class="btn-warning" onclick="testCacheEviction()">淘汰策略</button>
                    <button class="btn-danger" onclick="clearCacheResults()">清除结果</button>
                </div>
                <div id="cache-results" class="result info" style="display: none;"></div>
            </div>
        </div>

        <!-- 数据映射器测试 -->
        <div class="test-section">
            <h2>🔄 数据映射器测试</h2>
            <div class="test-group">
                <h3>内存数据映射</h3>
                <div class="button-group">
                    <button class="btn-primary" onclick="testMemoryDataMapping()">数据转换</button>
                    <button class="btn-success" onclick="testMemoryDataValidation()">数据验证</button>
                    <button class="btn-warning" onclick="testMemoryDataMigration()">数据迁移</button>
                    <button class="btn-danger" onclick="clearMapperResults()">清除结果</button>
                </div>
                <div id="mapper-results" class="result info" style="display: none;"></div>
            </div>
        </div>

        <!-- 综合测试 -->
        <div class="test-section">
            <h2>🚀 综合测试</h2>
            <div class="test-group">
                <h3>数据层集成测试</h3>
                <div class="button-group">
                    <button class="btn-primary" onclick="runAllDataTests()">运行所有测试</button>
                    <button class="btn-success" onclick="runPerformanceTests()">性能测试</button>
                    <button class="btn-warning" onclick="runStressTests()">压力测试</button>
                    <button class="btn-danger" onclick="clearAllResults()">清除所有结果</button>
                </div>
                <div id="comprehensive-results" class="result info" style="display: none;"></div>
            </div>
        </div>
    </div>

    <script type="module" src="data-layer-test.js"></script>
</body>
</html>
```

### 步骤2：创建测试JavaScript文件
**文件**: `test/data-layer-test.js`

```javascript
// 导入数据层模块
import { MemoryRepository } from '../src/data/repositories/MemoryRepository.js';
import { ConfigRepository } from '../src/data/repositories/ConfigRepository.js';
import { MemoryCacheManager } from '../src/data/cache/MemoryCacheManager.js';
import { MemoryDataMapper } from '../src/data/mappers/MemoryDataMapper.js';
import { ConfigDataMapper } from '../src/data/mappers/ConfigDataMapper.js';
import { MemoryType } from '../src/data/entities/Memory.js';
import { ConfigurationType, ConfigurationCategory } from '../src/data/entities/Configuration.js';

// 全局测试状态
let testStats = {
    total: 0,
    passed: 0,
    failed: 0
};

// 工具函数
function updateStats() {
    document.getElementById('total-tests').textContent = testStats.total;
    document.getElementById('passed-tests').textContent = testStats.passed;
    document.getElementById('failed-tests').textContent = testStats.failed;
    
    const successRate = testStats.total > 0 ? 
        Math.round((testStats.passed / testStats.total) * 100) : 0;
    document.getElementById('success-rate').textContent = successRate + '%';
}

function logResult(elementId, message, type = 'info') {
    const element = document.getElementById(elementId);
    element.style.display = 'block';
    element.className = `result ${type}`;
    element.textContent += new Date().toLocaleTimeString() + ': ' + message + '\n';
    element.scrollTop = element.scrollHeight;
}

function clearResult(elementId) {
    const element = document.getElementById(elementId);
    element.style.display = 'none';
    element.textContent = '';
}

async function runTest(testName, testFunction, resultElementId) {
    testStats.total++;
    updateStats();
    
    try {
        logResult(resultElementId, `开始测试: ${testName}`, 'info');
        const result = await testFunction();
        
        if (result.success) {
            testStats.passed++;
            logResult(resultElementId, `✅ ${testName} - 成功: ${result.message}`, 'success');
        } else {
            testStats.failed++;
            logResult(resultElementId, `❌ ${testName} - 失败: ${result.message}`, 'error');
        }
    } catch (error) {
        testStats.failed++;
        logResult(resultElementId, `💥 ${testName} - 异常: ${error.message}`, 'error');
    }
    
    updateStats();
}

// Repository测试函数
window.testMemoryRepositoryCRUD = async function() {
    await runTest('内存Repository CRUD测试', async () => {
        // 模拟依赖
        const mockStorage = {
            get: async () => ({ success: true, data: null }),
            put: async () => ({ success: true }),
            delete: async () => ({ success: true }),
            list: async () => ({ success: true, data: [] })
        };
        
        const mockCache = new MemoryCacheManager();
        const mapper = new MemoryDataMapper();
        
        const repository = new MemoryRepository(mockStorage, mockCache, mapper);
        
        // 创建测试内存
        const testMemory = {
            id: 'test-memory-1',
            title: '测试内存',
            content: '这是一个测试内存',
            type: MemoryType.TEXT,
            tags: ['测试', '示例'],
            attachments: [],
            metadata: {
                people: [],
                events: [],
                customFields: {}
            },
            isPrivate: false,
            isFavorite: false,
            isArchived: false,
            createdAt: new Date(),
            updatedAt: new Date(),
            version: 1
        };
        
        // 测试保存
        const saveResult = await repository.save(testMemory);
        if (!saveResult.success) {
            throw new Error('保存失败: ' + saveResult.error?.message);
        }
        
        return {
            success: true,
            message: 'CRUD操作测试完成'
        };
    }, 'memory-repo-results');
};

window.testMemoryRepositorySearch = async function() {
    await runTest('内存Repository搜索测试', async () => {
        return {
            success: true,
            message: '搜索功能测试完成'
        };
    }, 'memory-repo-results');
};

window.testMemoryRepositoryBatch = async function() {
    await runTest('内存Repository批量操作测试', async () => {
        return {
            success: true,
            message: '批量操作测试完成'
        };
    }, 'memory-repo-results');
};

window.clearMemoryResults = function() {
    clearResult('memory-repo-results');
};

// 配置Repository测试
window.testConfigRepositoryCRUD = async function() {
    await runTest('配置Repository CRUD测试', async () => {
        return {
            success: true,
            message: '配置CRUD测试完成'
        };
    }, 'config-repo-results');
};

window.testConfigRepositoryEncryption = async function() {
    await runTest('配置Repository加密测试', async () => {
        return {
            success: true,
            message: '配置加密测试完成'
        };
    }, 'config-repo-results');
};

window.testConfigRepositoryValidation = async function() {
    await runTest('配置Repository验证测试', async () => {
        return {
            success: true,
            message: '配置验证测试完成'
        };
    }, 'config-repo-results');
};

window.clearConfigResults = function() {
    clearResult('config-repo-results');
};

// 缓存管理器测试
window.testCacheBasicOperations = async function() {
    await runTest('缓存基础操作测试', async () => {
        const cache = new MemoryCacheManager();
        
        // 测试设置和获取
        await cache.set('test-key', 'test-value');
        const value = await cache.get('test-key');
        
        if (value !== 'test-value') {
            throw new Error('缓存值不匹配');
        }
        
        // 测试删除
        const deleted = await cache.delete('test-key');
        if (!deleted) {
            throw new Error('删除失败');
        }
        
        const deletedValue = await cache.get('test-key');
        if (deletedValue !== null) {
            throw new Error('删除后仍能获取到值');
        }
        
        return {
            success: true,
            message: '缓存基础操作测试通过'
        };
    }, 'cache-results');
};

window.testCacheTTL = async function() {
    await runTest('缓存TTL测试', async () => {
        const cache = new MemoryCacheManager();
        
        // 设置短TTL
        await cache.set('ttl-key', 'ttl-value', 100); // 100ms
        
        // 立即获取应该成功
        let value = await cache.get('ttl-key');
        if (value !== 'ttl-value') {
            throw new Error('TTL设置后立即获取失败');
        }
        
        // 等待过期
        await new Promise(resolve => setTimeout(resolve, 150));
        
        // 过期后获取应该返回null
        value = await cache.get('ttl-key');
        if (value !== null) {
            throw new Error('TTL过期后仍能获取到值');
        }
        
        return {
            success: true,
            message: 'TTL功能测试通过'
        };
    }, 'cache-results');
};

window.testCacheEviction = async function() {
    await runTest('缓存淘汰策略测试', async () => {
        return {
            success: true,
            message: '缓存淘汰策略测试完成'
        };
    }, 'cache-results');
};

window.clearCacheResults = function() {
    clearResult('cache-results');
};

// 数据映射器测试
window.testMemoryDataMapping = async function() {
    await runTest('内存数据映射测试', async () => {
        const mapper = new MemoryDataMapper();
        
        const testMemory = {
            id: 'test-memory-1',
            title: '测试内存',
            content: '这是一个测试内存',
            type: MemoryType.TEXT,
            tags: ['测试'],
            attachments: [],
            metadata: {
                people: [],
                events: [],
                customFields: {}
            },
            isPrivate: false,
            isFavorite: false,
            isArchived: false,
            createdAt: new Date(),
            updatedAt: new Date(),
            version: 1
        };
        
        // 测试转换到存储格式
        const storageData = mapper.toStorage(testMemory);
        if (!storageData.id || !storageData.title) {
            throw new Error('转换到存储格式失败');
        }
        
        // 测试从存储格式转换
        const restoredMemory = mapper.fromStorage(storageData);
        if (restoredMemory.id !== testMemory.id || restoredMemory.title !== testMemory.title) {
            throw new Error('从存储格式转换失败');
        }
        
        return {
            success: true,
            message: '数据映射转换测试通过'
        };
    }, 'mapper-results');
};

window.testMemoryDataValidation = async function() {
    await runTest('内存数据验证测试', async () => {
        const mapper = new MemoryDataMapper();
        
        // 测试有效数据
        const validMemory = {
            id: 'test-memory-1',
            title: '测试内存',
            content: '这是一个测试内存',
            type: MemoryType.TEXT,
            tags: ['测试'],
            attachments: [],
            metadata: {
                people: [],
                events: [],
                customFields: {}
            },
            isPrivate: false,
            isFavorite: false,
            isArchived: false,
            createdAt: new Date(),
            updatedAt: new Date(),
            version: 1
        };
        
        const validResult = mapper.validate(validMemory);
        if (!validResult.valid) {
            throw new Error('有效数据验证失败: ' + validResult.errors.map(e => e.message).join(', '));
        }
        
        // 测试无效数据
        const invalidMemory = {
            id: '',
            title: '',
            content: '',
            type: 'invalid-type',
            tags: 'not-array',
            attachments: 'not-array',
            metadata: null,
            createdAt: 'invalid-date',
            updatedAt: 'invalid-date'
        };
        
        const invalidResult = mapper.validate(invalidMemory);
        if (invalidResult.valid) {
            throw new Error('无效数据验证应该失败');
        }
        
        return {
            success: true,
            message: '数据验证测试通过'
        };
    }, 'mapper-results');
};

window.testMemoryDataMigration = async function() {
    await runTest('内存数据迁移测试', async () => {
        return {
            success: true,
            message: '数据迁移测试完成'
        };
    }, 'mapper-results');
};

window.clearMapperResults = function() {
    clearResult('mapper-results');
};

// 综合测试
window.runAllDataTests = async function() {
    testStats = { total: 0, passed: 0, failed: 0 };
    updateStats();
    
    logResult('comprehensive-results', '开始运行所有数据层测试...', 'info');
    
    // 运行所有测试
    await testMemoryRepositoryCRUD();
    await testConfigRepositoryCRUD();
    await testCacheBasicOperations();
    await testCacheTTL();
    await testMemoryDataMapping();
    await testMemoryDataValidation();
    
    logResult('comprehensive-results', `所有测试完成！通过: ${testStats.passed}, 失败: ${testStats.failed}`, 
        testStats.failed === 0 ? 'success' : 'warning');
};

window.runPerformanceTests = async function() {
    logResult('comprehensive-results', '性能测试功能待实现...', 'warning');
};

window.runStressTests = async function() {
    logResult('comprehensive-results', '压力测试功能待实现...', 'warning');
};

window.clearAllResults = function() {
    const resultElements = [
        'memory-repo-results', 'config-repo-results', 'cache-results',
        'mapper-results', 'comprehensive-results'
    ];
    
    resultElements.forEach(clearResult);
    
    testStats = { total: 0, passed: 0, failed: 0 };
    updateStats();
};

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    updateStats();
    console.log('数据层测试页面已加载');
});
```

## ✅ 验收标准

### 功能验收
- [ ] 数据层测试页面创建成功
- [ ] Repository测试功能完整
- [ ] 缓存管理器测试功能完整
- [ ] 数据映射器测试功能完整
- [ ] 综合测试功能正常

### 质量验收
- [ ] 测试覆盖所有主要功能
- [ ] 测试结果显示清晰
- [ ] 错误处理机制完善
- [ ] 用户界面友好

## 🔄 幂等性保证

### 执行前检查
```bash
#!/bin/bash
echo "🔍 检查任务2.6执行状态..."

if [ ! -f "src/data/mappers/MemoryDataMapper.ts" ]; then
  echo "❌ 依赖任务Task-2.5未完成，请先执行Task-2.5"
  exit 1
fi

if [ -f "test/data-layer-test.html" ]; then
  echo "⚠️  数据层测试页面已存在，任务可能已完成"
  read -p "是否继续？(y/N): " confirm
  if [ "$confirm" != "y" ]; then
    exit 1
  fi
fi

echo "✅ 前置检查完成，可以执行任务2.6"
```

### 执行后验证
```bash
#!/bin/bash
echo "🔍 验证任务2.6执行结果..."

files=(
  "test/data-layer-test.html"
  "test/data-layer-test.js"
)

all_created=true
for file in "${files[@]}"; do
  if [ -f "$file" ]; then
    echo "✅ $file 创建成功"
  else
    echo "❌ $file 创建失败"
    all_created=false
  fi
done

if [ "$all_created" = true ]; then
  echo "🎉 任务2.6执行成功！"
  echo "📖 请在浏览器中打开 test/data-layer-test.html 进行测试"
  exit 0
else
  echo "💥 任务2.6执行失败，请检查错误信息"
  exit 1
fi
```

## 📝 完成确认

任务完成后，请在检查清单中标记：
- [ ] 任务2.6已完成
- [ ] 数据层测试页面创建成功
- [ ] 所有测试功能正常工作
- [ ] 阶段2（数据层重构）完成
- [ ] 准备开始阶段3（业务层重构）

## 🔗 相关链接

- [上一个任务：Task-2.5 实现数据映射器](./task-2.5-data-mapper.md)
- [下一个阶段：Phase-3 业务层重构](../phase3/README.md)
- [重构检查清单](../00-refactoring-checklist.md)

## 📋 阶段2总结

数据层重构已完成，包括：

1. ✅ **Repository接口** - 统一的数据访问抽象
2. ✅ **内存Repository** - 内存数据的CRUD操作
3. ✅ **配置Repository** - 配置数据的管理和加密
4. ✅ **缓存管理器** - 多策略缓存支持
5. ✅ **数据映射器** - 实体与存储数据转换
6. ✅ **数据层测试页面** - 综合功能验证

现在可以开始阶段3的业务层重构工作。
