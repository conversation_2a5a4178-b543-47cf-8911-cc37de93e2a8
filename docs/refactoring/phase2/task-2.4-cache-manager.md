# 任务2.4：重构缓存管理器

## 📋 任务概述

**任务ID**: Task-2.4  
**任务名称**: 重构缓存管理器  
**所属阶段**: 阶段2 - 数据层重构  
**预计时间**: 2小时  
**依赖任务**: Task-2.3 (实现配置Repository)  
**后续任务**: Task-2.5 (实现数据映射器)  

## 🎯 任务目标

重构缓存管理器，提供统一的缓存接口，支持多种缓存策略和过期机制，提升数据访问性能。

## 📋 前置条件检查

### 依赖检查
- [ ] Task-2.3 已完成
- [ ] ConfigRepository已实现

### 状态检查
```bash
if [ -f "src/data/cache/CacheManager.ts" ]; then
  echo "⚠️  缓存管理器已存在，请确认是否需要重新创建"
  exit 1
else
  echo "✅ 可以开始创建缓存管理器"
fi
```

## 🛠️ 实施步骤

### 步骤1：定义缓存接口
**文件**: `src/data/interfaces/ICacheManager.ts`

```typescript
/**
 * 缓存管理器接口
 */
export interface ICacheManager {
  // 基础操作
  get<T>(key: string): Promise<T | null>;
  set<T>(key: string, value: T, ttl?: number): Promise<void>;
  delete(key: string): Promise<boolean>;
  clear(pattern?: string): Promise<void>;
  
  // 批量操作
  getBatch<T>(keys: string[]): Promise<Record<string, T>>;
  setBatch<T>(items: Record<string, T>, ttl?: number): Promise<void>;
  deleteBatch(keys: string[]): Promise<number>;
  
  // 缓存信息
  exists(key: string): Promise<boolean>;
  getTTL(key: string): Promise<number>;
  getSize(): Promise<number>;
  getKeys(pattern?: string): Promise<string[]>;
  
  // 统计信息
  getStats(): Promise<CacheStats>;
  resetStats(): Promise<void>;
}

export interface CacheStats {
  hitCount: number;
  missCount: number;
  hitRate: number;
  totalRequests: number;
  size: number;
  memoryUsage: number;
}

export interface CacheEntry<T> {
  value: T;
  createdAt: number;
  expiresAt?: number;
  accessCount: number;
  lastAccessed: number;
}

export enum CacheStrategy {
  LRU = 'lru',
  LFU = 'lfu',
  FIFO = 'fifo',
  TTL = 'ttl'
}
```

### 步骤2：实现内存缓存管理器
**文件**: `src/data/cache/MemoryCacheManager.ts`

```typescript
import { ICacheManager, CacheStats, CacheEntry, CacheStrategy } from '../interfaces/ICacheManager';
import { logger } from '../../infrastructure/utils/Logger';

export class MemoryCacheManager implements ICacheManager {
  private cache = new Map<string, CacheEntry<any>>();
  private stats: CacheStats = {
    hitCount: 0,
    missCount: 0,
    hitRate: 0,
    totalRequests: 0,
    size: 0,
    memoryUsage: 0
  };
  
  constructor(
    private maxSize: number = 1000,
    private defaultTTL: number = 3600000, // 1小时
    private strategy: CacheStrategy = CacheStrategy.LRU
  ) {}

  async get<T>(key: string): Promise<T | null> {
    this.stats.totalRequests++;
    
    const entry = this.cache.get(key);
    if (!entry) {
      this.stats.missCount++;
      this.updateHitRate();
      return null;
    }

    // 检查是否过期
    if (entry.expiresAt && Date.now() > entry.expiresAt) {
      this.cache.delete(key);
      this.stats.missCount++;
      this.updateHitRate();
      return null;
    }

    // 更新访问信息
    entry.accessCount++;
    entry.lastAccessed = Date.now();
    
    this.stats.hitCount++;
    this.updateHitRate();
    
    return entry.value as T;
  }

  async set<T>(key: string, value: T, ttl?: number): Promise<void> {
    const now = Date.now();
    const expiresAt = ttl ? now + ttl : (this.defaultTTL ? now + this.defaultTTL : undefined);
    
    const entry: CacheEntry<T> = {
      value,
      createdAt: now,
      expiresAt,
      accessCount: 0,
      lastAccessed: now
    };

    // 检查缓存大小限制
    if (this.cache.size >= this.maxSize && !this.cache.has(key)) {
      this.evictEntry();
    }

    this.cache.set(key, entry);
    this.updateStats();
  }

  async delete(key: string): Promise<boolean> {
    const deleted = this.cache.delete(key);
    if (deleted) {
      this.updateStats();
    }
    return deleted;
  }

  async clear(pattern?: string): Promise<void> {
    if (!pattern) {
      this.cache.clear();
    } else {
      const regex = new RegExp(pattern.replace(/\*/g, '.*'));
      for (const key of this.cache.keys()) {
        if (regex.test(key)) {
          this.cache.delete(key);
        }
      }
    }
    this.updateStats();
  }

  async getBatch<T>(keys: string[]): Promise<Record<string, T>> {
    const result: Record<string, T> = {};
    
    for (const key of keys) {
      const value = await this.get<T>(key);
      if (value !== null) {
        result[key] = value;
      }
    }
    
    return result;
  }

  async setBatch<T>(items: Record<string, T>, ttl?: number): Promise<void> {
    for (const [key, value] of Object.entries(items)) {
      await this.set(key, value, ttl);
    }
  }

  async deleteBatch(keys: string[]): Promise<number> {
    let deletedCount = 0;
    
    for (const key of keys) {
      if (await this.delete(key)) {
        deletedCount++;
      }
    }
    
    return deletedCount;
  }

  async exists(key: string): Promise<boolean> {
    return this.cache.has(key);
  }

  async getTTL(key: string): Promise<number> {
    const entry = this.cache.get(key);
    if (!entry || !entry.expiresAt) {
      return -1;
    }
    
    const remaining = entry.expiresAt - Date.now();
    return remaining > 0 ? remaining : 0;
  }

  async getSize(): Promise<number> {
    return this.cache.size;
  }

  async getKeys(pattern?: string): Promise<string[]> {
    const keys = Array.from(this.cache.keys());
    
    if (!pattern) {
      return keys;
    }
    
    const regex = new RegExp(pattern.replace(/\*/g, '.*'));
    return keys.filter(key => regex.test(key));
  }

  async getStats(): Promise<CacheStats> {
    return { ...this.stats };
  }

  async resetStats(): Promise<void> {
    this.stats = {
      hitCount: 0,
      missCount: 0,
      hitRate: 0,
      totalRequests: 0,
      size: this.cache.size,
      memoryUsage: this.estimateMemoryUsage()
    };
  }

  private evictEntry(): void {
    switch (this.strategy) {
      case CacheStrategy.LRU:
        this.evictLRU();
        break;
      case CacheStrategy.LFU:
        this.evictLFU();
        break;
      case CacheStrategy.FIFO:
        this.evictFIFO();
        break;
      case CacheStrategy.TTL:
        this.evictExpired();
        break;
    }
  }

  private evictLRU(): void {
    let oldestKey = '';
    let oldestTime = Date.now();
    
    for (const [key, entry] of this.cache.entries()) {
      if (entry.lastAccessed < oldestTime) {
        oldestTime = entry.lastAccessed;
        oldestKey = key;
      }
    }
    
    if (oldestKey) {
      this.cache.delete(oldestKey);
    }
  }

  private evictLFU(): void {
    let leastUsedKey = '';
    let leastCount = Infinity;
    
    for (const [key, entry] of this.cache.entries()) {
      if (entry.accessCount < leastCount) {
        leastCount = entry.accessCount;
        leastUsedKey = key;
      }
    }
    
    if (leastUsedKey) {
      this.cache.delete(leastUsedKey);
    }
  }

  private evictFIFO(): void {
    let oldestKey = '';
    let oldestTime = Date.now();
    
    for (const [key, entry] of this.cache.entries()) {
      if (entry.createdAt < oldestTime) {
        oldestTime = entry.createdAt;
        oldestKey = key;
      }
    }
    
    if (oldestKey) {
      this.cache.delete(oldestKey);
    }
  }

  private evictExpired(): void {
    const now = Date.now();
    const expiredKeys: string[] = [];
    
    for (const [key, entry] of this.cache.entries()) {
      if (entry.expiresAt && now > entry.expiresAt) {
        expiredKeys.push(key);
      }
    }
    
    for (const key of expiredKeys) {
      this.cache.delete(key);
    }
  }

  private updateHitRate(): void {
    this.stats.hitRate = this.stats.totalRequests > 0 
      ? this.stats.hitCount / this.stats.totalRequests 
      : 0;
  }

  private updateStats(): void {
    this.stats.size = this.cache.size;
    this.stats.memoryUsage = this.estimateMemoryUsage();
  }

  private estimateMemoryUsage(): number {
    // 简单的内存使用估算
    let usage = 0;
    for (const [key, entry] of this.cache.entries()) {
      usage += key.length * 2; // 字符串大小估算
      usage += JSON.stringify(entry.value).length * 2; // 值大小估算
      usage += 64; // 元数据大小估算
    }
    return usage;
  }
}
```

### 步骤3：创建缓存工厂
**文件**: `src/data/cache/CacheManagerFactory.ts`

```typescript
import { ICacheManager, CacheStrategy } from '../interfaces/ICacheManager';
import { MemoryCacheManager } from './MemoryCacheManager';

export interface CacheConfig {
  type: 'memory' | 'redis' | 'localStorage';
  maxSize?: number;
  defaultTTL?: number;
  strategy?: CacheStrategy;
}

export class CacheManagerFactory {
  private static instances = new Map<string, ICacheManager>();

  static create(name: string, config: CacheConfig): ICacheManager {
    if (this.instances.has(name)) {
      return this.instances.get(name)!;
    }

    let cacheManager: ICacheManager;

    switch (config.type) {
      case 'memory':
        cacheManager = new MemoryCacheManager(
          config.maxSize,
          config.defaultTTL,
          config.strategy
        );
        break;
      case 'redis':
        // TODO: 实现Redis缓存管理器
        throw new Error('Redis缓存管理器尚未实现');
      case 'localStorage':
        // TODO: 实现LocalStorage缓存管理器
        throw new Error('LocalStorage缓存管理器尚未实现');
      default:
        throw new Error(`不支持的缓存类型: ${config.type}`);
    }

    this.instances.set(name, cacheManager);
    return cacheManager;
  }

  static get(name: string): ICacheManager | undefined {
    return this.instances.get(name);
  }

  static getDefault(): ICacheManager {
    return this.create('default', { type: 'memory' });
  }

  static clear(): void {
    this.instances.clear();
  }
}
```

## ✅ 验收标准

### 功能验收
- [ ] ICacheManager接口定义完整
- [ ] MemoryCacheManager实现所有接口方法
- [ ] 支持多种缓存策略（LRU、LFU、FIFO、TTL）
- [ ] 支持批量操作和统计功能
- [ ] CacheManagerFactory工厂模式实现

### 质量验收
- [ ] TypeScript编译无错误
- [ ] 缓存性能良好
- [ ] 内存使用合理
- [ ] 错误处理完善

## 🔄 幂等性保证

### 执行前检查
```bash
#!/bin/bash
echo "🔍 检查任务2.4执行状态..."

if [ ! -f "src/data/repositories/ConfigRepository.ts" ]; then
  echo "❌ 依赖任务Task-2.3未完成，请先执行Task-2.3"
  exit 1
fi

if [ -f "src/data/cache/MemoryCacheManager.ts" ]; then
  echo "⚠️  缓存管理器已存在，任务可能已完成"
  read -p "是否继续？(y/N): " confirm
  if [ "$confirm" != "y" ]; then
    exit 1
  fi
fi

echo "✅ 前置检查完成，可以执行任务2.4"
```

### 执行后验证
```bash
#!/bin/bash
echo "🔍 验证任务2.4执行结果..."

files=(
  "src/data/interfaces/ICacheManager.ts"
  "src/data/cache/MemoryCacheManager.ts"
  "src/data/cache/CacheManagerFactory.ts"
)

all_created=true
for file in "${files[@]}"; do
  if [ -f "$file" ]; then
    echo "✅ $file 创建成功"
  else
    echo "❌ $file 创建失败"
    all_created=false
  fi
done

if npm run type-check > /dev/null 2>&1; then
  echo "✅ TypeScript编译检查通过"
else
  echo "❌ TypeScript编译检查失败"
  all_created=false
fi

if [ "$all_created" = true ]; then
  echo "🎉 任务2.4执行成功！"
  exit 0
else
  echo "💥 任务2.4执行失败，请检查错误信息"
  exit 1
fi
```

## 📝 完成确认

任务完成后，请在检查清单中标记：
- [ ] 任务2.4已完成
- [ ] 缓存管理器接口定义完成
- [ ] 内存缓存管理器实现完成
- [ ] 缓存工厂创建完成
- [ ] 准备执行任务2.5

## 🔗 相关链接

- [上一个任务：Task-2.3 实现配置Repository](./task-2.3-config-repository.md)
- [下一个任务：Task-2.5 实现数据映射器](./task-2.5-data-mapper.md)
- [重构检查清单](../00-refactoring-checklist.md)
