# 任务2.3：实现配置Repository

## 📋 任务概述

**任务ID**: Task-2.3
**任务名称**: 实现配置Repository
**所属阶段**: 阶段2 - 数据层重构
**预计时间**: 2小时
**依赖任务**: Task-2.2 (实现内存Repository)
**后续任务**: Task-2.4 (重构缓存管理器)

## 🎯 任务目标

实现Configuration实体的Repository，提供应用配置的存储和管理功能，支持配置的版本控制和变更通知。

## 📋 前置条件检查

### 依赖检查
- [ ] Task-2.2 已完成
- [ ] MemoryRepository已实现
- [ ] Repository基础设施可用

### 文件检查
- [ ] `src/data/repositories/MemoryRepository.ts` 存在
- [ ] `src/data/repositories/BaseRepository.ts` 存在

### 状态检查
```bash
# 检查配置Repository是否已存在
if [ -f "src/data/repositories/ConfigRepository.ts" ]; then
  echo "⚠️  配置Repository已存在，请确认是否需要重新创建"
  exit 1
else
  echo "✅ 可以开始创建配置Repository"
fi
```

## 🛠️ 实施步骤

### 步骤1：定义Configuration实体
**文件**: `src/data/entities/Configuration.ts`

```typescript
import { BaseEntity } from '../types/DataTypes';

/**
 * 配置实体
 */
export interface Configuration extends BaseEntity {
  id: string;
  key: string;
  value: any;
  type: ConfigurationType;
  category: ConfigurationCategory;
  description?: string;
  isEncrypted: boolean;
  isReadonly: boolean;
  validationRules?: ValidationRule[];
  defaultValue?: any;
  createdAt: Date;
  updatedAt: Date;
  version: number;
}

/**
 * 配置类型
 */
export enum ConfigurationType {
  STRING = 'string',
  NUMBER = 'number',
  BOOLEAN = 'boolean',
  OBJECT = 'object',
  ARRAY = 'array',
  JSON = 'json'
}

/**
 * 配置分类
 */
export enum ConfigurationCategory {
  SYSTEM = 'system',
  USER = 'user',
  STORAGE = 'storage',
  SECURITY = 'security',
  UI = 'ui',
  SYNC = 'sync',
  MIGRATION = 'migration',
  PERFORMANCE = 'performance'
}

/**
 * 验证规则
 */
export interface ValidationRule {
  type: 'required' | 'min' | 'max' | 'pattern' | 'enum' | 'custom';
  value?: any;
  message?: string;
  validator?: (value: any) => boolean;
}

/**
 * 配置变更历史
 */
export interface ConfigurationHistory {
  id: string;
  configId: string;
  oldValue: any;
  newValue: any;
  changedBy: string;
  changedAt: Date;
  reason?: string;
}

/**
 * 配置导出格式
 */
export interface ConfigurationExport {
  version: string;
  exportedAt: Date;
  configurations: Configuration[];
  metadata: {
    totalCount: number;
    categories: string[];
    encrypted: boolean;
  };
}
```

### 步骤2：实现配置Repository
**文件**: `src/data/repositories/ConfigRepository.ts`

```typescript
import { BaseRepository } from './BaseRepository';
import { Configuration, ConfigurationType, ConfigurationCategory } from '../entities/Configuration';
import {
  SearchCriteria,
  PageRequest,
  DataResult,
  FilterOperator
} from '../types/DataTypes';
import { IStorageProvider } from '../../infrastructure/interfaces/IStorageProvider';
import { ICryptoService } from '../../infrastructure/interfaces/ICryptoService';
import { IDataMapper } from '../interfaces/IDataMapper';
import { logger } from '../../infrastructure/utils/Logger';
import { validator } from '../../infrastructure/utils/Validator';

/**
 * 配置Repository实现
 */
export class ConfigRepository extends BaseRepository<Configuration> {
  private readonly STORAGE_KEY = 'configurations.json';
  private readonly ENCRYPTED_SUFFIX = '_encrypted';
  private configurations: Map<string, Configuration> = new Map();
  private loaded = false;

  constructor(
    private storageProvider: IStorageProvider,
    private cryptoService: ICryptoService,
    private dataMapper: IDataMapper<Configuration>
  ) {
    super('Configuration');
  }

  /**
   * 根据ID查找配置
   */
  async findById(id: string): Promise<DataResult<Configuration | null>> {
    try {
      await this.ensureLoaded();

      const config = this.configurations.get(id);
      if (!config) {
        return { success: true, data: null };
      }

      // 解密敏感配置
      const decryptedConfig = await this.decryptConfigIfNeeded(config);

      return { success: true, data: decryptedConfig };
    } catch (error) {
      logger.error(`查找配置失败 [${id}]:`, error);
      return { success: false, error: error as Error };
    }
  }

  /**
   * 根据键查找配置
   */
  async findByKey(key: string): Promise<DataResult<Configuration | null>> {
    try {
      await this.ensureLoaded();

      for (const config of this.configurations.values()) {
        if (config.key === key) {
          const decryptedConfig = await this.decryptConfigIfNeeded(config);
          return { success: true, data: decryptedConfig };
        }
      }

      return { success: true, data: null };
    } catch (error) {
      logger.error(`根据键查找配置失败 [${key}]:`, error);
      return { success: false, error: error as Error };
    }
  }

  /**
   * 根据分类查找配置
   */
  async findByCategory(category: ConfigurationCategory): Promise<DataResult<Configuration[]>> {
    const criteria: SearchCriteria<Configuration> = {
      filters: [{
        property: 'category',
        operator: FilterOperator.EQUALS,
        value: category
      }]
    };

    return this.findAll(criteria);
  }

  /**
   * 查找所有配置
   */
  async findAll(criteria?: SearchCriteria<Configuration>): Promise<DataResult<Configuration[]>> {
    try {
      await this.ensureLoaded();

      let configs = Array.from(this.configurations.values());

      // 应用搜索条件
      if (criteria) {
        configs = this.applySearchCriteria(configs, criteria);
      }

      // 解密敏感配置
      const decryptedConfigs = await Promise.all(
        configs.map(config => this.decryptConfigIfNeeded(config))
      );

      return { success: true, data: decryptedConfigs };
    } catch (error) {
      logger.error('查找所有配置失败:', error);
      return { success: false, error: error as Error };
    }
  }

  /**
   * 保存配置
   */
  async save(config: Configuration): Promise<DataResult<Configuration>> {
    try {
      // 验证配置
      const validationResult = this.validateConfiguration(config);
      if (!validationResult.valid) {
        return {
          success: false,
          error: new Error(`配置验证失败: ${validationResult.errors.join(', ')}`)
        };
      }

      await this.ensureLoaded();

      const now = new Date();
      const isNew = !config.id || !this.configurations.has(config.id);

      if (isNew) {
        config.id = this.generateId();
        config.createdAt = now;
        config.version = 1;
      } else {
        config.version = (this.configurations.get(config.id)?.version || 0) + 1;
      }

      config.updatedAt = now;

      // 加密敏感配置
      const encryptedConfig = await this.encryptConfigIfNeeded(config);

      // 更新内存中的配置
      this.configurations.set(config.id, encryptedConfig);

      // 保存到存储
      await this.saveToStorage();

      // 发射事件
      if (isNew) {
        this.emitEntityCreated(config);
      } else {
        this.emitEntityUpdated(config, config); // 简化处理
      }

      logger.info(`配置保存成功: ${config.key}`);
      return { success: true, data: config };
    } catch (error) {
      logger.error(`保存配置失败 [${config.key}]:`, error);
      return { success: false, error: error as Error };
    }
  }

  /**
   * 更新配置
   */
  async update(id: string, updates: Partial<Configuration>): Promise<DataResult<Configuration>> {
    try {
      await this.ensureLoaded();

      const existingConfig = this.configurations.get(id);
      if (!existingConfig) {
        return {
          success: false,
          error: new Error(`配置不存在: ${id}`)
        };
      }

      // 解密现有配置
      const decryptedConfig = await this.decryptConfigIfNeeded(existingConfig);

      // 合并更新
      const updatedConfig: Configuration = {
        ...decryptedConfig,
        ...updates,
        id, // 确保ID不被更改
        key: decryptedConfig.key, // 确保键不被更改
        createdAt: decryptedConfig.createdAt, // 确保创建时间不被更改
        updatedAt: new Date(),
        version: decryptedConfig.version + 1
      };

      return this.save(updatedConfig);
    } catch (error) {
      logger.error(`更新配置失败 [${id}]:`, error);
      return { success: false, error: error as Error };
    }
  }

  /**
   * 删除配置
   */
  async delete(id: string): Promise<DataResult<boolean>> {
    try {
      await this.ensureLoaded();

      const config = this.configurations.get(id);
      if (!config) {
        return { success: true, data: false };
      }

      // 检查是否为只读配置
      if (config.isReadonly) {
        return {
          success: false,
          error: new Error('只读配置不能删除')
        };
      }

      // 从内存中删除
      this.configurations.delete(id);

      // 保存到存储
      await this.saveToStorage();

      // 发射事件
      this.emitEntityDeleted(id);

      logger.info(`配置删除成功: ${id}`);
      return { success: true, data: true };
    } catch (error) {
      logger.error(`删除配置失败 [${id}]:`, error);
      return { success: false, error: error as Error };
    }
  }

  /**
   * 统计配置数量
   */
  async count(criteria?: SearchCriteria<Configuration>): Promise<DataResult<number>> {
    try {
      const allResult = await this.findAll(criteria);
      if (!allResult.success) {
        return { success: false, error: allResult.error };
      }

      return { success: true, data: allResult.data?.length || 0 };
    } catch (error) {
      logger.error('统计配置数量失败:', error);
      return { success: false, error: error as Error };
    }
  }

  /**
   * 分页查询实现
   */
  protected async findAllWithPagination(
    criteria: SearchCriteria<Configuration>,
    page: PageRequest
  ): Promise<DataResult<Configuration[]>> {
    try {
      const allResult = await this.findAll(criteria);
      if (!allResult.success) {
        return allResult;
      }

      const configs = allResult.data || [];

      // 应用排序
      const sortedConfigs = this.applySorting(configs, page.sort || criteria.sort);

      // 应用分页
      const startIndex = page.page * page.size;
      const endIndex = startIndex + page.size;
      const pagedConfigs = sortedConfigs.slice(startIndex, endIndex);

      return { success: true, data: pagedConfigs };
    } catch (error) {
      logger.error('分页查询配置失败:', error);
      return { success: false, error: error as Error };
    }
  }

  /**
   * 获取配置值
   */
  async getValue<T = any>(key: string, defaultValue?: T): Promise<T | undefined> {
    try {
      const result = await this.findByKey(key);
      if (result.success && result.data) {
        return result.data.value as T;
      }
      return defaultValue;
    } catch (error) {
      logger.error(`获取配置值失败 [${key}]:`, error);
      return defaultValue;
    }
  }

  /**
   * 设置配置值
   */
  async setValue(key: string, value: any, type?: ConfigurationType): Promise<DataResult<Configuration>> {
    try {
      const existingResult = await this.findByKey(key);

      if (existingResult.success && existingResult.data) {
        // 更新现有配置
        return this.update(existingResult.data.id, { value });
      } else {
        // 创建新配置
        const newConfig: Configuration = {
          id: '',
          key,
          value,
          type: type || this.inferType(value),
          category: ConfigurationCategory.USER,
          isEncrypted: false,
          isReadonly: false,
          createdAt: new Date(),
          updatedAt: new Date(),
          version: 1
        };

        return this.save(newConfig);
      }
    } catch (error) {
      logger.error(`设置配置值失败 [${key}]:`, error);
      return { success: false, error: error as Error };
    }
  }

  /**
   * 重置配置到默认值
   */
  async resetToDefault(key: string): Promise<DataResult<Configuration>> {
    try {
      const result = await this.findByKey(key);
      if (!result.success || !result.data) {
        return {
          success: false,
          error: new Error(`配置不存在: ${key}`)
        };
      }

      const config = result.data;
      if (config.defaultValue === undefined) {
        return {
          success: false,
          error: new Error(`配置没有默认值: ${key}`)
        };
      }

      return this.update(config.id, { value: config.defaultValue });
    } catch (error) {
      logger.error(`重置配置失败 [${key}]:`, error);
      return { success: false, error: error as Error };
    }
  }

  // 私有辅助方法
  private async ensureLoaded(): Promise<void> {
    if (this.loaded) {
      return;
    }

    try {
      const result = await this.storageProvider.get(this.STORAGE_KEY);

      if (result.success && result.data) {
        const configs = this.dataMapper.fromStorage(result.data) as Configuration[];

        for (const config of configs) {
          this.configurations.set(config.id, config);
        }
      }

      this.loaded = true;
      logger.info('配置加载完成');
    } catch (error) {
      logger.error('加载配置失败:', error);
      throw error;
    }
  }

  private async saveToStorage(): Promise<void> {
    try {
      const configs = Array.from(this.configurations.values());
      const storageData = this.dataMapper.toStorage(configs);

      const result = await this.storageProvider.put(this.STORAGE_KEY, storageData, {
        contentType: 'application/json'
      });

      if (!result.success) {
        throw result.error || new Error('保存配置到存储失败');
      }
    } catch (error) {
      logger.error('保存配置到存储失败:', error);
      throw error;
    }
  }

  private async encryptConfigIfNeeded(config: Configuration): Promise<Configuration> {
    if (!config.isEncrypted) {
      return config;
    }

    try {
      const encryptedValue = await this.cryptoService.encrypt(JSON.stringify(config.value));

      return {
        ...config,
        value: encryptedValue.encryptedData + ':' + encryptedValue.iv
      };
    } catch (error) {
      logger.error(`加密配置失败 [${config.key}]:`, error);
      throw error;
    }
  }

  private async decryptConfigIfNeeded(config: Configuration): Promise<Configuration> {
    if (!config.isEncrypted) {
      return config;
    }

    try {
      const decryptedResult = await this.cryptoService.decrypt(config.value);
      const decryptedValue = JSON.parse(new TextDecoder().decode(decryptedResult.decryptedData as ArrayBuffer));

      return {
        ...config,
        value: decryptedValue
      };
    } catch (error) {
      logger.error(`解密配置失败 [${config.key}]:`, error);
      // 返回原始配置，但标记解密失败
      return {
        ...config,
        value: '[解密失败]'
      };
    }
  }

  private validateConfiguration(config: Configuration): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!config.key || config.key.trim().length === 0) {
      errors.push('配置键不能为空');
    }

    if (config.value === undefined) {
      errors.push('配置值不能为undefined');
    }

    if (!Object.values(ConfigurationType).includes(config.type)) {
      errors.push('无效的配置类型');
    }

    if (!Object.values(ConfigurationCategory).includes(config.category)) {
      errors.push('无效的配置分类');
    }

    // 应用验证规则
    if (config.validationRules) {
      for (const rule of config.validationRules) {
        if (!this.validateRule(config.value, rule)) {
          errors.push(rule.message || `验证规则失败: ${rule.type}`);
        }
      }
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  private validateRule(value: any, rule: any): boolean {
    switch (rule.type) {
      case 'required':
        return value !== null && value !== undefined && value !== '';
      case 'min':
        return typeof value === 'number' && value >= rule.value;
      case 'max':
        return typeof value === 'number' && value <= rule.value;
      case 'pattern':
        return typeof value === 'string' && new RegExp(rule.value).test(value);
      case 'enum':
        return Array.isArray(rule.value) && rule.value.includes(value);
      case 'custom':
        return typeof rule.validator === 'function' && rule.validator(value);
      default:
        return true;
    }
  }

  private inferType(value: any): ConfigurationType {
    if (typeof value === 'string') return ConfigurationType.STRING;
    if (typeof value === 'number') return ConfigurationType.NUMBER;
    if (typeof value === 'boolean') return ConfigurationType.BOOLEAN;
    if (Array.isArray(value)) return ConfigurationType.ARRAY;
    if (typeof value === 'object') return ConfigurationType.OBJECT;
    return ConfigurationType.STRING;
  }

  private generateId(): string {
    return `config_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private applySearchCriteria(configs: Configuration[], criteria: SearchCriteria<Configuration>): Configuration[] {
    let filtered = configs;

    // 应用过滤条件
    if (criteria.filters) {
      for (const filter of criteria.filters) {
        filtered = filtered.filter(config => {
          const value = config[filter.property as keyof Configuration];
          return this.matchesFilter(value, filter.operator, filter.value, filter.values);
        });
      }
    }

    // 应用搜索关键词
    if (criteria.search) {
      const keyword = criteria.search.toLowerCase();
      filtered = filtered.filter(config =>
        config.key.toLowerCase().includes(keyword) ||
        (config.description && config.description.toLowerCase().includes(keyword))
      );
    }

    return filtered;
  }

  private matchesFilter(value: any, operator: FilterOperator, filterValue: any, filterValues?: any[]): boolean {
    switch (operator) {
      case FilterOperator.EQUALS:
        return value === filterValue;
      case FilterOperator.NOT_EQUALS:
        return value !== filterValue;
      case FilterOperator.IN:
        return filterValues ? filterValues.includes(value) : false;
      case FilterOperator.NOT_IN:
        return filterValues ? !filterValues.includes(value) : true;
      case FilterOperator.LIKE:
        return typeof value === 'string' && value.toLowerCase().includes(filterValue.toLowerCase());
      default:
        return true;
    }
  }

  private applySorting(configs: Configuration[], sortCriteria?: any[]): Configuration[] {
    if (!sortCriteria || sortCriteria.length === 0) {
      // 默认按键名排序
      return configs.sort((a, b) => a.key.localeCompare(b.key));
    }

    return configs.sort((a, b) => {
      for (const sort of sortCriteria) {
        const aValue = a[sort.property as keyof Configuration];
        const bValue = b[sort.property as keyof Configuration];

        let comparison = 0;
        if (aValue < bValue) comparison = -1;
        else if (aValue > bValue) comparison = 1;

        if (comparison !== 0) {
          return sort.direction === 'DESC' ? -comparison : comparison;
        }
      }
      return 0;
    });
  }
}
```

## ✅ 验收标准

### 功能验收
- [ ] ConfigRepository类创建成功
- [ ] 实现了所有Repository接口方法
- [ ] 支持配置的CRUD操作
- [ ] 支持配置加密和解密
- [ ] 支持配置验证规则
- [ ] 支持按分类和键查找配置

### 质量验收
- [ ] TypeScript编译无错误
- [ ] 代码符合项目编码规范
- [ ] 错误处理机制完善
- [ ] 日志记录完整

### 安全验收
- [ ] 敏感配置加密存储
- [ ] 只读配置保护机制
- [ ] 配置访问权限控制

## 🔄 幂等性保证

### 执行前检查
```bash
#!/bin/bash
echo "🔍 检查任务2.3执行状态..."

# 检查依赖任务
if [ ! -f "src/data/repositories/MemoryRepository.ts" ]; then
  echo "❌ 依赖任务Task-2.2未完成，请先执行Task-2.2"
  exit 1
fi

# 检查是否已存在ConfigRepository
if [ -f "src/data/repositories/ConfigRepository.ts" ]; then
  echo "⚠️  ConfigRepository已存在，任务可能已完成"
  echo "请确认是否需要重新执行此任务"
  read -p "是否继续？(y/N): " confirm
  if [ "$confirm" != "y" ]; then
    exit 1
  fi
fi

echo "✅ 前置检查完成，可以执行任务2.3"
```

### 执行后验证
```bash
#!/bin/bash
echo "🔍 验证任务2.3执行结果..."

# 检查文件是否创建成功
files=(
  "src/data/entities/Configuration.ts"
  "src/data/repositories/ConfigRepository.ts"
)

all_created=true
for file in "${files[@]}"; do
  if [ -f "$file" ]; then
    echo "✅ $file 创建成功"
  else
    echo "❌ $file 创建失败"
    all_created=false
  fi
done

# TypeScript编译检查
if npm run type-check > /dev/null 2>&1; then
  echo "✅ TypeScript编译检查通过"
else
  echo "❌ TypeScript编译检查失败"
  all_created=false
fi

if [ "$all_created" = true ]; then
  echo "🎉 任务2.3执行成功！"
  exit 0
else
  echo "💥 任务2.3执行失败，请检查错误信息"
  exit 1
fi
```

## 📝 完成确认

任务完成后，请在检查清单中标记：
- [ ] 任务2.3已完成
- [ ] Configuration实体定义完成
- [ ] ConfigRepository实现完成
- [ ] 配置加密功能正常工作
- [ ] 准备执行任务2.4

## 🔗 相关链接

- [上一个任务：Task-2.2 实现内存Repository](./task-2.2-memory-repository.md)
- [下一个任务：Task-2.4 重构缓存管理器](./task-2.4-cache-manager.md)
- [重构检查清单](../00-refactoring-checklist.md)
```