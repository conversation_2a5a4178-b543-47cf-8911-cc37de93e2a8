# 任务2.1：创建Repository接口

## 📋 任务概述

**任务ID**: Task-2.1  
**任务名称**: 创建Repository接口  
**所属阶段**: 阶段2 - 数据层重构  
**预计时间**: 2小时  
**依赖任务**: Phase-1 (基础设施层重构完成)  
**后续任务**: Task-2.2 (实现内存Repository)  

## 🎯 任务目标

设计和实现数据访问层的Repository模式接口，为业务层提供统一的数据访问抽象，支持不同的数据存储后端。

## 📋 前置条件检查

### 依赖检查
- [ ] Phase-1 所有任务已完成
- [ ] 基础设施层测试通过
- [ ] TypeScript环境正常

### 文件检查
- [ ] `src/infrastructure/` 目录完整
- [ ] 基础设施层所有组件可用

### 状态检查
```bash
# 检查Repository接口是否已存在
if [ -f "src/data/interfaces/IRepository.ts" ]; then
  echo "⚠️  Repository接口已存在，请确认是否需要重新创建"
  exit 1
else
  echo "✅ 可以开始创建Repository接口"
fi
```

## 🛠️ 实施步骤

### 步骤1：创建数据层目录结构
```bash
# 创建数据层目录
mkdir -p src/data/interfaces
mkdir -p src/data/repositories
mkdir -p src/data/mappers
mkdir -p src/data/cache
mkdir -p src/data/types
mkdir -p src/data/__tests__
```

### 步骤2：创建基础数据类型
**文件**: `src/data/types/DataTypes.ts`

```typescript
/**
 * 分页请求参数
 */
export interface PageRequest {
  page: number;
  size: number;
  sort?: SortCriteria[];
}

/**
 * 分页结果
 */
export interface PageResult<T> {
  content: T[];
  totalElements: number;
  totalPages: number;
  page: number;
  size: number;
  first: boolean;
  last: boolean;
}

/**
 * 排序条件
 */
export interface SortCriteria {
  property: string;
  direction: 'ASC' | 'DESC';
}

/**
 * 搜索条件
 */
export interface SearchCriteria<T = any> {
  filters?: FilterCriteria<T>[];
  sort?: SortCriteria[];
  search?: string;
}

/**
 * 过滤条件
 */
export interface FilterCriteria<T = any> {
  property: keyof T;
  operator: FilterOperator;
  value: any;
  values?: any[];
}

/**
 * 过滤操作符
 */
export enum FilterOperator {
  EQUALS = 'eq',
  NOT_EQUALS = 'ne',
  GREATER_THAN = 'gt',
  GREATER_THAN_OR_EQUAL = 'gte',
  LESS_THAN = 'lt',
  LESS_THAN_OR_EQUAL = 'lte',
  LIKE = 'like',
  IN = 'in',
  NOT_IN = 'nin',
  IS_NULL = 'null',
  IS_NOT_NULL = 'not_null',
  BETWEEN = 'between'
}

/**
 * 实体基类
 */
export interface BaseEntity {
  id: string;
  createdAt: Date;
  updatedAt: Date;
  version?: number;
}

/**
 * 数据操作结果
 */
export interface DataResult<T> {
  success: boolean;
  data?: T;
  error?: Error;
  metadata?: Record<string, any>;
}

/**
 * 批量操作结果
 */
export interface BatchResult<T> {
  success: boolean;
  results: DataResult<T>[];
  successCount: number;
  failureCount: number;
  errors: Error[];
}
```

### 步骤3：创建Repository基础接口
**文件**: `src/data/interfaces/IRepository.ts`

```typescript
import {
  PageRequest,
  PageResult,
  SearchCriteria,
  DataResult,
  BatchResult
} from '../types/DataTypes';
import { BaseEntity } from '../entities/BaseEntity';

/**
 * Repository基础接口
 * 定义数据访问层的标准操作
 */
export interface IRepository<T extends BaseEntity, K = string> {
  // 基础查询操作
  findById(id: K): Promise<DataResult<T | null>>;
  findAll(criteria?: SearchCriteria<T>): Promise<DataResult<T[]>>;
  findPage(criteria: SearchCriteria<T>, page: PageRequest): Promise<DataResult<PageResult<T>>>;
  
  // 条件查询
  findOne(criteria: SearchCriteria<T>): Promise<DataResult<T | null>>;
  findBy(property: keyof T, value: any): Promise<DataResult<T[]>>;
  
  // 保存操作
  save(entity: T): Promise<DataResult<T>>;
  saveAll(entities: T[]): Promise<BatchResult<T>>;
  
  // 更新操作
  update(id: K, updates: Partial<T>): Promise<DataResult<T>>;
  updateAll(criteria: SearchCriteria<T>, updates: Partial<T>): Promise<DataResult<number>>;
  
  // 删除操作
  delete(id: K): Promise<DataResult<boolean>>;
  deleteAll(ids: K[]): Promise<BatchResult<boolean>>;
  deleteBy(criteria: SearchCriteria<T>): Promise<DataResult<number>>;
  
  // 统计操作
  count(criteria?: SearchCriteria<T>): Promise<DataResult<number>>;
  exists(id: K): Promise<DataResult<boolean>>;
  existsBy(criteria: SearchCriteria<T>): Promise<DataResult<boolean>>;
  
  // 批量操作
  batchInsert(entities: T[]): Promise<BatchResult<T>>;
  batchUpdate(updates: Array<{ id: K; data: Partial<T> }>): Promise<BatchResult<T>>;
  batchDelete(ids: K[]): Promise<BatchResult<boolean>>;
}

/**
 * 可观察Repository接口
 * 支持数据变更监听
 */
export interface IObservableRepository<T extends BaseEntity, K = string> extends IRepository<T, K> {
  // 数据变更监听
  onEntityCreated(callback: (entity: T) => void): () => void;
  onEntityUpdated(callback: (entity: T, oldEntity: T) => void): () => void;
  onEntityDeleted(callback: (id: K) => void): () => void;
  
  // 批量变更监听
  onBatchCreated(callback: (entities: T[]) => void): () => void;
  onBatchUpdated(callback: (entities: T[]) => void): () => void;
  onBatchDeleted(callback: (ids: K[]) => void): () => void;
}

/**
 * 缓存Repository接口
 * 支持缓存功能的Repository
 */
export interface ICacheableRepository<T extends BaseEntity, K = string> extends IRepository<T, K> {
  // 缓存管理
  clearCache(): Promise<void>;
  clearCacheFor(id: K): Promise<void>;
  clearCacheBy(criteria: SearchCriteria<T>): Promise<void>;
  
  // 缓存预热
  preloadCache(ids: K[]): Promise<void>;
  preloadCacheBy(criteria: SearchCriteria<T>): Promise<void>;
  
  // 缓存统计
  getCacheStats(): Promise<{
    hitCount: number;
    missCount: number;
    hitRate: number;
    size: number;
  }>;
}

/**
 * 事务Repository接口
 * 支持事务操作的Repository
 */
export interface ITransactionalRepository<T extends BaseEntity, K = string> extends IRepository<T, K> {
  // 事务操作
  transaction<R>(operation: (repo: IRepository<T, K>) => Promise<R>): Promise<R>;
  
  // 批量事务操作
  batchTransaction<R>(operations: Array<(repo: IRepository<T, K>) => Promise<R>>): Promise<R[]>;
}
```

### 步骤4：创建Repository抽象基类
**文件**: `src/data/repositories/BaseRepository.ts`

```typescript
import { IRepository, IObservableRepository } from '../interfaces/IRepository';
import { 
  BaseEntity, 
  SearchCriteria, 
  PageRequest, 
  PageResult, 
  DataResult, 
  BatchResult,
  FilterCriteria,
  FilterOperator 
} from '../types/DataTypes';
import { EventEmitter } from '../../infrastructure/utils/EventEmitter';
import { logger } from '../../infrastructure/utils/Logger';

/**
 * Repository抽象基类
 * 提供通用功能的默认实现
 */
export abstract class BaseRepository<T extends BaseEntity, K = string> 
  implements IObservableRepository<T, K> {
  
  protected eventEmitter = new EventEmitter();
  protected entityName: string;

  constructor(entityName: string) {
    this.entityName = entityName;
  }

  // 抽象方法，子类必须实现
  abstract findById(id: K): Promise<DataResult<T | null>>;
  abstract findAll(criteria?: SearchCriteria<T>): Promise<DataResult<T[]>>;
  abstract save(entity: T): Promise<DataResult<T>>;
  abstract update(id: K, updates: Partial<T>): Promise<DataResult<T>>;
  abstract delete(id: K): Promise<DataResult<boolean>>;
  abstract count(criteria?: SearchCriteria<T>): Promise<DataResult<number>>;

  /**
   * 分页查询
   */
  async findPage(criteria: SearchCriteria<T>, page: PageRequest): Promise<DataResult<PageResult<T>>> {
    try {
      // 获取总数
      const countResult = await this.count(criteria);
      if (!countResult.success) {
        return { success: false, error: countResult.error };
      }

      const totalElements = countResult.data || 0;
      const totalPages = Math.ceil(totalElements / page.size);

      // 获取当前页数据
      const enhancedCriteria = {
        ...criteria,
        sort: page.sort || criteria.sort
      };

      const dataResult = await this.findAllWithPagination(enhancedCriteria, page);
      if (!dataResult.success) {
        return { success: false, error: dataResult.error };
      }

      const pageResult: PageResult<T> = {
        content: dataResult.data || [],
        totalElements,
        totalPages,
        page: page.page,
        size: page.size,
        first: page.page === 0,
        last: page.page >= totalPages - 1
      };

      return { success: true, data: pageResult };
    } catch (error) {
      logger.error(`分页查询${this.entityName}失败:`, error);
      return { success: false, error: error as Error };
    }
  }

  /**
   * 查找单个实体
   */
  async findOne(criteria: SearchCriteria<T>): Promise<DataResult<T | null>> {
    try {
      const result = await this.findAll(criteria);
      if (!result.success) {
        return result;
      }

      const entities = result.data || [];
      return { 
        success: true, 
        data: entities.length > 0 ? entities[0] : null 
      };
    } catch (error) {
      logger.error(`查找单个${this.entityName}失败:`, error);
      return { success: false, error: error as Error };
    }
  }

  /**
   * 按属性查找
   */
  async findBy(property: keyof T, value: any): Promise<DataResult<T[]>> {
    const criteria: SearchCriteria<T> = {
      filters: [{
        property,
        operator: FilterOperator.EQUALS,
        value
      }]
    };

    return this.findAll(criteria);
  }

  /**
   * 批量保存
   */
  async saveAll(entities: T[]): Promise<BatchResult<T>> {
    const results: DataResult<T>[] = [];
    const errors: Error[] = [];
    let successCount = 0;

    for (const entity of entities) {
      try {
        const result = await this.save(entity);
        results.push(result);
        
        if (result.success) {
          successCount++;
        } else if (result.error) {
          errors.push(result.error);
        }
      } catch (error) {
        const errorResult = { success: false, error: error as Error };
        results.push(errorResult);
        errors.push(error as Error);
      }
    }

    const batchResult: BatchResult<T> = {
      success: errors.length === 0,
      results,
      successCount,
      failureCount: entities.length - successCount,
      errors
    };

    if (successCount > 0) {
      this.eventEmitter.emit('batchCreated', results.filter(r => r.success).map(r => r.data));
    }

    return batchResult;
  }

  /**
   * 批量更新
   */
  async updateAll(criteria: SearchCriteria<T>, updates: Partial<T>): Promise<DataResult<number>> {
    try {
      // 先查找符合条件的实体
      const findResult = await this.findAll(criteria);
      if (!findResult.success) {
        return { success: false, error: findResult.error };
      }

      const entities = findResult.data || [];
      let updatedCount = 0;

      for (const entity of entities) {
        const updateResult = await this.update(entity.id as K, updates);
        if (updateResult.success) {
          updatedCount++;
        }
      }

      return { success: true, data: updatedCount };
    } catch (error) {
      logger.error(`批量更新${this.entityName}失败:`, error);
      return { success: false, error: error as Error };
    }
  }

  /**
   * 批量删除
   */
  async deleteAll(ids: K[]): Promise<BatchResult<boolean>> {
    const results: DataResult<boolean>[] = [];
    const errors: Error[] = [];
    let successCount = 0;

    for (const id of ids) {
      try {
        const result = await this.delete(id);
        results.push(result);
        
        if (result.success) {
          successCount++;
        } else if (result.error) {
          errors.push(result.error);
        }
      } catch (error) {
        const errorResult = { success: false, error: error as Error };
        results.push(errorResult);
        errors.push(error as Error);
      }
    }

    const batchResult: BatchResult<boolean> = {
      success: errors.length === 0,
      results,
      successCount,
      failureCount: ids.length - successCount,
      errors
    };

    if (successCount > 0) {
      this.eventEmitter.emit('batchDeleted', ids.slice(0, successCount));
    }

    return batchResult;
  }

  /**
   * 按条件删除
   */
  async deleteBy(criteria: SearchCriteria<T>): Promise<DataResult<number>> {
    try {
      // 先查找符合条件的实体
      const findResult = await this.findAll(criteria);
      if (!findResult.success) {
        return { success: false, error: findResult.error };
      }

      const entities = findResult.data || [];
      const ids = entities.map(entity => entity.id as K);
      
      const deleteResult = await this.deleteAll(ids);
      
      return { 
        success: deleteResult.success, 
        data: deleteResult.successCount,
        error: deleteResult.errors[0]
      };
    } catch (error) {
      logger.error(`按条件删除${this.entityName}失败:`, error);
      return { success: false, error: error as Error };
    }
  }

  /**
   * 检查实体是否存在
   */
  async exists(id: K): Promise<DataResult<boolean>> {
    try {
      const result = await this.findById(id);
      return { 
        success: true, 
        data: result.success && result.data !== null 
      };
    } catch (error) {
      logger.error(`检查${this.entityName}是否存在失败:`, error);
      return { success: false, error: error as Error };
    }
  }

  /**
   * 按条件检查是否存在
   */
  async existsBy(criteria: SearchCriteria<T>): Promise<DataResult<boolean>> {
    try {
      const countResult = await this.count(criteria);
      return { 
        success: countResult.success, 
        data: (countResult.data || 0) > 0,
        error: countResult.error
      };
    } catch (error) {
      logger.error(`按条件检查${this.entityName}是否存在失败:`, error);
      return { success: false, error: error as Error };
    }
  }

  // 批量操作方法（默认实现，子类可重写以优化性能）
  async batchInsert(entities: T[]): Promise<BatchResult<T>> {
    return this.saveAll(entities);
  }

  async batchUpdate(updates: Array<{ id: K; data: Partial<T> }>): Promise<BatchResult<T>> {
    const results: DataResult<T>[] = [];
    const errors: Error[] = [];
    let successCount = 0;

    for (const { id, data } of updates) {
      try {
        const result = await this.update(id, data);
        results.push(result);
        
        if (result.success) {
          successCount++;
        } else if (result.error) {
          errors.push(result.error);
        }
      } catch (error) {
        const errorResult = { success: false, error: error as Error };
        results.push(errorResult);
        errors.push(error as Error);
      }
    }

    return {
      success: errors.length === 0,
      results,
      successCount,
      failureCount: updates.length - successCount,
      errors
    };
  }

  async batchDelete(ids: K[]): Promise<BatchResult<boolean>> {
    return this.deleteAll(ids);
  }

  // 事件监听方法
  onEntityCreated(callback: (entity: T) => void): () => void {
    return this.eventEmitter.on('entityCreated', callback).unsubscribe;
  }

  onEntityUpdated(callback: (entity: T, oldEntity: T) => void): () => void {
    return this.eventEmitter.on('entityUpdated', callback).unsubscribe;
  }

  onEntityDeleted(callback: (id: K) => void): () => void {
    return this.eventEmitter.on('entityDeleted', callback).unsubscribe;
  }

  onBatchCreated(callback: (entities: T[]) => void): () => void {
    return this.eventEmitter.on('batchCreated', callback).unsubscribe;
  }

  onBatchUpdated(callback: (entities: T[]) => void): () => void {
    return this.eventEmitter.on('batchUpdated', callback).unsubscribe;
  }

  onBatchDeleted(callback: (ids: K[]) => void): () => void {
    return this.eventEmitter.on('batchDeleted', callback).unsubscribe;
  }

  // 受保护的辅助方法
  protected abstract findAllWithPagination(
    criteria: SearchCriteria<T>, 
    page: PageRequest
  ): Promise<DataResult<T[]>>;

  protected emitEntityCreated(entity: T): void {
    this.eventEmitter.emit('entityCreated', entity);
  }

  protected emitEntityUpdated(entity: T, oldEntity: T): void {
    this.eventEmitter.emit('entityUpdated', entity, oldEntity);
  }

  protected emitEntityDeleted(id: K): void {
    this.eventEmitter.emit('entityDeleted', id);
  }
}
```

## ✅ 验收标准

### 功能验收
- [ ] Repository接口设计完整，覆盖所有CRUD操作
- [ ] 支持分页、排序、过滤等高级查询功能
- [ ] 支持批量操作和事务处理
- [ ] 支持事件监听和缓存功能
- [ ] BaseRepository提供通用功能实现

### 质量验收
- [ ] TypeScript编译无错误
- [ ] 接口设计符合SOLID原则
- [ ] 代码符合项目编码规范
- [ ] 文档注释完整清晰

### 设计验收
- [ ] Repository模式实现正确
- [ ] 抽象层次合理，易于扩展
- [ ] 支持多种数据存储后端
- [ ] 错误处理机制完善

## 🔄 幂等性保证

### 执行前检查
```bash
#!/bin/bash
echo "🔍 检查任务2.1执行状态..."

# 检查依赖任务
if [ ! -f "src/infrastructure/utils/Logger.ts" ]; then
  echo "❌ 依赖任务Phase-1未完成，请先完成基础设施层重构"
  exit 1
fi

# 检查是否已存在Repository接口
if [ -f "src/data/interfaces/IRepository.ts" ]; then
  echo "⚠️  Repository接口已存在，任务可能已完成"
  echo "请确认是否需要重新执行此任务"
  read -p "是否继续？(y/N): " confirm
  if [ "$confirm" != "y" ]; then
    exit 1
  fi
fi

echo "✅ 前置检查完成，可以执行任务2.1"
```

### 执行后验证
```bash
#!/bin/bash
echo "🔍 验证任务2.1执行结果..."

# 检查文件是否创建成功
files=(
  "src/data/types/DataTypes.ts"
  "src/data/interfaces/IRepository.ts"
  "src/data/repositories/BaseRepository.ts"
)

all_created=true
for file in "${files[@]}"; do
  if [ -f "$file" ]; then
    echo "✅ $file 创建成功"
  else
    echo "❌ $file 创建失败"
    all_created=false
  fi
done

# TypeScript编译检查
if npm run type-check > /dev/null 2>&1; then
  echo "✅ TypeScript编译检查通过"
else
  echo "❌ TypeScript编译检查失败"
  all_created=false
fi

if [ "$all_created" = true ]; then
  echo "🎉 任务2.1执行成功！"
  exit 0
else
  echo "💥 任务2.1执行失败，请检查错误信息"
  exit 1
fi
```

## 📝 完成确认

任务完成后，请在检查清单中标记：
- [ ] 任务2.1已完成
- [ ] Repository接口创建成功
- [ ] BaseRepository基类实现完成
- [ ] TypeScript编译通过
- [ ] 准备执行任务2.2

## 🔗 相关链接

- [上一个阶段：Phase-1 基础设施层重构](../phase1/task-1.7-infrastructure-test-page.md)
- [下一个任务：Task-2.2 实现内存Repository](./task-2.2-memory-repository.md)
- [重构检查清单](../00-refactoring-checklist.md)
