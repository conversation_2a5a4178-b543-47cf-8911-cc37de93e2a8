# 基础设施层接口设计

## 📋 任务概述

**任务ID**: Interface-01  
**任务名称**: 基础设施层接口设计  
**所属阶段**: 接口设计阶段  
**预计时间**: 2小时  
**依赖任务**: 无  

## 🎯 任务目标

设计基础设施层的所有核心接口，为后续实现提供明确的契约定义。

## 📐 接口设计

### 1. 存储提供者接口

#### IStorageProvider
```typescript
/**
 * 存储提供者统一接口
 * 所有存储提供者必须实现此接口
 */
export interface IStorageProvider {
  // 基本属性
  readonly name: string;
  readonly type: StorageType;
  readonly isInitialized: boolean;
  
  // 生命周期管理
  initialize(config: IStorageConfig): Promise<void>;
  dispose(): Promise<void>;
  
  // 连接测试
  testConnection(): Promise<boolean>;
  
  // 基础CRUD操作
  get(key: string, options?: GetOptions): Promise<StorageResult<any>>;
  put(key: string, data: any, options?: PutOptions): Promise<StorageResult<void>>;
  delete(key: string): Promise<StorageResult<void>>;
  list(prefix?: string, options?: ListOptions): Promise<StorageResult<string[]>>;
  
  // 批量操作
  getBatch(keys: string[]): Promise<StorageResult<Record<string, any>>>;
  putBatch(items: Record<string, any>): Promise<StorageResult<void>>;
  deleteBatch(keys: string[]): Promise<StorageResult<void>>;
  
  // 元数据操作
  getMetadata(key: string): Promise<StorageResult<ObjectMetadata>>;
  
  // 流式操作（大文件支持）
  getStream(key: string): Promise<ReadableStream>;
  putStream(key: string, stream: ReadableStream): Promise<StorageResult<void>>;
  
  // URL生成
  getSignedUrl(key: string, options?: SignedUrlOptions): Promise<string>;
}
```

#### 相关类型定义
```typescript
export enum StorageType {
  HUAWEI_OBS = 'huaweiObs',
  MINIO = 'minio',
  LOCAL_STORAGE = 'localStorage',
  MEMORY_STORAGE = 'memoryStorage',
  AMAZON_S3 = 'amazonS3',
  ALIYUN_OSS = 'aliyunOss',
  TENCENT_COS = 'tencentCos'
}

export interface IStorageConfig {
  readonly type: StorageType;
  readonly name: string;
  readonly endpoint?: string;
  readonly region?: string;
  readonly accessKey?: string;
  readonly secretKey?: string;
  readonly bucketName?: string;
  readonly timeout?: number;
  readonly retryCount?: number;
  readonly [key: string]: any;
}

export interface StorageResult<T> {
  success: boolean;
  data?: T;
  error?: Error;
  metadata?: Record<string, any>;
}

export interface ObjectMetadata {
  size: number;
  lastModified: Date;
  etag: string;
  contentType: string;
  customMetadata?: Record<string, string>;
}

export interface GetOptions {
  range?: { start: number; end: number };
  saveByType?: 'text' | 'arraybuffer' | 'blob';
}

export interface PutOptions {
  contentType?: string;
  metadata?: Record<string, string>;
  overwrite?: boolean;
}

export interface ListOptions {
  maxKeys?: number;
  marker?: string;
  delimiter?: string;
}

export interface SignedUrlOptions {
  expires?: number;
  method?: 'GET' | 'PUT' | 'DELETE';
}
```

### 2. 网络客户端接口

#### IHttpClient
```typescript
/**
 * HTTP客户端统一接口
 */
export interface IHttpClient {
  // 基础HTTP方法
  get<T>(url: string, options?: RequestOptions): Promise<HttpResponse<T>>;
  post<T>(url: string, data?: any, options?: RequestOptions): Promise<HttpResponse<T>>;
  put<T>(url: string, data?: any, options?: RequestOptions): Promise<HttpResponse<T>>;
  delete<T>(url: string, options?: RequestOptions): Promise<HttpResponse<T>>;
  patch<T>(url: string, data?: any, options?: RequestOptions): Promise<HttpResponse<T>>;
  head<T>(url: string, options?: RequestOptions): Promise<HttpResponse<T>>;
  
  // 流式请求
  getStream(url: string, options?: RequestOptions): Promise<ReadableStream>;
  postStream(url: string, stream: ReadableStream, options?: RequestOptions): Promise<HttpResponse<void>>;
  
  // 拦截器管理
  addRequestInterceptor(interceptor: RequestInterceptor): string;
  addResponseInterceptor(interceptor: ResponseInterceptor): string;
  removeRequestInterceptor(id: string): void;
  removeResponseInterceptor(id: string): void;
  
  // 配置管理
  setDefaultConfig(config: Partial<RequestOptions>): void;
  getDefaultConfig(): RequestOptions;
}
```

#### 相关类型定义
```typescript
export interface RequestOptions {
  headers?: Record<string, string>;
  timeout?: number;
  retryCount?: number;
  retryDelay?: number;
  responseType?: 'json' | 'text' | 'blob' | 'arraybuffer';
  withCredentials?: boolean;
  signal?: AbortSignal;
}

export interface HttpResponse<T> {
  data: T;
  status: number;
  statusText: string;
  headers: Record<string, string>;
  config: RequestOptions;
}

export type RequestInterceptor = (config: RequestOptions) => RequestOptions | Promise<RequestOptions>;
export type ResponseInterceptor = <T>(response: HttpResponse<T>) => HttpResponse<T> | Promise<HttpResponse<T>>;
```

### 3. 加密服务接口

#### ICryptoService
```typescript
/**
 * 加密服务统一接口
 */
export interface ICryptoService {
  // 基础加密解密
  encrypt(data: string | ArrayBuffer, key?: string): Promise<EncryptResult>;
  decrypt(encryptedData: string | ArrayBuffer, key?: string): Promise<DecryptResult>;
  
  // 密钥管理
  generateKey(): Promise<string>;
  deriveKey(password: string, salt: string): Promise<string>;
  
  // 哈希函数
  hash(data: string | ArrayBuffer, algorithm?: HashAlgorithm): Promise<string>;
  
  // 流式加密
  createEncryptStream(key: string): TransformStream;
  createDecryptStream(key: string): TransformStream;
  
  // 数字签名
  sign(data: string | ArrayBuffer, privateKey: string): Promise<string>;
  verify(data: string | ArrayBuffer, signature: string, publicKey: string): Promise<boolean>;
}
```

#### 相关类型定义
```typescript
export interface EncryptResult {
  encryptedData: string;
  iv: string;
  salt?: string;
  algorithm: string;
}

export interface DecryptResult {
  decryptedData: string | ArrayBuffer;
  algorithm: string;
}

export enum HashAlgorithm {
  SHA256 = 'SHA-256',
  SHA512 = 'SHA-512',
  MD5 = 'MD5'
}
```

### 4. 工具接口

#### ILogger
```typescript
/**
 * 日志记录器接口
 */
export interface ILogger {
  debug(message: string, ...args: any[]): void;
  info(message: string, ...args: any[]): void;
  warn(message: string, ...args: any[]): void;
  error(message: string, ...args: any[]): void;
  
  setLevel(level: LogLevel): void;
  getLevel(): LogLevel;
  
  addAppender(appender: ILogAppender): void;
  removeAppender(appender: ILogAppender): void;
}

export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3
}

export interface ILogAppender {
  append(level: LogLevel, message: string, timestamp: Date, ...args: any[]): void;
}
```

#### IEventEmitter
```typescript
/**
 * 事件发射器接口
 */
export interface IEventEmitter {
  // 事件订阅
  on<T>(event: string, handler: EventHandler<T>): EventSubscription;
  once<T>(event: string, handler: EventHandler<T>): EventSubscription;
  off(event: string, handler?: EventHandler<any>): void;
  
  // 事件发布
  emit<T>(event: string, data?: T): void;
  
  // 事件管理
  clear(): void;
  getListeners(event: string): EventHandler<any>[];
  hasListeners(event: string): boolean;
}

export type EventHandler<T> = (data: T) => void | Promise<void>;

export interface EventSubscription {
  unsubscribe(): void;
}
```

#### IValidator
```typescript
/**
 * 数据验证器接口
 */
export interface IValidator {
  // 基础验证
  isRequired(value: any): boolean;
  isString(value: any): boolean;
  isNumber(value: any): boolean;
  isBoolean(value: any): boolean;
  isArray(value: any): boolean;
  isObject(value: any): boolean;
  
  // 格式验证
  isEmail(value: string): boolean;
  isUrl(value: string): boolean;
  isUuid(value: string): boolean;
  
  // 范围验证
  minLength(value: string, min: number): boolean;
  maxLength(value: string, max: number): boolean;
  min(value: number, min: number): boolean;
  max(value: number, max: number): boolean;
  
  // 自定义验证
  custom(value: any, validator: CustomValidator): boolean;
  
  // 批量验证
  validate(data: any, schema: ValidationSchema): ValidationResult;
}

export type CustomValidator = (value: any) => boolean;

export interface ValidationSchema {
  [key: string]: ValidationRule[];
}

export interface ValidationRule {
  type: 'required' | 'string' | 'number' | 'email' | 'url' | 'minLength' | 'maxLength' | 'custom';
  value?: any;
  message?: string;
  validator?: CustomValidator;
}

export interface ValidationResult {
  valid: boolean;
  errors: ValidationError[];
}

export interface ValidationError {
  field: string;
  message: string;
  value: any;
}
```

## ✅ 验收标准

### 功能验收
- [ ] 所有接口定义完整且类型安全
- [ ] 接口设计遵循SOLID原则
- [ ] 接口支持扩展和实现
- [ ] 类型定义覆盖所有使用场景

### 质量验收
- [ ] 接口文档清晰完整
- [ ] 类型定义准确无误
- [ ] 接口设计一致性良好
- [ ] 支持TypeScript类型检查

## 🔄 幂等性检查

### 执行前检查
```bash
# 检查接口文件是否已存在
if [ -f "src/infrastructure/interfaces/IStorageProvider.ts" ]; then
  echo "接口文件已存在，跳过创建"
else
  echo "开始创建接口文件"
fi
```

### 执行后验证
```bash
# 验证接口文件是否创建成功
if [ -f "src/infrastructure/interfaces/IStorageProvider.ts" ] && 
   [ -f "src/infrastructure/interfaces/IHttpClient.ts" ] && 
   [ -f "src/infrastructure/interfaces/ICryptoService.ts" ]; then
  echo "✅ 接口文件创建成功"
else
  echo "❌ 接口文件创建失败"
fi
```

## 📝 实施步骤

1. **创建接口目录**
   ```bash
   mkdir -p src/infrastructure/interfaces
   ```

2. **创建存储接口文件**
   - 创建 `IStorageProvider.ts`
   - 定义所有存储相关类型

3. **创建网络接口文件**
   - 创建 `IHttpClient.ts`
   - 定义所有网络相关类型

4. **创建加密接口文件**
   - 创建 `ICryptoService.ts`
   - 定义所有加密相关类型

5. **创建工具接口文件**
   - 创建 `ILogger.ts`
   - 创建 `IEventEmitter.ts`
   - 创建 `IValidator.ts`

6. **创建索引文件**
   - 创建 `index.ts` 导出所有接口

## 🚨 注意事项

1. **接口稳定性** - 接口一旦定义，尽量避免破坏性变更
2. **类型安全** - 确保所有类型定义准确无误
3. **文档完整** - 每个接口都要有详细的注释说明
4. **向后兼容** - 新增功能时保持向后兼容性

## 📚 相关文档

- [TypeScript接口设计最佳实践](https://www.typescriptlang.org/docs/handbook/interfaces.html)
- [SOLID原则在接口设计中的应用](https://en.wikipedia.org/wiki/SOLID)
- [API设计指南](https://docs.microsoft.com/en-us/azure/architecture/best-practices/api-design)
