# Task 5.3: 重构UI工具

## 📋 任务概述

- **任务ID**: 5.3
- **任务名称**: 重构UI工具
- **所属阶段**: Phase 5 - 表现层重构
- **预估时间**: 0.5天
- **任务类型**: 重构
- **优先级**: 中
- **依赖任务**: 5.1, 5.2
- **负责人**: 开发团队
- **状态**: 待执行

## 🎯 任务目标

### 主要目标
1. **重构UI工具函数** - 提供可复用的UI辅助工具
2. **实现主题管理** - 支持多主题切换和自定义主题
3. **实现国际化支持** - 多语言支持和本地化
4. **创建响应式工具** - 适配不同屏幕尺寸
5. **实现动画工具** - 提供流畅的用户体验

### 技术目标
- 创建模块化的UI工具库
- 实现TypeScript类型安全
- 提供完整的工具函数文档
- 确保工具函数的可测试性
- 支持按需加载和tree-shaking

## 📋 前置条件检查

### 依赖检查
```bash
# 检查核心组件是否完成
- [ ] src/presentation/components/core/ 目录存在
- [ ] 核心组件接口已定义
- [ ] 页面组件已重构完成

# 检查技术依赖
- [ ] React 18+ 已安装
- [ ] TypeScript 配置正确
- [ ] CSS-in-JS 库已配置
- [ ] 国际化库已安装
```

### 环境检查
```bash
# 检查开发环境
npm run type-check  # TypeScript检查
npm run lint        # 代码质量检查
npm run test        # 现有测试通过
```

## 🛠️ 实施步骤

### 步骤1: 创建UI工具接口定义 (15分钟)

#### 1.1 创建主题管理接口
```typescript
// src/presentation/interfaces/IThemeManager.ts
export interface ITheme {
  name: string;
  colors: {
    primary: string;
    secondary: string;
    background: string;
    surface: string;
    text: string;
    textSecondary: string;
    border: string;
    error: string;
    warning: string;
    success: string;
    info: string;
  };
  typography: {
    fontFamily: string;
    fontSize: {
      xs: string;
      sm: string;
      md: string;
      lg: string;
      xl: string;
    };
    fontWeight: {
      light: number;
      normal: number;
      medium: number;
      bold: number;
    };
  };
  spacing: {
    xs: string;
    sm: string;
    md: string;
    lg: string;
    xl: string;
  };
  borderRadius: {
    sm: string;
    md: string;
    lg: string;
  };
  shadows: {
    sm: string;
    md: string;
    lg: string;
  };
}

export interface IThemeManager {
  getCurrentTheme(): ITheme;
  setTheme(themeName: string): void;
  getAvailableThemes(): string[];
  registerTheme(theme: ITheme): void;
  createCustomTheme(baseTheme: string, overrides: Partial<ITheme>): ITheme;
}
```

#### 1.2 创建国际化接口
```typescript
// src/presentation/interfaces/II18nManager.ts
export interface ITranslation {
  [key: string]: string | ITranslation;
}

export interface ILocale {
  code: string;
  name: string;
  translations: ITranslation;
}

export interface II18nManager {
  getCurrentLocale(): string;
  setLocale(locale: string): void;
  getAvailableLocales(): string[];
  translate(key: string, params?: Record<string, any>): string;
  registerLocale(locale: ILocale): void;
  formatDate(date: Date, format?: string): string;
  formatNumber(number: number, options?: Intl.NumberFormatOptions): string;
}
```

#### 1.3 创建响应式工具接口
```typescript
// src/presentation/interfaces/IResponsiveUtils.ts
export interface IBreakpoints {
  xs: number;
  sm: number;
  md: number;
  lg: number;
  xl: number;
}

export interface IResponsiveUtils {
  getBreakpoints(): IBreakpoints;
  getCurrentBreakpoint(): string;
  isBreakpoint(breakpoint: string): boolean;
  isMobile(): boolean;
  isTablet(): boolean;
  isDesktop(): boolean;
  useMediaQuery(query: string): boolean;
}
```

### 步骤2: 实现主题管理器 (30分钟)

#### 2.1 创建默认主题
```typescript
// src/presentation/themes/defaultTheme.ts
export const lightTheme: ITheme = {
  name: 'light',
  colors: {
    primary: '#1890ff',
    secondary: '#722ed1',
    background: '#ffffff',
    surface: '#fafafa',
    text: '#262626',
    textSecondary: '#8c8c8c',
    border: '#d9d9d9',
    error: '#ff4d4f',
    warning: '#faad14',
    success: '#52c41a',
    info: '#1890ff'
  },
  typography: {
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
    fontSize: {
      xs: '12px',
      sm: '14px',
      md: '16px',
      lg: '18px',
      xl: '20px'
    },
    fontWeight: {
      light: 300,
      normal: 400,
      medium: 500,
      bold: 600
    }
  },
  spacing: {
    xs: '4px',
    sm: '8px',
    md: '16px',
    lg: '24px',
    xl: '32px'
  },
  borderRadius: {
    sm: '4px',
    md: '8px',
    lg: '12px'
  },
  shadows: {
    sm: '0 1px 3px rgba(0, 0, 0, 0.1)',
    md: '0 4px 6px rgba(0, 0, 0, 0.1)',
    lg: '0 10px 15px rgba(0, 0, 0, 0.1)'
  }
};

export const darkTheme: ITheme = {
  ...lightTheme,
  name: 'dark',
  colors: {
    primary: '#1890ff',
    secondary: '#722ed1',
    background: '#141414',
    surface: '#1f1f1f',
    text: '#ffffff',
    textSecondary: '#a6a6a6',
    border: '#434343',
    error: '#ff7875',
    warning: '#ffc53d',
    success: '#73d13d',
    info: '#40a9ff'
  }
};
```

#### 2.2 实现主题管理器
```typescript
// src/presentation/utils/ThemeManager.ts
import { ITheme, IThemeManager } from '../interfaces/IThemeManager';
import { lightTheme, darkTheme } from '../themes/defaultTheme';

export class ThemeManager implements IThemeManager {
  private themes: Map<string, ITheme> = new Map();
  private currentTheme: string = 'light';
  private listeners: Set<(theme: ITheme) => void> = new Set();

  constructor() {
    this.registerTheme(lightTheme);
    this.registerTheme(darkTheme);
    this.loadSavedTheme();
  }

  getCurrentTheme(): ITheme {
    return this.themes.get(this.currentTheme) || lightTheme;
  }

  setTheme(themeName: string): void {
    if (this.themes.has(themeName)) {
      this.currentTheme = themeName;
      this.saveTheme();
      this.notifyListeners();
      this.applyThemeToDOM();
    }
  }

  getAvailableThemes(): string[] {
    return Array.from(this.themes.keys());
  }

  registerTheme(theme: ITheme): void {
    this.themes.set(theme.name, theme);
  }

  createCustomTheme(baseTheme: string, overrides: Partial<ITheme>): ITheme {
    const base = this.themes.get(baseTheme) || lightTheme;
    return {
      ...base,
      ...overrides,
      colors: { ...base.colors, ...overrides.colors },
      typography: { ...base.typography, ...overrides.typography },
      spacing: { ...base.spacing, ...overrides.spacing }
    };
  }

  subscribe(listener: (theme: ITheme) => void): () => void {
    this.listeners.add(listener);
    return () => this.listeners.delete(listener);
  }

  private loadSavedTheme(): void {
    const saved = localStorage.getItem('memorykeeper-theme');
    if (saved && this.themes.has(saved)) {
      this.currentTheme = saved;
    }
  }

  private saveTheme(): void {
    localStorage.setItem('memorykeeper-theme', this.currentTheme);
  }

  private notifyListeners(): void {
    const theme = this.getCurrentTheme();
    this.listeners.forEach(listener => listener(theme));
  }

  private applyThemeToDOM(): void {
    const theme = this.getCurrentTheme();
    const root = document.documentElement;

    // 应用CSS变量
    Object.entries(theme.colors).forEach(([key, value]) => {
      root.style.setProperty(`--color-${key}`, value);
    });

    Object.entries(theme.spacing).forEach(([key, value]) => {
      root.style.setProperty(`--spacing-${key}`, value);
    });
  }
}

export const themeManager = new ThemeManager();
```

### 步骤3: 实现国际化管理器 (30分钟)

#### 3.1 创建默认语言包
```typescript
// src/presentation/locales/zh-CN.ts
export const zhCN = {
  code: 'zh-CN',
  name: '简体中文',
  translations: {
    common: {
      save: '保存',
      cancel: '取消',
      delete: '删除',
      edit: '编辑',
      add: '添加',
      search: '搜索',
      loading: '加载中...',
      error: '错误',
      success: '成功',
      warning: '警告',
      info: '信息'
    },
    memory: {
      title: '记忆',
      add: '添加记忆',
      edit: '编辑记忆',
      delete: '删除记忆',
      search: '搜索记忆',
      noMemories: '暂无记忆',
      uploadImages: '上传图片',
      uploadVideos: '上传视频'
    },
    settings: {
      title: '设置',
      theme: '主题',
      language: '语言',
      storage: '存储',
      backup: '备份',
      export: '导出',
      import: '导入'
    }
  }
};
```

#### 3.2 实现国际化管理器
```typescript
// src/presentation/utils/I18nManager.ts
import { II18nManager, ILocale, ITranslation } from '../interfaces/II18nManager';
import { zhCN } from '../locales/zh-CN';

export class I18nManager implements II18nManager {
  private locales: Map<string, ILocale> = new Map();
  private currentLocale: string = 'zh-CN';
  private listeners: Set<(locale: string) => void> = new Set();

  constructor() {
    this.registerLocale(zhCN);
    this.loadSavedLocale();
  }

  getCurrentLocale(): string {
    return this.currentLocale;
  }

  setLocale(locale: string): void {
    if (this.locales.has(locale)) {
      this.currentLocale = locale;
      this.saveLocale();
      this.notifyListeners();
    }
  }

  getAvailableLocales(): string[] {
    return Array.from(this.locales.keys());
  }

  translate(key: string, params?: Record<string, any>): string {
    const locale = this.locales.get(this.currentLocale);
    if (!locale) return key;

    const translation = this.getNestedValue(locale.translations, key);
    if (typeof translation !== 'string') return key;

    return this.interpolate(translation, params);
  }

  registerLocale(locale: ILocale): void {
    this.locales.set(locale.code, locale);
  }

  formatDate(date: Date, format?: string): string {
    return new Intl.DateTimeFormat(this.currentLocale, {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      ...this.parseFormat(format)
    }).format(date);
  }

  formatNumber(number: number, options?: Intl.NumberFormatOptions): string {
    return new Intl.NumberFormat(this.currentLocale, options).format(number);
  }

  subscribe(listener: (locale: string) => void): () => void {
    this.listeners.add(listener);
    return () => this.listeners.delete(listener);
  }

  private getNestedValue(obj: ITranslation, path: string): any {
    return path.split('.').reduce((current, key) => {
      return current && typeof current === 'object' ? current[key] : undefined;
    }, obj);
  }

  private interpolate(template: string, params?: Record<string, any>): string {
    if (!params) return template;

    return template.replace(/\{\{(\w+)\}\}/g, (match, key) => {
      return params[key] !== undefined ? String(params[key]) : match;
    });
  }

  private parseFormat(format?: string): Intl.DateTimeFormatOptions {
    // 简单的格式解析，可以根据需要扩展
    if (!format) return {};

    const options: Intl.DateTimeFormatOptions = {};
    if (format.includes('HH:mm')) {
      options.hour = '2-digit';
      options.minute = '2-digit';
    }
    return options;
  }

  private loadSavedLocale(): void {
    const saved = localStorage.getItem('memorykeeper-locale');
    if (saved && this.locales.has(saved)) {
      this.currentLocale = saved;
    }
  }

  private saveLocale(): void {
    localStorage.setItem('memorykeeper-locale', this.currentLocale);
  }

  private notifyListeners(): void {
    this.listeners.forEach(listener => listener(this.currentLocale));
  }
}

export const i18nManager = new I18nManager();
```

### 步骤4: 实现响应式工具 (20分钟)

```typescript
// src/presentation/utils/ResponsiveUtils.ts
import { IResponsiveUtils, IBreakpoints } from '../interfaces/IResponsiveUtils';

export class ResponsiveUtils implements IResponsiveUtils {
  private breakpoints: IBreakpoints = {
    xs: 0,
    sm: 576,
    md: 768,
    lg: 992,
    xl: 1200
  };

  private listeners: Map<string, Set<(matches: boolean) => void>> = new Map();

  getBreakpoints(): IBreakpoints {
    return { ...this.breakpoints };
  }

  getCurrentBreakpoint(): string {
    const width = window.innerWidth;
    const breakpoints = Object.entries(this.breakpoints)
      .sort(([, a], [, b]) => b - a);

    for (const [name, minWidth] of breakpoints) {
      if (width >= minWidth) {
        return name;
      }
    }
    return 'xs';
  }

  isBreakpoint(breakpoint: string): boolean {
    return this.getCurrentBreakpoint() === breakpoint;
  }

  isMobile(): boolean {
    return window.innerWidth < this.breakpoints.md;
  }

  isTablet(): boolean {
    const width = window.innerWidth;
    return width >= this.breakpoints.md && width < this.breakpoints.lg;
  }

  isDesktop(): boolean {
    return window.innerWidth >= this.breakpoints.lg;
  }

  useMediaQuery(query: string): boolean {
    return window.matchMedia(query).matches;
  }

  subscribeToBreakpoint(breakpoint: string, callback: (matches: boolean) => void): () => void {
    const query = this.getMediaQuery(breakpoint);
    const mediaQuery = window.matchMedia(query);

    if (!this.listeners.has(query)) {
      this.listeners.set(query, new Set());
    }

    const listeners = this.listeners.get(query)!;
    listeners.add(callback);

    const handler = (e: MediaQueryListEvent) => {
      callback(e.matches);
    };

    mediaQuery.addEventListener('change', handler);
    callback(mediaQuery.matches);

    return () => {
      mediaQuery.removeEventListener('change', handler);
      listeners.delete(callback);
    };
  }

  private getMediaQuery(breakpoint: string): string {
    const minWidth = this.breakpoints[breakpoint as keyof IBreakpoints];
    return `(min-width: ${minWidth}px)`;
  }
}

export const responsiveUtils = new ResponsiveUtils();
```

### 步骤5: 实现动画工具 (25分钟)

```typescript
// src/presentation/utils/AnimationUtils.ts
export interface IAnimationOptions {
  duration?: number;
  easing?: string;
  delay?: number;
  fill?: 'none' | 'forwards' | 'backwards' | 'both';
}

export interface ITransition {
  property: string;
  duration: number;
  easing: string;
  delay?: number;
}

export class AnimationUtils {
  static fadeIn(element: HTMLElement, options: IAnimationOptions = {}): Animation {
    return element.animate([
      { opacity: 0 },
      { opacity: 1 }
    ], {
      duration: options.duration || 300,
      easing: options.easing || 'ease-out',
      delay: options.delay || 0,
      fill: options.fill || 'forwards'
    });
  }

  static fadeOut(element: HTMLElement, options: IAnimationOptions = {}): Animation {
    return element.animate([
      { opacity: 1 },
      { opacity: 0 }
    ], {
      duration: options.duration || 300,
      easing: options.easing || 'ease-out',
      delay: options.delay || 0,
      fill: options.fill || 'forwards'
    });
  }

  static slideIn(element: HTMLElement, direction: 'up' | 'down' | 'left' | 'right' = 'up', options: IAnimationOptions = {}): Animation {
    const transforms = {
      up: ['translateY(100%)', 'translateY(0)'],
      down: ['translateY(-100%)', 'translateY(0)'],
      left: ['translateX(100%)', 'translateX(0)'],
      right: ['translateX(-100%)', 'translateX(0)']
    };

    return element.animate([
      { transform: transforms[direction][0] },
      { transform: transforms[direction][1] }
    ], {
      duration: options.duration || 300,
      easing: options.easing || 'ease-out',
      delay: options.delay || 0,
      fill: options.fill || 'forwards'
    });
  }

  static scale(element: HTMLElement, from: number = 0, to: number = 1, options: IAnimationOptions = {}): Animation {
    return element.animate([
      { transform: `scale(${from})` },
      { transform: `scale(${to})` }
    ], {
      duration: options.duration || 300,
      easing: options.easing || 'ease-out',
      delay: options.delay || 0,
      fill: options.fill || 'forwards'
    });
  }

  static createTransition(transitions: ITransition[]): string {
    return transitions.map(t =>
      `${t.property} ${t.duration}ms ${t.easing}${t.delay ? ` ${t.delay}ms` : ''}`
    ).join(', ');
  }

  static async sequence(animations: (() => Animation)[]): Promise<void> {
    for (const createAnimation of animations) {
      const animation = createAnimation();
      await animation.finished;
    }
  }

  static parallel(animations: (() => Animation)[]): Promise<void[]> {
    const promises = animations.map(createAnimation => createAnimation().finished);
    return Promise.all(promises);
  }
}
```

## ✅ 验收标准

### 功能验收
- [ ] 主题管理器正常工作，支持主题切换
- [ ] 国际化管理器正常工作，支持语言切换
- [ ] 响应式工具正确检测屏幕尺寸
- [ ] 动画工具提供流畅的动画效果
- [ ] 所有工具函数都有TypeScript类型定义

### 质量验收
- [ ] TypeScript编译无错误
- [ ] 代码覆盖率 ≥ 80%
- [ ] ESLint检查通过
- [ ] 性能测试通过

### 集成验收
- [ ] 与核心组件集成正常
- [ ] 与页面组件集成正常
- [ ] 工具函数可以独立使用
- [ ] 支持按需导入

## 🔄 幂等性保证

### 执行前检查
```bash
# 检查文件是否已存在
if [ -f "src/presentation/utils/ThemeManager.ts" ]; then
  echo "ThemeManager already exists, checking for updates..."
fi

# 检查依赖是否满足
npm list react typescript
```

### 执行后验证
```bash
# 验证文件创建
ls -la src/presentation/utils/
ls -la src/presentation/themes/
ls -la src/presentation/locales/

# 验证TypeScript编译
npm run type-check

# 验证导入导出
node -e "
  const { themeManager } = require('./src/presentation/utils/ThemeManager.ts');
  const { i18nManager } = require('./src/presentation/utils/I18nManager.ts');
  console.log('Import test passed');
"
```

## 📝 完成确认

### 开发者确认
- [ ] 所有UI工具文件已创建
- [ ] TypeScript类型定义完整
- [ ] 工具函数测试通过
- [ ] 文档已更新

### 测试确认
- [ ] 单元测试通过
- [ ] 集成测试通过
- [ ] 性能测试通过
- [ ] 兼容性测试通过

### 部署确认
- [ ] 构建成功
- [ ] 无运行时错误
- [ ] 功能正常工作
- [ ] 性能指标达标

## 🔗 相关链接

- [Phase 5 概述](../README.md)
- [Task 5.1: 核心组件](./task-5.1-core-components.md)
- [Task 5.2: 页面组件](./task-5.2-page-components.md)
- [Task 5.4: 表现层测试](./task-5.4-presentation-layer-test.md)
- [表现层接口文档](../interfaces/05-presentation-interfaces.md)

## 📋 任务检查清单

- [ ] 任务开始前环境检查
- [ ] UI工具接口定义完成
- [ ] 主题管理器实现完成
- [ ] 国际化管理器实现完成
- [ ] 响应式工具实现完成
- [ ] 动画工具实现完成
- [ ] TypeScript编译检查
- [ ] 单元测试编写和执行
- [ ] 集成测试验证
- [ ] 文档更新
- [ ] 代码审查
- [ ] 任务完成确认

---

**任务状态**: ⏳ 待执行
**最后更新**: 2024-12-19
**下一个任务**: [Task 5.4: 创建表现层测试页面](./task-5.4-presentation-layer-test.md)