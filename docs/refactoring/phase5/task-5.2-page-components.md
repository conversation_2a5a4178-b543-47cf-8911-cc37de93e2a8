# 任务5.2：重构页面组件

## 📋 任务概述

**任务ID**: Task-5.2  
**任务名称**: 重构页面组件  
**所属阶段**: 阶段5 - 表现层重构  
**预计时间**: 4小时  
**依赖任务**: Task-5.1 (重构核心组件)  
**后续任务**: Task-5.3 (重构UI工具)  

## 🎯 任务目标

重构页面级组件，实现页面功能模块化，包括内存页面、设置页面、搜索页面和统计页面。

## 🛠️ 实施步骤

### 步骤1：定义页面接口
**文件**: `src/presentation/interfaces/IPage.ts`

```typescript
import { IComponent } from './IComponent';

export interface IPage extends IComponent {
  // 页面生命周期
  onPageEnter(params?: PageParams): Promise<void>;
  onPageLeave(): Promise<void>;
  onPageRefresh(): Promise<void>;
  
  // 页面状态
  getPageTitle(): string;
  getPageId(): string;
  isPageActive(): boolean;
  
  // 页面导航
  canNavigateAway(): Promise<boolean>;
  getUnsavedChanges(): UnsavedChange[];
  
  // 页面数据
  loadData(params?: PageParams): Promise<void>;
  saveData(): Promise<void>;
  resetData(): void;
}

export interface PageParams {
  [key: string]: any;
}

export interface UnsavedChange {
  field: string;
  oldValue: any;
  newValue: any;
  timestamp: Date;
}

export interface PageConfig {
  id: string;
  title: string;
  route: string;
  requireAuth?: boolean;
  permissions?: string[];
  layout?: string;
  metadata?: Record<string, any>;
}

export abstract class BasePage extends IComponent implements IPage {
  protected pageConfig: PageConfig;
  protected isActive = false;
  protected unsavedChanges: UnsavedChange[] = [];

  constructor(config: PageConfig) {
    super();
    this.pageConfig = config;
  }

  async onPageEnter(params?: PageParams): Promise<void> {
    this.isActive = true;
    await this.loadData(params);
    this.onPageEntered();
  }

  async onPageLeave(): Promise<void> {
    const canLeave = await this.canNavigateAway();
    if (canLeave) {
      this.isActive = false;
      this.onPageLeft();
    }
  }

  async onPageRefresh(): Promise<void> {
    await this.loadData();
    this.update();
  }

  getPageTitle(): string {
    return this.pageConfig.title;
  }

  getPageId(): string {
    return this.pageConfig.id;
  }

  isPageActive(): boolean {
    return this.isActive;
  }

  async canNavigateAway(): Promise<boolean> {
    if (this.unsavedChanges.length > 0) {
      return confirm('您有未保存的更改，确定要离开吗？');
    }
    return true;
  }

  getUnsavedChanges(): UnsavedChange[] {
    return [...this.unsavedChanges];
  }

  protected addUnsavedChange(field: string, oldValue: any, newValue: any): void {
    const existingIndex = this.unsavedChanges.findIndex(c => c.field === field);
    const change: UnsavedChange = {
      field,
      oldValue,
      newValue,
      timestamp: new Date()
    };

    if (existingIndex > -1) {
      this.unsavedChanges[existingIndex] = change;
    } else {
      this.unsavedChanges.push(change);
    }
  }

  protected clearUnsavedChanges(): void {
    this.unsavedChanges = [];
  }

  // 抽象方法
  abstract loadData(params?: PageParams): Promise<void>;
  abstract saveData(): Promise<void>;
  abstract resetData(): void;

  // 生命周期钩子
  protected onPageEntered(): void {}
  protected onPageLeft(): void {}
}
```

### 步骤2：实现内存页面组件
**文件**: `src/presentation/pages/MemoryPage.ts`

```typescript
import { BasePage, PageConfig, PageParams } from '../interfaces/IPage';
import { ComponentFactory } from '../components/core/ComponentFactory';
import { Button } from '../components/core/Button';
import { Input } from '../components/core/Input';
import { Modal } from '../components/core/Modal';

export class MemoryPage extends BasePage {
  private memories: any[] = [];
  private searchInput?: Input;
  private addButton?: Button;
  private memoryModal?: Modal;
  private currentMemory?: any;

  constructor() {
    super({
      id: 'memory-page',
      title: '内存管理',
      route: '/memories',
      requireAuth: false
    });
  }

  protected createElement(): HTMLElement {
    const page = document.createElement('div');
    page.className = 'memory-page';
    page.innerHTML = `
      <div class="page-header">
        <h1>内存管理</h1>
        <div class="page-actions">
          <div class="search-container"></div>
          <div class="add-button-container"></div>
        </div>
      </div>
      <div class="page-content">
        <div class="memory-list"></div>
      </div>
      <div class="memory-modal-container"></div>
    `;

    this.initializeComponents();
    this.bindEvents();

    return page;
  }

  protected render(): void {
    this.renderMemoryList();
  }

  async loadData(params?: PageParams): Promise<void> {
    try {
      // 模拟加载内存数据
      this.memories = [
        {
          id: '1',
          title: '示例内存1',
          content: '这是第一个示例内存',
          type: 'text',
          tags: ['示例', '测试'],
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          id: '2',
          title: '示例内存2',
          content: '这是第二个示例内存',
          type: 'text',
          tags: ['示例'],
          createdAt: new Date(),
          updatedAt: new Date()
        }
      ];

      this.render();
    } catch (error) {
      console.error('加载内存数据失败:', error);
    }
  }

  async saveData(): Promise<void> {
    try {
      // 模拟保存数据
      console.log('保存内存数据');
      this.clearUnsavedChanges();
    } catch (error) {
      console.error('保存内存数据失败:', error);
    }
  }

  resetData(): void {
    this.memories = [];
    this.currentMemory = undefined;
    this.render();
  }

  private initializeComponents(): void {
    if (!this.state.element) return;

    // 初始化搜索输入框
    this.searchInput = ComponentFactory.createInput({
      placeholder: '搜索内存...',
      onChange: (value) => this.handleSearch(value)
    });

    const searchContainer = this.state.element.querySelector('.search-container');
    if (searchContainer) {
      this.searchInput.mount(searchContainer as HTMLElement);
    }

    // 初始化添加按钮
    this.addButton = ComponentFactory.createButton({
      text: '添加内存',
      type: 'primary',
      onClick: () => this.handleAddMemory()
    });

    const addButtonContainer = this.state.element.querySelector('.add-button-container');
    if (addButtonContainer) {
      this.addButton.mount(addButtonContainer as HTMLElement);
    }

    // 初始化内存模态框
    this.memoryModal = ComponentFactory.createModal({
      title: '编辑内存',
      width: '600px',
      onClose: () => this.handleModalClose()
    });

    const modalContainer = this.state.element.querySelector('.memory-modal-container');
    if (modalContainer) {
      this.memoryModal.mount(modalContainer as HTMLElement);
    }
  }

  private bindEvents(): void {
    // 页面级事件绑定
  }

  private renderMemoryList(): void {
    if (!this.state.element) return;

    const listContainer = this.state.element.querySelector('.memory-list');
    if (!listContainer) return;

    listContainer.innerHTML = '';

    if (this.memories.length === 0) {
      listContainer.innerHTML = '<div class="empty-state">暂无内存数据</div>';
      return;
    }

    this.memories.forEach(memory => {
      const memoryItem = this.createMemoryItem(memory);
      listContainer.appendChild(memoryItem);
    });
  }

  private createMemoryItem(memory: any): HTMLElement {
    const item = document.createElement('div');
    item.className = 'memory-item';
    item.innerHTML = `
      <div class="memory-header">
        <h3 class="memory-title">${memory.title}</h3>
        <div class="memory-actions">
          <button class="btn-edit" data-id="${memory.id}">编辑</button>
          <button class="btn-delete" data-id="${memory.id}">删除</button>
        </div>
      </div>
      <div class="memory-content">${memory.content}</div>
      <div class="memory-meta">
        <span class="memory-type">${memory.type}</span>
        <span class="memory-date">${memory.createdAt.toLocaleDateString()}</span>
        <div class="memory-tags">
          ${memory.tags.map((tag: string) => `<span class="tag">${tag}</span>`).join('')}
        </div>
      </div>
    `;

    // 绑定事件
    const editBtn = item.querySelector('.btn-edit');
    const deleteBtn = item.querySelector('.btn-delete');

    if (editBtn) {
      editBtn.addEventListener('click', () => this.handleEditMemory(memory));
    }

    if (deleteBtn) {
      deleteBtn.addEventListener('click', () => this.handleDeleteMemory(memory));
    }

    return item;
  }

  private handleSearch(query: string): void {
    // 实现搜索逻辑
    console.log('搜索:', query);
  }

  private handleAddMemory(): void {
    this.currentMemory = {
      id: '',
      title: '',
      content: '',
      type: 'text',
      tags: [],
      createdAt: new Date(),
      updatedAt: new Date()
    };

    this.showMemoryModal();
  }

  private handleEditMemory(memory: any): void {
    this.currentMemory = { ...memory };
    this.showMemoryModal();
  }

  private handleDeleteMemory(memory: any): void {
    if (confirm(`确定要删除内存"${memory.title}"吗？`)) {
      const index = this.memories.findIndex(m => m.id === memory.id);
      if (index > -1) {
        this.memories.splice(index, 1);
        this.render();
        this.addUnsavedChange('memories', null, this.memories);
      }
    }
  }

  private showMemoryModal(): void {
    if (!this.memoryModal || !this.currentMemory) return;

    // 创建模态框内容
    const content = document.createElement('div');
    content.innerHTML = `
      <div class="memory-form">
        <div class="form-group">
          <label>标题</label>
          <input type="text" class="title-input" value="${this.currentMemory.title}" />
        </div>
        <div class="form-group">
          <label>内容</label>
          <textarea class="content-input" rows="6">${this.currentMemory.content}</textarea>
        </div>
        <div class="form-group">
          <label>标签</label>
          <input type="text" class="tags-input" value="${this.currentMemory.tags.join(', ')}" placeholder="用逗号分隔" />
        </div>
      </div>
    `;

    // 创建模态框页脚
    const footer = document.createElement('div');
    footer.innerHTML = `
      <button class="btn btn-secondary modal-cancel">取消</button>
      <button class="btn btn-primary modal-save">保存</button>
    `;

    // 绑定页脚事件
    const cancelBtn = footer.querySelector('.modal-cancel');
    const saveBtn = footer.querySelector('.modal-save');

    if (cancelBtn) {
      cancelBtn.addEventListener('click', () => this.memoryModal?.close());
    }

    if (saveBtn) {
      saveBtn.addEventListener('click', () => this.handleSaveMemory(content));
    }

    this.memoryModal.setContent(content);
    this.memoryModal.setProps({ footer });
    this.memoryModal.open();
  }

  private handleSaveMemory(formContent: HTMLElement): void {
    if (!this.currentMemory) return;

    const titleInput = formContent.querySelector('.title-input') as HTMLInputElement;
    const contentInput = formContent.querySelector('.content-input') as HTMLTextAreaElement;
    const tagsInput = formContent.querySelector('.tags-input') as HTMLInputElement;

    if (!titleInput.value.trim()) {
      alert('请输入标题');
      return;
    }

    this.currentMemory.title = titleInput.value.trim();
    this.currentMemory.content = contentInput.value.trim();
    this.currentMemory.tags = tagsInput.value.split(',').map(tag => tag.trim()).filter(tag => tag);
    this.currentMemory.updatedAt = new Date();

    if (!this.currentMemory.id) {
      // 新增
      this.currentMemory.id = Date.now().toString();
      this.memories.unshift(this.currentMemory);
    } else {
      // 编辑
      const index = this.memories.findIndex(m => m.id === this.currentMemory.id);
      if (index > -1) {
        this.memories[index] = this.currentMemory;
      }
    }

    this.render();
    this.addUnsavedChange('memories', null, this.memories);
    this.memoryModal?.close();
  }

  private handleModalClose(): void {
    this.currentMemory = undefined;
  }

  protected onPageEntered(): void {
    console.log('进入内存页面');
  }

  protected onPageLeft(): void {
    console.log('离开内存页面');
  }
}
```

### 步骤3：实现设置页面组件
**文件**: `src/presentation/pages/SettingsPage.ts`

```typescript
import { BasePage, PageConfig, PageParams } from '../interfaces/IPage';
import { ComponentFactory } from '../components/core/ComponentFactory';
import { Input } from '../components/core/Input';
import { Button } from '../components/core/Button';

export class SettingsPage extends BasePage {
  private settings: Record<string, any> = {};
  private settingsInputs: Map<string, Input> = new Map();
  private saveButton?: Button;
  private resetButton?: Button;

  constructor() {
    super({
      id: 'settings-page',
      title: '设置',
      route: '/settings',
      requireAuth: false
    });
  }

  protected createElement(): HTMLElement {
    const page = document.createElement('div');
    page.className = 'settings-page';
    page.innerHTML = `
      <div class="page-header">
        <h1>设置</h1>
      </div>
      <div class="page-content">
        <div class="settings-sections">
          <div class="settings-section" data-section="general">
            <h2>常规设置</h2>
            <div class="settings-form"></div>
          </div>
          <div class="settings-section" data-section="storage">
            <h2>存储设置</h2>
            <div class="settings-form"></div>
          </div>
          <div class="settings-section" data-section="security">
            <h2>安全设置</h2>
            <div class="settings-form"></div>
          </div>
        </div>
        <div class="settings-actions">
          <div class="save-button-container"></div>
          <div class="reset-button-container"></div>
        </div>
      </div>
    `;

    this.initializeComponents();
    this.bindEvents();

    return page;
  }

  protected render(): void {
    this.renderSettingsForms();
  }

  async loadData(params?: PageParams): Promise<void> {
    try {
      // 模拟加载设置数据
      this.settings = {
        // 常规设置
        appName: 'MemoryKeeper',
        language: 'zh-CN',
        theme: 'light',
        autoSave: true,
        
        // 存储设置
        storageProvider: 'localStorage',
        backupEnabled: true,
        backupInterval: '24',
        
        // 安全设置
        encryptionEnabled: true,
        passwordRequired: false,
        sessionTimeout: '30'
      };

      this.render();
    } catch (error) {
      console.error('加载设置数据失败:', error);
    }
  }

  async saveData(): Promise<void> {
    try {
      // 收集所有输入框的值
      const newSettings: Record<string, any> = {};
      
      this.settingsInputs.forEach((input, key) => {
        newSettings[key] = input.getValue();
      });

      // 模拟保存设置
      this.settings = { ...this.settings, ...newSettings };
      console.log('保存设置:', this.settings);
      
      this.clearUnsavedChanges();
      alert('设置已保存');
    } catch (error) {
      console.error('保存设置失败:', error);
      alert('保存设置失败');
    }
  }

  resetData(): void {
    // 重置为默认设置
    this.loadData();
    this.clearUnsavedChanges();
  }

  private initializeComponents(): void {
    if (!this.state.element) return;

    // 初始化保存按钮
    this.saveButton = ComponentFactory.createButton({
      text: '保存设置',
      type: 'primary',
      onClick: () => this.saveData()
    });

    const saveButtonContainer = this.state.element.querySelector('.save-button-container');
    if (saveButtonContainer) {
      this.saveButton.mount(saveButtonContainer as HTMLElement);
    }

    // 初始化重置按钮
    this.resetButton = ComponentFactory.createButton({
      text: '重置设置',
      type: 'secondary',
      onClick: () => this.handleReset()
    });

    const resetButtonContainer = this.state.element.querySelector('.reset-button-container');
    if (resetButtonContainer) {
      this.resetButton.mount(resetButtonContainer as HTMLElement);
    }
  }

  private bindEvents(): void {
    // 页面级事件绑定
  }

  private renderSettingsForms(): void {
    if (!this.state.element) return;

    const sections = [
      {
        name: 'general',
        fields: [
          { key: 'appName', label: '应用名称', type: 'text' },
          { key: 'language', label: '语言', type: 'text' },
          { key: 'theme', label: '主题', type: 'text' },
          { key: 'autoSave', label: '自动保存', type: 'text' }
        ]
      },
      {
        name: 'storage',
        fields: [
          { key: 'storageProvider', label: '存储提供者', type: 'text' },
          { key: 'backupEnabled', label: '启用备份', type: 'text' },
          { key: 'backupInterval', label: '备份间隔(小时)', type: 'number' }
        ]
      },
      {
        name: 'security',
        fields: [
          { key: 'encryptionEnabled', label: '启用加密', type: 'text' },
          { key: 'passwordRequired', label: '需要密码', type: 'text' },
          { key: 'sessionTimeout', label: '会话超时(分钟)', type: 'number' }
        ]
      }
    ];

    sections.forEach(section => {
      const sectionElement = this.state.element?.querySelector(`[data-section="${section.name}"] .settings-form`);
      if (!sectionElement) return;

      sectionElement.innerHTML = '';

      section.fields.forEach(field => {
        const fieldContainer = document.createElement('div');
        fieldContainer.className = 'field-container';

        const input = ComponentFactory.createInput({
          label: field.label,
          type: field.type as any,
          value: String(this.settings[field.key] || ''),
          onChange: (value) => this.handleFieldChange(field.key, value)
        });

        input.mount(fieldContainer);
        sectionElement.appendChild(fieldContainer);

        this.settingsInputs.set(field.key, input);
      });
    });
  }

  private handleFieldChange(key: string, value: string): void {
    const oldValue = this.settings[key];
    this.addUnsavedChange(key, oldValue, value);
  }

  private handleReset(): void {
    if (confirm('确定要重置所有设置吗？')) {
      this.resetData();
    }
  }

  protected onPageEntered(): void {
    console.log('进入设置页面');
  }

  protected onPageLeft(): void {
    console.log('离开设置页面');
  }
}
```

### 步骤4：创建页面路由器
**文件**: `src/presentation/pages/PageRouter.ts`

```typescript
import { IPage, PageConfig } from '../interfaces/IPage';
import { MemoryPage } from './MemoryPage';
import { SettingsPage } from './SettingsPage';

export class PageRouter {
  private pages = new Map<string, IPage>();
  private currentPage?: IPage;
  private container?: HTMLElement;

  constructor(container: HTMLElement) {
    this.container = container;
    this.initializePages();
    this.bindEvents();
  }

  private initializePages(): void {
    // 注册页面
    this.registerPage(new MemoryPage());
    this.registerPage(new SettingsPage());
  }

  private registerPage(page: IPage): void {
    this.pages.set(page.getPageId(), page);
  }

  private bindEvents(): void {
    // 监听浏览器前进后退
    window.addEventListener('popstate', (event) => {
      const path = window.location.pathname;
      this.navigateToPath(path);
    });
  }

  async navigateTo(pageId: string, params?: any): Promise<boolean> {
    const page = this.pages.get(pageId);
    if (!page) {
      console.error(`页面不存在: ${pageId}`);
      return false;
    }

    // 检查当前页面是否可以离开
    if (this.currentPage) {
      const canLeave = await this.currentPage.canNavigateAway();
      if (!canLeave) {
        return false;
      }

      await this.currentPage.onPageLeave();
      this.currentPage.unmount();
    }

    // 挂载新页面
    if (this.container) {
      page.mount(this.container);
      await page.onPageEnter(params);
      this.currentPage = page;

      // 更新浏览器历史
      const route = this.getPageRoute(pageId);
      if (route && window.location.pathname !== route) {
        window.history.pushState({ pageId, params }, page.getPageTitle(), route);
      }

      // 更新页面标题
      document.title = page.getPageTitle();
    }

    return true;
  }

  async navigateToPath(path: string): Promise<boolean> {
    const pageId = this.getPageIdByPath(path);
    if (pageId) {
      return await this.navigateTo(pageId);
    }
    return false;
  }

  getCurrentPage(): IPage | undefined {
    return this.currentPage;
  }

  getPages(): IPage[] {
    return Array.from(this.pages.values());
  }

  private getPageRoute(pageId: string): string | undefined {
    const page = this.pages.get(pageId);
    if (page && 'pageConfig' in page) {
      return (page as any).pageConfig.route;
    }
    return undefined;
  }

  private getPageIdByPath(path: string): string | undefined {
    for (const page of this.pages.values()) {
      if ('pageConfig' in page) {
        const config = (page as any).pageConfig as PageConfig;
        if (config.route === path) {
          return page.getPageId();
        }
      }
    }
    return undefined;
  }
}
```

## ✅ 验收标准

### 功能验收
- [ ] IPage接口定义完整
- [ ] BasePage基类实现完整
- [ ] MemoryPage页面功能完整
- [ ] SettingsPage页面功能完整
- [ ] PageRouter路由功能正常

### 质量验收
- [ ] TypeScript编译无错误
- [ ] 页面生命周期管理正确
- [ ] 页面导航功能正常
- [ ] 数据保存和加载正确

### 用户体验验收
- [ ] 页面切换流畅
- [ ] 未保存提醒功能正常
- [ ] 表单验证有效

## 🔄 幂等性保证

### 执行前检查
```bash
#!/bin/bash
echo "🔍 检查任务5.2执行状态..."

if [ ! -f "src/presentation/components/core/Button.ts" ]; then
  echo "❌ 依赖任务Task-5.1未完成，请先执行Task-5.1"
  exit 1
fi

if [ -f "src/presentation/pages/MemoryPage.ts" ]; then
  echo "⚠️  页面组件已存在，任务可能已完成"
  read -p "是否继续？(y/N): " confirm
  if [ "$confirm" != "y" ]; then
    exit 1
  fi
fi

echo "✅ 前置检查完成，可以执行任务5.2"
```

### 执行后验证
```bash
#!/bin/bash
echo "🔍 验证任务5.2执行结果..."

files=(
  "src/presentation/interfaces/IPage.ts"
  "src/presentation/pages/MemoryPage.ts"
  "src/presentation/pages/SettingsPage.ts"
  "src/presentation/pages/PageRouter.ts"
)

all_created=true
for file in "${files[@]}"; do
  if [ -f "$file" ]; then
    echo "✅ $file 创建成功"
  else
    echo "❌ $file 创建失败"
    all_created=false
  fi
done

if npm run type-check > /dev/null 2>&1; then
  echo "✅ TypeScript编译检查通过"
else
  echo "❌ TypeScript编译检查失败"
  all_created=false
fi

if [ "$all_created" = true ]; then
  echo "🎉 任务5.2执行成功！"
  exit 0
else
  echo "💥 任务5.2执行失败，请检查错误信息"
  exit 1
fi
```

## 📝 完成确认

任务完成后，请在检查清单中标记：
- [ ] 任务5.2已完成
- [ ] 页面组件接口定义完成
- [ ] 内存页面组件实现完成
- [ ] 设置页面组件实现完成
- [ ] 页面路由器创建完成
- [ ] 准备执行任务5.3

## 🔗 相关链接

- [上一个任务：Task-5.1 重构核心组件](./task-5.1-core-components.md)
- [下一个任务：Task-5.3 重构UI工具](./task-5.3-ui-tools.md)
- [重构检查清单](../00-refactoring-checklist.md)
