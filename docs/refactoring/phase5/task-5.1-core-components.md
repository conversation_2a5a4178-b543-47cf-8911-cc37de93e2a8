# 任务5.1：重构核心组件

## 📋 任务概述

**任务ID**: Task-5.1  
**任务名称**: 重构核心组件  
**所属阶段**: 阶段5 - 表现层重构  
**预计时间**: 4小时  
**依赖任务**: Phase-4 (应用层重构完成)  
**后续任务**: Task-5.2 (重构页面组件)  

## 🎯 任务目标

重构核心UI组件，提供可复用的组件库，包括基础组件、表单组件、数据展示组件和交互组件。

## 🛠️ 实施步骤

### 步骤1：定义组件接口
**文件**: `src/presentation/interfaces/IComponent.ts`

```typescript
export interface IComponent {
  // 组件生命周期
  mount(container: HTMLElement): void;
  unmount(): void;
  update(props?: ComponentProps): void;
  
  // 组件状态
  getElement(): HTMLElement | null;
  isVisible(): boolean;
  setVisible(visible: boolean): void;
  
  // 事件处理
  addEventListener(event: string, handler: EventListener): void;
  removeEventListener(event: string, handler: EventListener): void;
  emit(event: string, data?: any): void;
  
  // 组件属性
  setProps(props: ComponentProps): void;
  getProps(): ComponentProps;
  
  // 组件样式
  addClass(className: string): void;
  removeClass(className: string): void;
  toggleClass(className: string): void;
  
  // 组件销毁
  destroy(): void;
}

export interface ComponentProps {
  id?: string;
  className?: string;
  style?: Partial<CSSStyleDeclaration>;
  data?: Record<string, any>;
  children?: IComponent[];
  [key: string]: any;
}

export interface ComponentConfig {
  tag?: string;
  className?: string;
  attributes?: Record<string, string>;
  events?: Record<string, EventListener>;
  template?: string;
  styles?: string;
}

export interface ComponentState {
  mounted: boolean;
  visible: boolean;
  props: ComponentProps;
  element: HTMLElement | null;
}

export abstract class BaseComponent implements IComponent {
  protected state: ComponentState;
  protected config: ComponentConfig;
  protected eventListeners = new Map<string, Set<EventListener>>();

  constructor(config: ComponentConfig = {}) {
    this.config = config;
    this.state = {
      mounted: false,
      visible: true,
      props: {},
      element: null
    };
  }

  mount(container: HTMLElement): void {
    if (this.state.mounted) {
      console.warn('组件已挂载');
      return;
    }

    this.state.element = this.createElement();
    container.appendChild(this.state.element);
    this.state.mounted = true;
    
    this.onMounted();
  }

  unmount(): void {
    if (!this.state.mounted || !this.state.element) {
      return;
    }

    this.onBeforeUnmount();
    
    if (this.state.element.parentNode) {
      this.state.element.parentNode.removeChild(this.state.element);
    }
    
    this.state.mounted = false;
    this.state.element = null;
    
    this.onUnmounted();
  }

  update(props?: ComponentProps): void {
    if (props) {
      this.setProps(props);
    }
    
    if (this.state.element) {
      this.render();
    }
  }

  getElement(): HTMLElement | null {
    return this.state.element;
  }

  isVisible(): boolean {
    return this.state.visible;
  }

  setVisible(visible: boolean): void {
    this.state.visible = visible;
    if (this.state.element) {
      this.state.element.style.display = visible ? '' : 'none';
    }
  }

  addEventListener(event: string, handler: EventListener): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, new Set());
    }
    
    this.eventListeners.get(event)!.add(handler);
    
    if (this.state.element) {
      this.state.element.addEventListener(event, handler);
    }
  }

  removeEventListener(event: string, handler: EventListener): void {
    const handlers = this.eventListeners.get(event);
    if (handlers) {
      handlers.delete(handler);
      
      if (this.state.element) {
        this.state.element.removeEventListener(event, handler);
      }
    }
  }

  emit(event: string, data?: any): void {
    if (this.state.element) {
      const customEvent = new CustomEvent(event, { detail: data });
      this.state.element.dispatchEvent(customEvent);
    }
  }

  setProps(props: ComponentProps): void {
    this.state.props = { ...this.state.props, ...props };
    this.onPropsChanged();
  }

  getProps(): ComponentProps {
    return { ...this.state.props };
  }

  addClass(className: string): void {
    if (this.state.element) {
      this.state.element.classList.add(className);
    }
  }

  removeClass(className: string): void {
    if (this.state.element) {
      this.state.element.classList.remove(className);
    }
  }

  toggleClass(className: string): void {
    if (this.state.element) {
      this.state.element.classList.toggle(className);
    }
  }

  destroy(): void {
    this.unmount();
    this.eventListeners.clear();
    this.onDestroyed();
  }

  // 抽象方法
  protected abstract createElement(): HTMLElement;
  protected abstract render(): void;

  // 生命周期钩子
  protected onMounted(): void {}
  protected onBeforeUnmount(): void {}
  protected onUnmounted(): void {}
  protected onPropsChanged(): void {}
  protected onDestroyed(): void {}
}
```

### 步骤2：实现基础组件
**文件**: `src/presentation/components/core/Button.ts`

```typescript
import { BaseComponent, ComponentConfig, ComponentProps } from '../../interfaces/IComponent';

export interface ButtonProps extends ComponentProps {
  text?: string;
  type?: 'primary' | 'secondary' | 'success' | 'warning' | 'danger';
  size?: 'small' | 'medium' | 'large';
  disabled?: boolean;
  loading?: boolean;
  icon?: string;
  onClick?: (event: MouseEvent) => void;
}

export class Button extends BaseComponent {
  constructor(config: ComponentConfig = {}) {
    super({
      tag: 'button',
      className: 'btn',
      ...config
    });
  }

  protected createElement(): HTMLElement {
    const button = document.createElement(this.config.tag || 'button');
    button.className = this.config.className || 'btn';
    
    this.render();
    this.bindEvents();
    
    return button;
  }

  protected render(): void {
    if (!this.state.element) return;

    const props = this.state.props as ButtonProps;
    const button = this.state.element as HTMLButtonElement;

    // 设置文本
    button.textContent = props.text || '';

    // 设置类型样式
    button.className = `btn ${props.type ? `btn-${props.type}` : 'btn-primary'}`;
    
    // 设置大小样式
    if (props.size) {
      button.classList.add(`btn-${props.size}`);
    }

    // 设置状态
    button.disabled = props.disabled || props.loading || false;
    
    if (props.loading) {
      button.classList.add('btn-loading');
    } else {
      button.classList.remove('btn-loading');
    }

    // 设置图标
    if (props.icon) {
      const icon = document.createElement('i');
      icon.className = `icon ${props.icon}`;
      button.insertBefore(icon, button.firstChild);
    }

    // 应用自定义样式
    if (props.style) {
      Object.assign(button.style, props.style);
    }
  }

  private bindEvents(): void {
    if (!this.state.element) return;

    this.state.element.addEventListener('click', (event: Event) => {
      const props = this.state.props as ButtonProps;
      if (props.onClick && !props.disabled && !props.loading) {
        props.onClick(event as MouseEvent);
      }
    });
  }

  // 公共方法
  setLoading(loading: boolean): void {
    this.setProps({ loading });
  }

  setDisabled(disabled: boolean): void {
    this.setProps({ disabled });
  }

  setText(text: string): void {
    this.setProps({ text });
  }
}
```

### 步骤3：实现输入组件
**文件**: `src/presentation/components/core/Input.ts`

```typescript
import { BaseComponent, ComponentConfig, ComponentProps } from '../../interfaces/IComponent';

export interface InputProps extends ComponentProps {
  type?: 'text' | 'password' | 'email' | 'number' | 'search';
  placeholder?: string;
  value?: string;
  disabled?: boolean;
  readonly?: boolean;
  required?: boolean;
  maxLength?: number;
  pattern?: string;
  label?: string;
  error?: string;
  onChange?: (value: string, event: Event) => void;
  onFocus?: (event: FocusEvent) => void;
  onBlur?: (event: FocusEvent) => void;
}

export class Input extends BaseComponent {
  constructor(config: ComponentConfig = {}) {
    super({
      tag: 'div',
      className: 'input-group',
      ...config
    });
  }

  protected createElement(): HTMLElement {
    const container = document.createElement('div');
    container.className = 'input-group';
    
    this.render();
    this.bindEvents();
    
    return container;
  }

  protected render(): void {
    if (!this.state.element) return;

    const props = this.state.props as InputProps;
    const container = this.state.element;

    // 清空容器
    container.innerHTML = '';

    // 创建标签
    if (props.label) {
      const label = document.createElement('label');
      label.className = 'input-label';
      label.textContent = props.label;
      if (props.required) {
        label.classList.add('required');
      }
      container.appendChild(label);
    }

    // 创建输入框
    const input = document.createElement('input');
    input.className = 'input-field';
    input.type = props.type || 'text';
    input.placeholder = props.placeholder || '';
    input.value = props.value || '';
    input.disabled = props.disabled || false;
    input.readOnly = props.readonly || false;
    input.required = props.required || false;

    if (props.maxLength) {
      input.maxLength = props.maxLength;
    }

    if (props.pattern) {
      input.pattern = props.pattern;
    }

    container.appendChild(input);

    // 创建错误信息
    if (props.error) {
      const error = document.createElement('div');
      error.className = 'input-error';
      error.textContent = props.error;
      container.appendChild(error);
      container.classList.add('has-error');
    } else {
      container.classList.remove('has-error');
    }

    // 应用自定义样式
    if (props.style) {
      Object.assign(container.style, props.style);
    }
  }

  private bindEvents(): void {
    if (!this.state.element) return;

    const input = this.state.element.querySelector('.input-field') as HTMLInputElement;
    if (!input) return;

    input.addEventListener('input', (event: Event) => {
      const props = this.state.props as InputProps;
      if (props.onChange) {
        props.onChange(input.value, event);
      }
    });

    input.addEventListener('focus', (event: FocusEvent) => {
      const props = this.state.props as InputProps;
      this.state.element?.classList.add('focused');
      if (props.onFocus) {
        props.onFocus(event);
      }
    });

    input.addEventListener('blur', (event: FocusEvent) => {
      const props = this.state.props as InputProps;
      this.state.element?.classList.remove('focused');
      if (props.onBlur) {
        props.onBlur(event);
      }
    });
  }

  // 公共方法
  getValue(): string {
    const input = this.state.element?.querySelector('.input-field') as HTMLInputElement;
    return input ? input.value : '';
  }

  setValue(value: string): void {
    this.setProps({ value });
  }

  focus(): void {
    const input = this.state.element?.querySelector('.input-field') as HTMLInputElement;
    if (input) {
      input.focus();
    }
  }

  blur(): void {
    const input = this.state.element?.querySelector('.input-field') as HTMLInputElement;
    if (input) {
      input.blur();
    }
  }

  setError(error: string): void {
    this.setProps({ error });
  }

  clearError(): void {
    this.setProps({ error: undefined });
  }
}
```

### 步骤4：实现模态框组件
**文件**: `src/presentation/components/core/Modal.ts`

```typescript
import { BaseComponent, ComponentConfig, ComponentProps } from '../../interfaces/IComponent';

export interface ModalProps extends ComponentProps {
  title?: string;
  content?: string | HTMLElement;
  width?: string;
  height?: string;
  closable?: boolean;
  maskClosable?: boolean;
  footer?: HTMLElement;
  onOpen?: () => void;
  onClose?: () => void;
  onConfirm?: () => void;
  onCancel?: () => void;
}

export class Modal extends BaseComponent {
  private isOpen = false;

  constructor(config: ComponentConfig = {}) {
    super({
      tag: 'div',
      className: 'modal',
      ...config
    });
  }

  protected createElement(): HTMLElement {
    const modal = document.createElement('div');
    modal.className = 'modal';
    modal.style.display = 'none';
    
    this.render();
    this.bindEvents();
    
    return modal;
  }

  protected render(): void {
    if (!this.state.element) return;

    const props = this.state.props as ModalProps;
    const modal = this.state.element;

    modal.innerHTML = `
      <div class="modal-mask"></div>
      <div class="modal-container">
        <div class="modal-content">
          ${props.title ? `<div class="modal-header">
            <h3 class="modal-title">${props.title}</h3>
            ${props.closable !== false ? '<button class="modal-close">&times;</button>' : ''}
          </div>` : ''}
          <div class="modal-body"></div>
          ${props.footer ? '<div class="modal-footer"></div>' : ''}
        </div>
      </div>
    `;

    // 设置内容
    const body = modal.querySelector('.modal-body') as HTMLElement;
    if (props.content) {
      if (typeof props.content === 'string') {
        body.innerHTML = props.content;
      } else {
        body.appendChild(props.content);
      }
    }

    // 设置页脚
    if (props.footer) {
      const footer = modal.querySelector('.modal-footer') as HTMLElement;
      footer.appendChild(props.footer);
    }

    // 设置尺寸
    const container = modal.querySelector('.modal-container') as HTMLElement;
    if (props.width) {
      container.style.width = props.width;
    }
    if (props.height) {
      container.style.height = props.height;
    }

    // 应用自定义样式
    if (props.style) {
      Object.assign(modal.style, props.style);
    }
  }

  private bindEvents(): void {
    if (!this.state.element) return;

    const modal = this.state.element;
    const props = this.state.props as ModalProps;

    // 关闭按钮事件
    const closeBtn = modal.querySelector('.modal-close');
    if (closeBtn) {
      closeBtn.addEventListener('click', () => {
        this.close();
      });
    }

    // 遮罩点击事件
    if (props.maskClosable !== false) {
      const mask = modal.querySelector('.modal-mask');
      if (mask) {
        mask.addEventListener('click', () => {
          this.close();
        });
      }
    }

    // ESC键关闭
    document.addEventListener('keydown', (event: KeyboardEvent) => {
      if (event.key === 'Escape' && this.isOpen) {
        this.close();
      }
    });
  }

  // 公共方法
  open(): void {
    if (this.isOpen) return;

    this.isOpen = true;
    if (this.state.element) {
      this.state.element.style.display = 'block';
      document.body.classList.add('modal-open');
    }

    const props = this.state.props as ModalProps;
    if (props.onOpen) {
      props.onOpen();
    }

    this.emit('open');
  }

  close(): void {
    if (!this.isOpen) return;

    this.isOpen = false;
    if (this.state.element) {
      this.state.element.style.display = 'none';
      document.body.classList.remove('modal-open');
    }

    const props = this.state.props as ModalProps;
    if (props.onClose) {
      props.onClose();
    }

    this.emit('close');
  }

  toggle(): void {
    if (this.isOpen) {
      this.close();
    } else {
      this.open();
    }
  }

  setTitle(title: string): void {
    this.setProps({ title });
  }

  setContent(content: string | HTMLElement): void {
    this.setProps({ content });
  }
}
```

### 步骤5：创建组件工厂
**文件**: `src/presentation/components/core/ComponentFactory.ts`

```typescript
import { IComponent, ComponentConfig } from '../../interfaces/IComponent';
import { Button, ButtonProps } from './Button';
import { Input, InputProps } from './Input';
import { Modal, ModalProps } from './Modal';

export class ComponentFactory {
  private static components = new Map<string, new (config?: ComponentConfig) => IComponent>();

  static {
    // 注册核心组件
    this.register('button', Button);
    this.register('input', Input);
    this.register('modal', Modal);
  }

  static register<T extends IComponent>(
    name: string, 
    componentClass: new (config?: ComponentConfig) => T
  ): void {
    this.components.set(name, componentClass);
  }

  static create<T extends IComponent>(
    name: string, 
    config?: ComponentConfig
  ): T | null {
    const ComponentClass = this.components.get(name);
    if (ComponentClass) {
      return new ComponentClass(config) as T;
    }
    return null;
  }

  static createButton(props?: ButtonProps): Button {
    const button = new Button();
    if (props) {
      button.setProps(props);
    }
    return button;
  }

  static createInput(props?: InputProps): Input {
    const input = new Input();
    if (props) {
      input.setProps(props);
    }
    return input;
  }

  static createModal(props?: ModalProps): Modal {
    const modal = new Modal();
    if (props) {
      modal.setProps(props);
    }
    return modal;
  }

  static getRegisteredComponents(): string[] {
    return Array.from(this.components.keys());
  }
}
```

## ✅ 验收标准

### 功能验收
- [ ] IComponent接口定义完整
- [ ] BaseComponent基类实现完整
- [ ] Button组件功能完整
- [ ] Input组件功能完整
- [ ] Modal组件功能完整
- [ ] ComponentFactory工厂模式实现

### 质量验收
- [ ] TypeScript编译无错误
- [ ] 组件生命周期管理正确
- [ ] 事件处理机制完善
- [ ] 样式应用正确

### 可用性验收
- [ ] 组件API设计合理
- [ ] 组件可复用性强
- [ ] 组件扩展性好

## 🔄 幂等性保证

### 执行前检查
```bash
#!/bin/bash
echo "🔍 检查任务5.1执行状态..."

if [ ! -f "test/application-layer-test.html" ]; then
  echo "❌ 依赖任务Phase-4未完成，请先完成应用层重构"
  exit 1
fi

if [ -f "src/presentation/components/core/Button.ts" ]; then
  echo "⚠️  核心组件已存在，任务可能已完成"
  read -p "是否继续？(y/N): " confirm
  if [ "$confirm" != "y" ]; then
    exit 1
  fi
fi

echo "✅ 前置检查完成，可以执行任务5.1"
```

### 执行后验证
```bash
#!/bin/bash
echo "🔍 验证任务5.1执行结果..."

files=(
  "src/presentation/interfaces/IComponent.ts"
  "src/presentation/components/core/Button.ts"
  "src/presentation/components/core/Input.ts"
  "src/presentation/components/core/Modal.ts"
  "src/presentation/components/core/ComponentFactory.ts"
)

all_created=true
for file in "${files[@]}"; do
  if [ -f "$file" ]; then
    echo "✅ $file 创建成功"
  else
    echo "❌ $file 创建失败"
    all_created=false
  fi
done

if npm run type-check > /dev/null 2>&1; then
  echo "✅ TypeScript编译检查通过"
else
  echo "❌ TypeScript编译检查失败"
  all_created=false
fi

if [ "$all_created" = true ]; then
  echo "🎉 任务5.1执行成功！"
  exit 0
else
  echo "💥 任务5.1执行失败，请检查错误信息"
  exit 1
fi
```

## 📝 完成确认

任务完成后，请在检查清单中标记：
- [ ] 任务5.1已完成
- [ ] 核心组件接口定义完成
- [ ] 基础组件实现完成
- [ ] 组件工厂创建完成
- [ ] 准备执行任务5.2

## 🔗 相关链接

- [上一个阶段：Phase-4 应用层重构](../phase4/task-4.4-application-layer-test-page.md)
- [下一个任务：Task-5.2 重构页面组件](./task-5.2-page-components.md)
- [重构检查清单](../00-refactoring-checklist.md)
