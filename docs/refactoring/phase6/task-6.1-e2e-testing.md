# Task 6.1: 端到端测试

## 📋 任务概述

- **任务ID**: 6.1
- **任务名称**: 端到端测试
- **所属阶段**: Phase 6 - 集成测试和优化
- **预估时间**: 1天
- **任务类型**: 测试
- **优先级**: 高
- **依赖任务**: 5.1, 5.2, 5.3, 5.4
- **负责人**: 开发团队
- **状态**: 待执行

## 🎯 任务目标

### 主要目标
1. **实现端到端测试** - 验证整个系统的完整功能流程
2. **创建用户场景测试** - 模拟真实用户使用场景
3. **实现集成测试** - 验证各层之间的集成
4. **建立回归测试** - 确保新功能不破坏现有功能
5. **提供自动化测试** - 支持持续集成和部署

### 技术目标
- 创建完整的测试套件
- 实现自动化测试脚本
- 提供测试报告和分析
- 确保测试的稳定性和可靠性
- 支持多环境测试

## 📋 前置条件检查

### 依赖检查
```bash
# 检查所有层级是否完成
- [ ] 基础设施层测试通过
- [ ] 数据层测试通过
- [ ] 业务层测试通过
- [ ] 应用层测试通过
- [ ] 表现层测试通过

# 检查测试环境
- [ ] 测试框架已安装
- [ ] 浏览器驱动已配置
- [ ] 测试数据已准备
```

### 环境检查
```bash
# 检查系统状态
npm run build       # 构建成功
npm run type-check  # 类型检查通过
npm run lint        # 代码质量检查通过
```

## 🛠️ 实施步骤

### 步骤1: 创建E2E测试框架 (20分钟)

#### 1.1 安装测试依赖
```bash
npm install --save-dev playwright @playwright/test
npm install --save-dev jest-environment-jsdom
```

#### 1.2 创建测试配置
```javascript
// test/e2e/playwright.config.js
import { defineConfig, devices } from '@playwright/test';

export default defineConfig({
  testDir: './tests',
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  reporter: [
    ['html'],
    ['json', { outputFile: 'test-results/results.json' }],
    ['junit', { outputFile: 'test-results/results.xml' }]
  ],
  use: {
    baseURL: 'http://localhost:3000',
    trace: 'on-first-retry',
    screenshot: 'only-on-failure',
    video: 'retain-on-failure'
  },
  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
    },
    {
      name: 'firefox',
      use: { ...devices['Desktop Firefox'] },
    },
    {
      name: 'webkit',
      use: { ...devices['Desktop Safari'] },
    },
    {
      name: 'Mobile Chrome',
      use: { ...devices['Pixel 5'] },
    },
  ],
  webServer: {
    command: 'npm run serve',
    url: 'http://localhost:3000',
    reuseExistingServer: !process.env.CI,
  },
});
```

### 步骤2: 创建用户场景测试 (40分钟)

#### 2.1 内存管理场景测试
```javascript
// test/e2e/tests/memory-management.spec.js
import { test, expect } from '@playwright/test';

test.describe('内存管理功能', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
    // 等待应用加载完成
    await page.waitForSelector('[data-testid="app-loaded"]');
  });

  test('创建新内存', async ({ page }) => {
    // 点击添加内存按钮
    await page.click('[data-testid="add-memory-btn"]');
    
    // 填写内存信息
    await page.fill('[data-testid="memory-title"]', '测试内存标题');
    await page.fill('[data-testid="memory-content"]', '这是一个测试内存的内容');
    
    // 选择日期
    await page.click('[data-testid="memory-date"]');
    await page.click('[data-testid="date-today"]');
    
    // 上传图片
    const fileInput = page.locator('[data-testid="image-upload"]');
    await fileInput.setInputFiles('test/fixtures/test-image.jpg');
    
    // 保存内存
    await page.click('[data-testid="save-memory-btn"]');
    
    // 验证内存创建成功
    await expect(page.locator('[data-testid="success-message"]')).toBeVisible();
    await expect(page.locator('[data-testid="memory-list"]')).toContainText('测试内存标题');
  });

  test('编辑现有内存', async ({ page }) => {
    // 假设已有内存存在
    await page.click('[data-testid="memory-item"]:first-child [data-testid="edit-btn"]');
    
    // 修改标题
    await page.fill('[data-testid="memory-title"]', '修改后的标题');
    
    // 保存修改
    await page.click('[data-testid="save-memory-btn"]');
    
    // 验证修改成功
    await expect(page.locator('[data-testid="memory-list"]')).toContainText('修改后的标题');
  });

  test('删除内存', async ({ page }) => {
    // 点击删除按钮
    await page.click('[data-testid="memory-item"]:first-child [data-testid="delete-btn"]');
    
    // 确认删除
    await page.click('[data-testid="confirm-delete-btn"]');
    
    // 验证删除成功
    await expect(page.locator('[data-testid="success-message"]')).toBeVisible();
  });

  test('搜索内存', async ({ page }) => {
    // 在搜索框输入关键词
    await page.fill('[data-testid="search-input"]', '测试');
    
    // 点击搜索按钮
    await page.click('[data-testid="search-btn"]');
    
    // 验证搜索结果
    await expect(page.locator('[data-testid="search-results"]')).toBeVisible();
    await expect(page.locator('[data-testid="memory-item"]')).toContainText('测试');
  });
});
```

#### 2.2 设置管理场景测试
```javascript
// test/e2e/tests/settings-management.spec.js
import { test, expect } from '@playwright/test';

test.describe('设置管理功能', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/settings');
    await page.waitForSelector('[data-testid="settings-loaded"]');
  });

  test('主题切换', async ({ page }) => {
    // 切换到深色主题
    await page.click('[data-testid="theme-selector"]');
    await page.click('[data-testid="theme-dark"]');
    
    // 验证主题切换
    await expect(page.locator('body')).toHaveClass(/dark-theme/);
    
    // 切换回浅色主题
    await page.click('[data-testid="theme-selector"]');
    await page.click('[data-testid="theme-light"]');
    
    // 验证主题切换
    await expect(page.locator('body')).not.toHaveClass(/dark-theme/);
  });

  test('语言切换', async ({ page }) => {
    // 切换到英文
    await page.click('[data-testid="language-selector"]');
    await page.click('[data-testid="language-en"]');
    
    // 验证语言切换
    await expect(page.locator('[data-testid="settings-title"]')).toContainText('Settings');
    
    // 切换回中文
    await page.click('[data-testid="language-selector"]');
    await page.click('[data-testid="language-zh"]');
    
    // 验证语言切换
    await expect(page.locator('[data-testid="settings-title"]')).toContainText('设置');
  });

  test('存储配置', async ({ page }) => {
    // 配置云存储
    await page.click('[data-testid="storage-config-btn"]');
    
    // 选择存储类型
    await page.click('[data-testid="storage-type-selector"]');
    await page.click('[data-testid="storage-type-minio"]');
    
    // 填写配置信息
    await page.fill('[data-testid="storage-endpoint"]', 'http://localhost:9000');
    await page.fill('[data-testid="storage-access-key"]', 'minioadmin');
    await page.fill('[data-testid="storage-secret-key"]', 'minioadmin');
    await page.fill('[data-testid="storage-bucket"]', 'test-bucket');
    
    // 测试连接
    await page.click('[data-testid="test-connection-btn"]');
    
    // 验证连接成功
    await expect(page.locator('[data-testid="connection-status"]')).toContainText('连接成功');
    
    // 保存配置
    await page.click('[data-testid="save-config-btn"]');
    
    // 验证保存成功
    await expect(page.locator('[data-testid="success-message"]')).toBeVisible();
  });
});
```

#### 2.3 数据迁移场景测试
```javascript
// test/e2e/tests/data-migration.spec.js
import { test, expect } from '@playwright/test';

test.describe('数据迁移功能', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/settings/migration');
    await page.waitForSelector('[data-testid="migration-loaded"]');
  });

  test('存储迁移流程', async ({ page }) => {
    // 选择源存储
    await page.click('[data-testid="source-storage-selector"]');
    await page.click('[data-testid="source-storage-local"]');
    
    // 选择目标存储
    await page.click('[data-testid="target-storage-selector"]');
    await page.click('[data-testid="target-storage-minio"]');
    
    // 开始迁移
    await page.click('[data-testid="start-migration-btn"]');
    
    // 等待迁移完成
    await page.waitForSelector('[data-testid="migration-complete"]', { timeout: 30000 });
    
    // 验证迁移结果
    await expect(page.locator('[data-testid="migration-status"]')).toContainText('迁移完成');
    await expect(page.locator('[data-testid="migrated-files-count"]')).toBeVisible();
  });

  test('数据备份', async ({ page }) => {
    // 点击备份按钮
    await page.click('[data-testid="backup-data-btn"]');
    
    // 选择备份选项
    await page.check('[data-testid="backup-memories"]');
    await page.check('[data-testid="backup-settings"]');
    
    // 开始备份
    await page.click('[data-testid="start-backup-btn"]');
    
    // 等待备份完成
    await page.waitForSelector('[data-testid="backup-complete"]', { timeout: 15000 });
    
    // 验证备份文件下载
    const downloadPromise = page.waitForEvent('download');
    await page.click('[data-testid="download-backup-btn"]');
    const download = await downloadPromise;
    
    expect(download.suggestedFilename()).toMatch(/backup-\d+\.json/);
  });
});
```

### 步骤3: 创建集成测试 (30分钟)

#### 3.1 跨层集成测试
```javascript
// test/e2e/tests/integration.spec.js
import { test, expect } from '@playwright/test';

test.describe('系统集成测试', () => {
  test('完整的内存生命周期', async ({ page }) => {
    await page.goto('/');
    
    // 1. 创建内存
    await page.click('[data-testid="add-memory-btn"]');
    await page.fill('[data-testid="memory-title"]', '集成测试内存');
    await page.fill('[data-testid="memory-content"]', '这是集成测试的内容');
    
    // 上传文件
    const fileInput = page.locator('[data-testid="image-upload"]');
    await fileInput.setInputFiles('test/fixtures/test-image.jpg');
    
    await page.click('[data-testid="save-memory-btn"]');
    await expect(page.locator('[data-testid="success-message"]')).toBeVisible();
    
    // 2. 验证存储
    await page.reload();
    await expect(page.locator('[data-testid="memory-list"]')).toContainText('集成测试内存');
    
    // 3. 搜索功能
    await page.fill('[data-testid="search-input"]', '集成测试');
    await page.click('[data-testid="search-btn"]');
    await expect(page.locator('[data-testid="search-results"]')).toContainText('集成测试内存');
    
    // 4. 编辑内存
    await page.click('[data-testid="memory-item"]:first-child [data-testid="edit-btn"]');
    await page.fill('[data-testid="memory-content"]', '修改后的内容');
    await page.click('[data-testid="save-memory-btn"]');
    
    // 5. 验证修改
    await expect(page.locator('[data-testid="memory-content"]')).toContainText('修改后的内容');
    
    // 6. 删除内存
    await page.click('[data-testid="delete-btn"]');
    await page.click('[data-testid="confirm-delete-btn"]');
    await expect(page.locator('[data-testid="success-message"]')).toBeVisible();
  });

  test('多设备响应式测试', async ({ page, browserName }) => {
    // 测试不同屏幕尺寸
    const viewports = [
      { width: 375, height: 667 },  // Mobile
      { width: 768, height: 1024 }, // Tablet
      { width: 1920, height: 1080 } // Desktop
    ];

    for (const viewport of viewports) {
      await page.setViewportSize(viewport);
      await page.goto('/');
      
      // 验证响应式布局
      await expect(page.locator('[data-testid="app-container"]')).toBeVisible();
      
      if (viewport.width < 768) {
        // 移动端特定测试
        await expect(page.locator('[data-testid="mobile-menu"]')).toBeVisible();
      } else {
        // 桌面端特定测试
        await expect(page.locator('[data-testid="desktop-sidebar"]')).toBeVisible();
      }
    }
  });
});
```

### 步骤4: 创建性能测试 (20分钟)

```javascript
// test/e2e/tests/performance.spec.js
import { test, expect } from '@playwright/test';

test.describe('性能测试', () => {
  test('页面加载性能', async ({ page }) => {
    const startTime = Date.now();
    
    await page.goto('/');
    await page.waitForSelector('[data-testid="app-loaded"]');
    
    const loadTime = Date.now() - startTime;
    
    // 验证加载时间小于3秒
    expect(loadTime).toBeLessThan(3000);
    
    // 检查性能指标
    const performanceMetrics = await page.evaluate(() => {
      const navigation = performance.getEntriesByType('navigation')[0];
      return {
        domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
        loadComplete: navigation.loadEventEnd - navigation.loadEventStart,
        firstPaint: performance.getEntriesByType('paint').find(entry => entry.name === 'first-paint')?.startTime,
        firstContentfulPaint: performance.getEntriesByType('paint').find(entry => entry.name === 'first-contentful-paint')?.startTime
      };
    });
    
    console.log('Performance Metrics:', performanceMetrics);
    
    // 验证首次内容绘制时间
    expect(performanceMetrics.firstContentfulPaint).toBeLessThan(2000);
  });

  test('大量数据处理性能', async ({ page }) => {
    await page.goto('/');
    
    // 模拟创建大量内存
    for (let i = 0; i < 50; i++) {
      await page.click('[data-testid="add-memory-btn"]');
      await page.fill('[data-testid="memory-title"]', `性能测试内存 ${i}`);
      await page.fill('[data-testid="memory-content"]', `内容 ${i}`);
      await page.click('[data-testid="save-memory-btn"]');
      await page.waitForSelector('[data-testid="success-message"]');
    }
    
    // 测试搜索性能
    const searchStartTime = Date.now();
    await page.fill('[data-testid="search-input"]', '性能测试');
    await page.click('[data-testid="search-btn"]');
    await page.waitForSelector('[data-testid="search-results"]');
    const searchTime = Date.now() - searchStartTime;
    
    // 验证搜索时间小于1秒
    expect(searchTime).toBeLessThan(1000);
  });
});
```

## ✅ 验收标准

### 功能验收
- [ ] 所有用户场景测试通过
- [ ] 集成测试覆盖主要功能
- [ ] 性能测试达到预期指标
- [ ] 回归测试确保稳定性
- [ ] 多浏览器兼容性测试通过

### 质量验收
- [ ] 测试覆盖率 ≥ 90%
- [ ] 测试稳定性 ≥ 95%
- [ ] 测试执行时间合理
- [ ] 测试报告完整详细

### 自动化验收
- [ ] 测试可以自动执行
- [ ] 支持持续集成
- [ ] 测试结果自动报告
- [ ] 失败测试自动重试

## 🔄 幂等性保证

### 执行前检查
```bash
# 检查测试环境
npx playwright install
npm run build

# 检查测试数据
ls -la test/fixtures/
```

### 执行后验证
```bash
# 运行测试
npx playwright test

# 检查测试报告
ls -la test-results/
npx playwright show-report
```

### 步骤5: 创建测试工具和辅助函数 (10分钟)

```javascript
// test/e2e/utils/test-helpers.js
export class TestHelpers {
  static async waitForAppLoad(page) {
    await page.waitForSelector('[data-testid="app-loaded"]');
    await page.waitForLoadState('networkidle');
  }

  static async createTestMemory(page, title = '测试内存', content = '测试内容') {
    await page.click('[data-testid="add-memory-btn"]');
    await page.fill('[data-testid="memory-title"]', title);
    await page.fill('[data-testid="memory-content"]', content);
    await page.click('[data-testid="save-memory-btn"]');
    await page.waitForSelector('[data-testid="success-message"]');
  }

  static async uploadTestFile(page, filePath) {
    const fileInput = page.locator('[data-testid="file-upload"]');
    await fileInput.setInputFiles(filePath);
  }

  static async takeScreenshot(page, name) {
    await page.screenshot({
      path: `test-results/screenshots/${name}-${Date.now()}.png`,
      fullPage: true
    });
  }

  static async measurePerformance(page, action) {
    const startTime = Date.now();
    await action();
    return Date.now() - startTime;
  }
}
```

## ✅ 验收标准

### 功能验收
- [ ] 所有用户场景测试通过
- [ ] 集成测试覆盖主要功能
- [ ] 性能测试达到预期指标
- [ ] 回归测试确保稳定性
- [ ] 多浏览器兼容性测试通过

### 质量验收
- [ ] 测试覆盖率 ≥ 90%
- [ ] 测试稳定性 ≥ 95%
- [ ] 测试执行时间合理
- [ ] 测试报告完整详细

### 自动化验收
- [ ] 测试可以自动执行
- [ ] 支持持续集成
- [ ] 测试结果自动报告
- [ ] 失败测试自动重试

## 🔄 幂等性保证

### 执行前检查
```bash
# 检查测试环境
npx playwright install
npm run build

# 检查测试数据
ls -la test/fixtures/
```

### 执行后验证
```bash
# 运行测试
npx playwright test

# 检查测试报告
ls -la test-results/
npx playwright show-report
```

## 📝 完成确认

### 开发者确认
- [ ] 所有测试文件已创建
- [ ] 测试配置正确
- [ ] 测试数据准备完整
- [ ] 文档已更新

### 测试确认
- [ ] 所有测试用例通过
- [ ] 测试报告生成正常
- [ ] 性能指标达标
- [ ] 兼容性测试通过

### 部署确认
- [ ] CI/CD集成完成
- [ ] 自动化测试运行正常
- [ ] 测试结果监控到位
- [ ] 回归测试策略确定

## 🔗 相关链接

- [Phase 6 概述](../README.md)
- [Task 6.2: 性能优化](./task-6.2-performance-optimization.md)
- [Task 6.3: 代码清理](./task-6.3-code-cleanup.md)
- [Task 6.4: 文档更新](./task-6.4-documentation-update.md)

---

**任务状态**: ⏳ 待执行
**最后更新**: 2024-12-19
**下一个任务**: [Task 6.2: 性能优化](./task-6.2-performance-optimization.md)
