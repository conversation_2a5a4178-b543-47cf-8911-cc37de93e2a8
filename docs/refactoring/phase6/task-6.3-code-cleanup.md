# Task 6.3: 代码清理

## 📋 任务概述

- **任务ID**: 6.3
- **任务名称**: 代码清理
- **所属阶段**: Phase 6 - 集成测试和优化
- **预估时间**: 0.5天
- **任务类型**: 清理
- **优先级**: 中
- **依赖任务**: 6.1, 6.2
- **负责人**: 开发团队
- **状态**: 待执行

## 🎯 任务目标

### 主要目标
1. **删除旧代码** - 移除重构前的旧代码文件
2. **更新引用** - 修正所有导入和引用路径
3. **清理冗余代码** - 移除未使用的代码和依赖
4. **统一代码风格** - 确保代码风格一致性
5. **优化项目结构** - 整理和优化目录结构

### 技术目标
- 减少代码库大小 30%
- 移除所有未使用的依赖
- 统一代码格式和风格
- 确保所有引用正确
- 优化构建配置

## 📋 前置条件检查

### 依赖检查
```bash
# 检查重构是否完成
- [ ] 所有新代码已实现并测试通过
- [ ] E2E测试全部通过
- [ ] 性能优化已完成

# 检查清理工具
- [ ] ESLint配置正确
- [ ] Prettier配置正确
- [ ] 依赖分析工具可用
```

### 环境检查
```bash
# 检查当前状态
npm run build       # 构建成功
npm run test        # 所有测试通过
npm run lint        # 代码质量检查
```

## 🛠️ 实施步骤

### 步骤1: 分析和识别待清理代码 (15分钟)

#### 1.1 创建代码分析脚本
```javascript
// scripts/analyze-code.js
const fs = require('fs');
const path = require('path');

class CodeAnalyzer {
  constructor() {
    this.oldFiles = [];
    this.newFiles = [];
    this.unusedFiles = [];
    this.duplicateCode = [];
  }

  analyzeProject() {
    console.log('🔍 分析项目代码...');
    
    this.findOldFiles();
    this.findUnusedFiles();
    this.findDuplicateCode();
    this.generateReport();
  }

  findOldFiles() {
    const oldPatterns = [
      'src/js/',           // 旧的JS文件
      'src/css/',          // 旧的CSS文件
      'popup/',            // 旧的popup文件
      'background/',       // 旧的background文件
      'content/',          // 旧的content文件
      'options/',          // 旧的options文件
    ];

    oldPatterns.forEach(pattern => {
      const fullPath = path.join(process.cwd(), pattern);
      if (fs.existsSync(fullPath)) {
        this.oldFiles.push(...this.getFilesRecursively(fullPath));
      }
    });
  }

  findUnusedFiles() {
    // 分析未使用的文件
    const allFiles = this.getFilesRecursively('src/');
    const importedFiles = new Set();
    
    // 扫描所有import语句
    allFiles.forEach(file => {
      if (file.endsWith('.ts') || file.endsWith('.js')) {
        const content = fs.readFileSync(file, 'utf8');
        const imports = content.match(/import.*from\s+['"]([^'"]+)['"]/g) || [];
        
        imports.forEach(imp => {
          const match = imp.match(/from\s+['"]([^'"]+)['"]/);
          if (match) {
            importedFiles.add(this.resolveImportPath(match[1], file));
          }
        });
      }
    });

    // 找出未被导入的文件
    this.unusedFiles = allFiles.filter(file => 
      !importedFiles.has(file) && 
      !file.includes('test/') &&
      !file.includes('.spec.') &&
      !file.includes('.test.')
    );
  }

  findDuplicateCode() {
    // 简单的重复代码检测
    const codeBlocks = new Map();
    const allFiles = this.getFilesRecursively('src/');
    
    allFiles.forEach(file => {
      if (file.endsWith('.ts') || file.endsWith('.js')) {
        const content = fs.readFileSync(file, 'utf8');
        const lines = content.split('\n');
        
        // 检查5行以上的代码块
        for (let i = 0; i <= lines.length - 5; i++) {
          const block = lines.slice(i, i + 5).join('\n').trim();
          if (block.length > 100) { // 忽略太短的块
            if (codeBlocks.has(block)) {
              codeBlocks.get(block).push({ file, line: i + 1 });
            } else {
              codeBlocks.set(block, [{ file, line: i + 1 }]);
            }
          }
        }
      }
    });

    // 找出重复的代码块
    for (const [block, locations] of codeBlocks) {
      if (locations.length > 1) {
        this.duplicateCode.push({ block: block.substring(0, 200), locations });
      }
    }
  }

  generateReport() {
    const report = {
      timestamp: new Date().toISOString(),
      summary: {
        oldFiles: this.oldFiles.length,
        unusedFiles: this.unusedFiles.length,
        duplicateBlocks: this.duplicateCode.length
      },
      details: {
        oldFiles: this.oldFiles,
        unusedFiles: this.unusedFiles,
        duplicateCode: this.duplicateCode
      }
    };

    fs.writeFileSync('code-analysis-report.json', JSON.stringify(report, null, 2));
    
    console.log('📊 代码分析报告:');
    console.log(`- 旧文件: ${this.oldFiles.length} 个`);
    console.log(`- 未使用文件: ${this.unusedFiles.length} 个`);
    console.log(`- 重复代码块: ${this.duplicateCode.length} 个`);
    console.log('详细报告已保存到 code-analysis-report.json');
  }

  getFilesRecursively(dir) {
    const files = [];
    
    if (!fs.existsSync(dir)) return files;
    
    const items = fs.readdirSync(dir);
    
    items.forEach(item => {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        files.push(...this.getFilesRecursively(fullPath));
      } else {
        files.push(fullPath);
      }
    });
    
    return files;
  }

  resolveImportPath(importPath, fromFile) {
    // 简化的路径解析
    if (importPath.startsWith('./') || importPath.startsWith('../')) {
      return path.resolve(path.dirname(fromFile), importPath);
    }
    return importPath;
  }
}

const analyzer = new CodeAnalyzer();
analyzer.analyzeProject();
```

#### 1.2 运行代码分析
```bash
# 运行代码分析
node scripts/analyze-code.js

# 分析依赖
npm run analyze-deps
npx depcheck
```

### 步骤2: 删除旧代码文件 (20分钟)

#### 2.1 创建安全删除脚本
```javascript
// scripts/safe-delete.js
const fs = require('fs');
const path = require('path');

class SafeDeleter {
  constructor() {
    this.deletedFiles = [];
    this.backupDir = 'backup-' + Date.now();
  }

  async deleteOldFiles() {
    console.log('🗑️  开始删除旧文件...');
    
    // 创建备份目录
    fs.mkdirSync(this.backupDir, { recursive: true });
    
    const filesToDelete = [
      // 旧的主要目录
      'src/js/',
      'src/css/',
      'popup/',
      'background/',
      'content/',
      'options/',
      
      // 旧的配置文件
      'webpack.config.old.js',
      'manifest.v2.json',
      
      // 旧的样式文件
      'src/styles/old/',
      
      // 临时文件
      '*.tmp',
      '*.bak',
      '.DS_Store'
    ];

    for (const pattern of filesToDelete) {
      await this.deletePattern(pattern);
    }

    console.log(`✅ 删除完成，共删除 ${this.deletedFiles.length} 个文件`);
    console.log(`📦 备份保存在: ${this.backupDir}`);
  }

  async deletePattern(pattern) {
    const fullPath = path.join(process.cwd(), pattern);
    
    if (fs.existsSync(fullPath)) {
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        await this.deleteDirectory(fullPath);
      } else {
        await this.deleteFile(fullPath);
      }
    }
  }

  async deleteDirectory(dirPath) {
    const files = fs.readdirSync(dirPath);
    
    for (const file of files) {
      const filePath = path.join(dirPath, file);
      const stat = fs.statSync(filePath);
      
      if (stat.isDirectory()) {
        await this.deleteDirectory(filePath);
      } else {
        await this.deleteFile(filePath);
      }
    }
    
    // 备份目录结构
    const relativePath = path.relative(process.cwd(), dirPath);
    const backupPath = path.join(this.backupDir, relativePath);
    fs.mkdirSync(backupPath, { recursive: true });
    
    fs.rmdirSync(dirPath);
    console.log(`📁 删除目录: ${relativePath}`);
  }

  async deleteFile(filePath) {
    // 备份文件
    const relativePath = path.relative(process.cwd(), filePath);
    const backupPath = path.join(this.backupDir, relativePath);
    
    fs.mkdirSync(path.dirname(backupPath), { recursive: true });
    fs.copyFileSync(filePath, backupPath);
    
    // 删除原文件
    fs.unlinkSync(filePath);
    this.deletedFiles.push(relativePath);
    
    console.log(`🗑️  删除文件: ${relativePath}`);
  }
}

const deleter = new SafeDeleter();
deleter.deleteOldFiles().catch(console.error);
```

### 步骤3: 更新引用和导入 (15分钟)

#### 3.1 创建引用更新脚本
```javascript
// scripts/update-imports.js
const fs = require('fs');
const path = require('path');

class ImportUpdater {
  constructor() {
    this.updatedFiles = [];
    this.importMappings = new Map([
      // 旧路径 -> 新路径映射
      ['../js/', '../infrastructure/'],
      ['./js/', './infrastructure/'],
      ['../utils/', '../infrastructure/utils/'],
      ['./utils/', './infrastructure/utils/'],
      ['../services/', '../business/managers/'],
      ['./services/', './business/managers/'],
    ]);
  }

  updateAllImports() {
    console.log('🔄 更新导入引用...');
    
    const files = this.getFilesRecursively('src/');
    
    files.forEach(file => {
      if (file.endsWith('.ts') || file.endsWith('.js') || file.endsWith('.tsx') || file.endsWith('.jsx')) {
        this.updateFileImports(file);
      }
    });

    console.log(`✅ 更新完成，共更新 ${this.updatedFiles.length} 个文件`);
  }

  updateFileImports(filePath) {
    let content = fs.readFileSync(filePath, 'utf8');
    let hasChanges = false;

    // 更新import语句
    content = content.replace(/import\s+.*?\s+from\s+['"]([^'"]+)['"]/g, (match, importPath) => {
      const newPath = this.resolveNewPath(importPath);
      if (newPath !== importPath) {
        hasChanges = true;
        return match.replace(importPath, newPath);
      }
      return match;
    });

    // 更新require语句
    content = content.replace(/require\(['"]([^'"]+)['"]\)/g, (match, requirePath) => {
      const newPath = this.resolveNewPath(requirePath);
      if (newPath !== requirePath) {
        hasChanges = true;
        return match.replace(requirePath, newPath);
      }
      return match;
    });

    if (hasChanges) {
      fs.writeFileSync(filePath, content);
      this.updatedFiles.push(filePath);
      console.log(`🔄 更新: ${path.relative(process.cwd(), filePath)}`);
    }
  }

  resolveNewPath(oldPath) {
    for (const [oldPattern, newPattern] of this.importMappings) {
      if (oldPath.includes(oldPattern)) {
        return oldPath.replace(oldPattern, newPattern);
      }
    }
    return oldPath;
  }

  getFilesRecursively(dir) {
    const files = [];
    
    if (!fs.existsSync(dir)) return files;
    
    const items = fs.readdirSync(dir);
    
    items.forEach(item => {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        files.push(...this.getFilesRecursively(fullPath));
      } else {
        files.push(fullPath);
      }
    });
    
    return files;
  }
}

const updater = new ImportUpdater();
updater.updateAllImports();
```

### 步骤4: 清理依赖和配置 (10分钟)

#### 4.1 清理package.json
```javascript
// scripts/cleanup-dependencies.js
const fs = require('fs');

class DependencyCleanup {
  cleanupPackageJson() {
    console.log('🧹 清理package.json...');
    
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    
    // 移除未使用的依赖
    const unusedDeps = [
      'jquery',
      'bootstrap',
      'moment', // 已替换为dayjs
      'lodash', // 使用原生方法替代
    ];

    unusedDeps.forEach(dep => {
      if (packageJson.dependencies && packageJson.dependencies[dep]) {
        delete packageJson.dependencies[dep];
        console.log(`🗑️  移除依赖: ${dep}`);
      }
      if (packageJson.devDependencies && packageJson.devDependencies[dep]) {
        delete packageJson.devDependencies[dep];
        console.log(`🗑️  移除开发依赖: ${dep}`);
      }
    });

    // 清理scripts
    const unusedScripts = [
      'build:old',
      'dev:old',
      'test:old'
    ];

    unusedScripts.forEach(script => {
      if (packageJson.scripts && packageJson.scripts[script]) {
        delete packageJson.scripts[script];
        console.log(`🗑️  移除脚本: ${script}`);
      }
    });

    // 保存更新后的package.json
    fs.writeFileSync('package.json', JSON.stringify(packageJson, null, 2));
    console.log('✅ package.json清理完成');
  }

  cleanupConfigFiles() {
    console.log('🧹 清理配置文件...');
    
    const configFilesToRemove = [
      'webpack.config.old.js',
      'babel.config.old.js',
      'tsconfig.old.json',
      '.eslintrc.old.js'
    ];

    configFilesToRemove.forEach(file => {
      if (fs.existsSync(file)) {
        fs.unlinkSync(file);
        console.log(`🗑️  删除配置文件: ${file}`);
      }
    });

    console.log('✅ 配置文件清理完成');
  }
}

const cleanup = new DependencyCleanup();
cleanup.cleanupPackageJson();
cleanup.cleanupConfigFiles();
```

### 步骤5: 代码格式化和风格统一 (10分钟)

```bash
# 运行代码格式化
npx prettier --write "src/**/*.{ts,tsx,js,jsx,css,scss,json}"

# 运行ESLint修复
npx eslint "src/**/*.{ts,tsx,js,jsx}" --fix

# 检查TypeScript
npx tsc --noEmit

# 最终验证
npm run build
npm run test
```

## ✅ 验收标准

### 清理验收
- [ ] 所有旧代码文件已删除
- [ ] 所有引用路径已更新
- [ ] 未使用的依赖已移除
- [ ] 代码风格统一
- [ ] 项目结构清晰

### 功能验收
- [ ] 所有功能正常工作
- [ ] 构建成功
- [ ] 测试全部通过
- [ ] 无引用错误

### 质量验收
- [ ] 代码库大小减少 ≥ 30%
- [ ] ESLint检查通过
- [ ] TypeScript编译无错误
- [ ] 代码覆盖率保持

## 🔄 幂等性保证

### 执行前检查
```bash
# 创建完整备份
git add .
git commit -m "Before code cleanup"

# 检查当前状态
npm run build
npm run test
```

### 执行后验证
```bash
# 验证清理结果
npm run build
npm run test
npm run lint

# 检查文件大小
du -sh src/
```

## 📝 完成确认

### 开发者确认
- [ ] 代码分析报告已生成
- [ ] 旧文件已安全删除
- [ ] 引用路径已更新
- [ ] 依赖已清理

### 质量确认
- [ ] 代码风格统一
- [ ] 构建成功
- [ ] 测试通过
- [ ] 性能保持

### 项目确认
- [ ] 项目结构清晰
- [ ] 文档已更新
- [ ] 备份已创建
- [ ] 清理日志完整

## 🔗 相关链接

- [Task 6.1: 端到端测试](./task-6.1-e2e-testing.md)
- [Task 6.2: 性能优化](./task-6.2-performance-optimization.md)
- [Task 6.4: 文档更新](./task-6.4-documentation-update.md)

---

**任务状态**: ⏳ 待执行
**最后更新**: 2024-12-19
**下一个任务**: [Task 6.4: 文档更新](./task-6.4-documentation-update.md)
