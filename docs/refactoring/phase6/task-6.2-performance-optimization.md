# Task 6.2: 性能优化

## 📋 任务概述

- **任务ID**: 6.2
- **任务名称**: 性能优化
- **所属阶段**: Phase 6 - 集成测试和优化
- **预估时间**: 1天
- **任务类型**: 优化
- **优先级**: 高
- **依赖任务**: 6.1
- **负责人**: 开发团队
- **状态**: 待执行

## 🎯 任务目标

### 主要目标
1. **代码性能优化** - 优化关键代码路径和算法
2. **缓存策略优化** - 实现高效的缓存机制
3. **加载性能优化** - 减少首屏加载时间
4. **内存使用优化** - 降低内存占用和泄漏风险
5. **网络请求优化** - 减少网络请求次数和大小

### 技术目标
- 首屏加载时间 < 2秒
- 内存使用 < 100MB
- 网络请求优化 50%
- 代码执行效率提升 30%
- 用户交互响应时间 < 100ms

## 📋 前置条件检查

### 依赖检查
```bash
# 检查E2E测试是否完成
- [ ] 端到端测试全部通过
- [ ] 性能基准测试已建立
- [ ] 性能监控工具已配置

# 检查优化工具
- [ ] Webpack Bundle Analyzer已安装
- [ ] Chrome DevTools可用
- [ ] 性能分析工具已准备
```

### 环境检查
```bash
# 检查构建状态
npm run build       # 构建成功
npm run analyze     # 包分析可用
npm run test        # 所有测试通过
```

## 🛠️ 实施步骤

### 步骤1: 建立性能监控基础设施 (20分钟)

#### 1.1 创建性能监控工具
```javascript
// src/utils/PerformanceMonitor.ts
export class PerformanceMonitor {
  private static instance: PerformanceMonitor;
  private metrics: Map<string, number[]> = new Map();
  private observers: PerformanceObserver[] = [];

  static getInstance(): PerformanceMonitor {
    if (!this.instance) {
      this.instance = new PerformanceMonitor();
    }
    return this.instance;
  }

  init(): void {
    this.setupPerformanceObservers();
    this.setupMemoryMonitoring();
    this.setupNetworkMonitoring();
  }

  private setupPerformanceObservers(): void {
    // 监控导航性能
    const navObserver = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        this.recordMetric('navigation', entry.duration);
      }
    });
    navObserver.observe({ entryTypes: ['navigation'] });
    this.observers.push(navObserver);

    // 监控资源加载
    const resourceObserver = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        this.recordMetric('resource', entry.duration);
      }
    });
    resourceObserver.observe({ entryTypes: ['resource'] });
    this.observers.push(resourceObserver);

    // 监控用户交互
    const measureObserver = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        this.recordMetric(entry.name, entry.duration);
      }
    });
    measureObserver.observe({ entryTypes: ['measure'] });
    this.observers.push(measureObserver);
  }

  measureFunction<T>(name: string, fn: () => T): T {
    const start = performance.now();
    const result = fn();
    const duration = performance.now() - start;
    this.recordMetric(name, duration);
    return result;
  }

  async measureAsyncFunction<T>(name: string, fn: () => Promise<T>): Promise<T> {
    const start = performance.now();
    const result = await fn();
    const duration = performance.now() - start;
    this.recordMetric(name, duration);
    return result;
  }

  recordMetric(name: string, value: number): void {
    if (!this.metrics.has(name)) {
      this.metrics.set(name, []);
    }
    this.metrics.get(name)!.push(value);
  }

  getMetrics(): Record<string, { avg: number; min: number; max: number; count: number }> {
    const result: Record<string, any> = {};
    
    for (const [name, values] of this.metrics) {
      result[name] = {
        avg: values.reduce((a, b) => a + b, 0) / values.length,
        min: Math.min(...values),
        max: Math.max(...values),
        count: values.length
      };
    }
    
    return result;
  }

  private setupMemoryMonitoring(): void {
    if ('memory' in performance) {
      setInterval(() => {
        const memory = (performance as any).memory;
        this.recordMetric('memory.used', memory.usedJSHeapSize);
        this.recordMetric('memory.total', memory.totalJSHeapSize);
        this.recordMetric('memory.limit', memory.jsHeapSizeLimit);
      }, 5000);
    }
  }

  private setupNetworkMonitoring(): void {
    // 监控网络请求
    const originalFetch = window.fetch;
    window.fetch = async (...args) => {
      const start = performance.now();
      try {
        const response = await originalFetch(...args);
        const duration = performance.now() - start;
        this.recordMetric('network.request', duration);
        return response;
      } catch (error) {
        const duration = performance.now() - start;
        this.recordMetric('network.error', duration);
        throw error;
      }
    };
  }

  exportMetrics(): string {
    return JSON.stringify(this.getMetrics(), null, 2);
  }

  destroy(): void {
    this.observers.forEach(observer => observer.disconnect());
    this.observers = [];
    this.metrics.clear();
  }
}

export const performanceMonitor = PerformanceMonitor.getInstance();
```

#### 1.2 创建性能分析工具
```javascript
// src/utils/PerformanceAnalyzer.ts
export class PerformanceAnalyzer {
  static analyzeBundle(): void {
    // 分析包大小
    console.log('Bundle Analysis:');
    console.log('- Main bundle size:', this.getBundleSize());
    console.log('- Chunk analysis:', this.getChunkAnalysis());
    console.log('- Unused code:', this.getUnusedCode());
  }

  static analyzeLCP(): Promise<number> {
    return new Promise((resolve) => {
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const lastEntry = entries[entries.length - 1];
        resolve(lastEntry.startTime);
        observer.disconnect();
      });
      observer.observe({ entryTypes: ['largest-contentful-paint'] });
    });
  }

  static analyzeFID(): Promise<number> {
    return new Promise((resolve) => {
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const firstEntry = entries[0];
        resolve(firstEntry.processingStart - firstEntry.startTime);
        observer.disconnect();
      });
      observer.observe({ entryTypes: ['first-input'] });
    });
  }

  static analyzeCLS(): Promise<number> {
    return new Promise((resolve) => {
      let clsValue = 0;
      const observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (!entry.hadRecentInput) {
            clsValue += entry.value;
          }
        }
        resolve(clsValue);
      });
      observer.observe({ entryTypes: ['layout-shift'] });
      
      // 5秒后返回结果
      setTimeout(() => {
        observer.disconnect();
        resolve(clsValue);
      }, 5000);
    });
  }

  private static getBundleSize(): string {
    // 实现包大小分析
    return 'Analysis needed';
  }

  private static getChunkAnalysis(): string {
    // 实现代码块分析
    return 'Analysis needed';
  }

  private static getUnusedCode(): string {
    // 实现未使用代码分析
    return 'Analysis needed';
  }
}
```

### 步骤2: 代码性能优化 (40分钟)

#### 2.1 优化关键算法
```typescript
// src/business/managers/OptimizedMemoryManager.ts
import { IMemoryManager } from '../interfaces/IMemoryManager';
import { Memory } from '../../domain/entities/Memory';

export class OptimizedMemoryManager implements IMemoryManager {
  private memoryCache = new Map<string, Memory>();
  private searchIndex = new Map<string, Set<string>>();
  private sortedMemories: Memory[] = [];

  async getMemories(page: number = 1, pageSize: number = 20): Promise<Memory[]> {
    // 使用虚拟滚动优化大量数据显示
    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    
    if (this.sortedMemories.length === 0) {
      await this.loadAndSortMemories();
    }
    
    return this.sortedMemories.slice(startIndex, endIndex);
  }

  async searchMemories(query: string): Promise<Memory[]> {
    // 使用预建索引优化搜索
    const keywords = query.toLowerCase().split(' ');
    let resultIds: Set<string> | null = null;
    
    for (const keyword of keywords) {
      const matchingIds = this.searchIndex.get(keyword) || new Set();
      
      if (resultIds === null) {
        resultIds = new Set(matchingIds);
      } else {
        // 交集操作
        resultIds = new Set([...resultIds].filter(id => matchingIds.has(id)));
      }
    }
    
    if (!resultIds) return [];
    
    return Array.from(resultIds)
      .map(id => this.memoryCache.get(id))
      .filter(memory => memory !== undefined) as Memory[];
  }

  private async loadAndSortMemories(): Promise<void> {
    // 批量加载并排序
    const memories = await this.repository.getAll();
    
    // 使用高效排序算法
    this.sortedMemories = memories.sort((a, b) => 
      new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    );
    
    // 构建缓存和索引
    this.buildCacheAndIndex(memories);
  }

  private buildCacheAndIndex(memories: Memory[]): void {
    this.memoryCache.clear();
    this.searchIndex.clear();
    
    for (const memory of memories) {
      // 构建缓存
      this.memoryCache.set(memory.id, memory);
      
      // 构建搜索索引
      const searchText = `${memory.title} ${memory.content}`.toLowerCase();
      const words = searchText.split(/\s+/);
      
      for (const word of words) {
        if (word.length > 2) { // 忽略太短的词
          if (!this.searchIndex.has(word)) {
            this.searchIndex.set(word, new Set());
          }
          this.searchIndex.get(word)!.add(memory.id);
        }
      }
    }
  }
}
```

#### 2.2 优化React组件性能
```typescript
// src/presentation/components/optimized/VirtualizedMemoryList.tsx
import React, { useMemo, useCallback, memo } from 'react';
import { FixedSizeList as List } from 'react-window';

interface VirtualizedMemoryListProps {
  memories: Memory[];
  onMemoryClick: (memory: Memory) => void;
  height: number;
  itemHeight: number;
}

const MemoryItem = memo(({ index, style, data }: any) => {
  const { memories, onMemoryClick } = data;
  const memory = memories[index];
  
  const handleClick = useCallback(() => {
    onMemoryClick(memory);
  }, [memory, onMemoryClick]);
  
  return (
    <div style={style} onClick={handleClick} className="memory-item">
      <h3>{memory.title}</h3>
      <p>{memory.content.substring(0, 100)}...</p>
      <span>{new Date(memory.createdAt).toLocaleDateString()}</span>
    </div>
  );
});

export const VirtualizedMemoryList: React.FC<VirtualizedMemoryListProps> = memo(({
  memories,
  onMemoryClick,
  height,
  itemHeight
}) => {
  const itemData = useMemo(() => ({
    memories,
    onMemoryClick
  }), [memories, onMemoryClick]);
  
  return (
    <List
      height={height}
      itemCount={memories.length}
      itemSize={itemHeight}
      itemData={itemData}
    >
      {MemoryItem}
    </List>
  );
});
```

### 步骤3: 缓存策略优化 (30分钟)

#### 3.1 实现多级缓存
```typescript
// src/infrastructure/cache/MultiLevelCache.ts
export class MultiLevelCache {
  private memoryCache = new Map<string, any>();
  private indexedDBCache: IDBDatabase | null = null;
  private maxMemorySize = 50; // 最大内存缓存项数

  async init(): Promise<void> {
    await this.initIndexedDB();
  }

  async get<T>(key: string): Promise<T | null> {
    // 1. 先查内存缓存
    if (this.memoryCache.has(key)) {
      return this.memoryCache.get(key);
    }
    
    // 2. 查IndexedDB缓存
    const value = await this.getFromIndexedDB<T>(key);
    if (value !== null) {
      // 提升到内存缓存
      this.setMemoryCache(key, value);
      return value;
    }
    
    return null;
  }

  async set<T>(key: string, value: T, ttl?: number): Promise<void> {
    // 设置内存缓存
    this.setMemoryCache(key, value);
    
    // 设置IndexedDB缓存
    await this.setIndexedDB(key, value, ttl);
  }

  private setMemoryCache<T>(key: string, value: T): void {
    // LRU淘汰策略
    if (this.memoryCache.size >= this.maxMemorySize) {
      const firstKey = this.memoryCache.keys().next().value;
      this.memoryCache.delete(firstKey);
    }
    
    this.memoryCache.set(key, value);
  }

  private async initIndexedDB(): Promise<void> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open('MemoryKeeperCache', 1);
      
      request.onerror = () => reject(request.error);
      request.onsuccess = () => {
        this.indexedDBCache = request.result;
        resolve();
      };
      
      request.onupgradeneeded = () => {
        const db = request.result;
        if (!db.objectStoreNames.contains('cache')) {
          const store = db.createObjectStore('cache', { keyPath: 'key' });
          store.createIndex('expiry', 'expiry', { unique: false });
        }
      };
    });
  }

  private async getFromIndexedDB<T>(key: string): Promise<T | null> {
    if (!this.indexedDBCache) return null;
    
    return new Promise((resolve) => {
      const transaction = this.indexedDBCache!.transaction(['cache'], 'readonly');
      const store = transaction.objectStore('cache');
      const request = store.get(key);
      
      request.onsuccess = () => {
        const result = request.result;
        if (result && (!result.expiry || result.expiry > Date.now())) {
          resolve(result.value);
        } else {
          resolve(null);
        }
      };
      
      request.onerror = () => resolve(null);
    });
  }

  private async setIndexedDB<T>(key: string, value: T, ttl?: number): Promise<void> {
    if (!this.indexedDBCache) return;
    
    const expiry = ttl ? Date.now() + ttl : null;
    
    return new Promise((resolve) => {
      const transaction = this.indexedDBCache!.transaction(['cache'], 'readwrite');
      const store = transaction.objectStore('cache');
      const request = store.put({ key, value, expiry });
      
      request.onsuccess = () => resolve();
      request.onerror = () => resolve(); // 静默失败
    });
  }
}
```

### 步骤4: 网络请求优化 (20分钟)

#### 4.1 实现请求合并和缓存
```typescript
// src/infrastructure/network/OptimizedNetworkClient.ts
export class OptimizedNetworkClient {
  private requestCache = new Map<string, Promise<any>>();
  private batchQueue = new Map<string, any[]>();
  private batchTimer: NodeJS.Timeout | null = null;

  async request<T>(url: string, options: RequestInit = {}): Promise<T> {
    const cacheKey = this.getCacheKey(url, options);
    
    // 检查是否有正在进行的相同请求
    if (this.requestCache.has(cacheKey)) {
      return this.requestCache.get(cacheKey);
    }
    
    // 创建请求Promise
    const requestPromise = this.executeRequest<T>(url, options);
    
    // 缓存请求Promise
    this.requestCache.set(cacheKey, requestPromise);
    
    // 请求完成后清除缓存
    requestPromise.finally(() => {
      this.requestCache.delete(cacheKey);
    });
    
    return requestPromise;
  }

  batchRequest(endpoint: string, data: any): Promise<any> {
    return new Promise((resolve, reject) => {
      // 添加到批处理队列
      if (!this.batchQueue.has(endpoint)) {
        this.batchQueue.set(endpoint, []);
      }
      
      this.batchQueue.get(endpoint)!.push({ data, resolve, reject });
      
      // 设置批处理定时器
      if (!this.batchTimer) {
        this.batchTimer = setTimeout(() => {
          this.processBatchQueue();
        }, 50); // 50ms内的请求合并
      }
    });
  }

  private async processBatchQueue(): Promise<void> {
    const batches = Array.from(this.batchQueue.entries());
    this.batchQueue.clear();
    this.batchTimer = null;
    
    for (const [endpoint, requests] of batches) {
      try {
        const batchData = requests.map(req => req.data);
        const response = await this.executeRequest(endpoint, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ batch: batchData })
        });
        
        // 分发结果
        response.results.forEach((result: any, index: number) => {
          requests[index].resolve(result);
        });
      } catch (error) {
        // 所有请求都失败
        requests.forEach(req => req.reject(error));
      }
    }
  }

  private async executeRequest<T>(url: string, options: RequestInit): Promise<T> {
    const response = await fetch(url, {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      }
    });
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    return response.json();
  }

  private getCacheKey(url: string, options: RequestInit): string {
    return `${options.method || 'GET'}:${url}:${JSON.stringify(options.body || {})}`;
  }
}
```

## ✅ 验收标准

### 性能指标验收
- [ ] 首屏加载时间 < 2秒
- [ ] 内存使用 < 100MB
- [ ] 网络请求减少 ≥ 50%
- [ ] 用户交互响应时间 < 100ms
- [ ] 代码执行效率提升 ≥ 30%

### 功能验收
- [ ] 所有功能正常工作
- [ ] 性能优化不影响功能
- [ ] 缓存策略有效
- [ ] 网络请求优化生效

### 质量验收
- [ ] 性能监控正常工作
- [ ] 性能指标可量化
- [ ] 优化效果可验证
- [ ] 代码质量保持

## 🔄 幂等性保证

### 执行前检查
```bash
# 建立性能基准
npm run build
npm run analyze
npm run performance-test
```

### 执行后验证
```bash
# 验证性能改进
npm run performance-test
npm run compare-performance
```

## 📝 完成确认

### 开发者确认
- [ ] 性能监控工具已实现
- [ ] 关键代码已优化
- [ ] 缓存策略已实现
- [ ] 网络请求已优化

### 性能确认
- [ ] 性能指标达标
- [ ] 优化效果明显
- [ ] 监控数据正常
- [ ] 用户体验提升

### 质量确认
- [ ] 功能完整性保持
- [ ] 代码质量良好
- [ ] 测试全部通过
- [ ] 文档已更新

## 🔗 相关链接

- [Task 6.1: 端到端测试](./task-6.1-e2e-testing.md)
- [Task 6.3: 代码清理](./task-6.3-code-cleanup.md)
- [Task 6.4: 文档更新](./task-6.4-documentation-update.md)

---

**任务状态**: ⏳ 待执行
**最后更新**: 2024-12-19
**下一个任务**: [Task 6.3: 代码清理](./task-6.3-code-cleanup.md)
