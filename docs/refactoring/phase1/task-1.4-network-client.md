# 任务1.4：创建网络客户端

## 📋 任务概述

**任务ID**: Task-1.4  
**任务名称**: 创建统一网络客户端  
**所属阶段**: 阶段1 - 基础设施层重构  
**预计时间**: 2小时  
**依赖任务**: Task-1.3 (重构MinIO提供者)  
**后续任务**: Task-1.5 (创建加密服务)  

## 🎯 任务目标

创建统一的HTTP客户端，提供标准化的网络请求功能，支持拦截器、重试机制和错误处理。

## 📋 前置条件检查

### 依赖检查
- [ ] Task-1.3 已完成
- [ ] IHttpClient接口已定义

### 文件检查
- [ ] `src/infrastructure/interfaces/IStorageProvider.ts` 存在
- [ ] 项目依赖已安装

### 状态检查
```bash
# 检查网络客户端是否已存在
if [ -f "src/infrastructure/network/HttpClient.ts" ]; then
  echo "⚠️  网络客户端已存在，请确认是否需要重新创建"
  exit 1
else
  echo "✅ 可以开始创建网络客户端"
fi
```

## 🛠️ 实施步骤

### 步骤1：创建网络接口定义
**文件**: `src/infrastructure/interfaces/IHttpClient.ts`

```typescript
/**
 * HTTP客户端统一接口
 */
export interface IHttpClient {
  // 基础HTTP方法
  get<T>(url: string, options?: RequestOptions): Promise<HttpResponse<T>>;
  post<T>(url: string, data?: any, options?: RequestOptions): Promise<HttpResponse<T>>;
  put<T>(url: string, data?: any, options?: RequestOptions): Promise<HttpResponse<T>>;
  delete<T>(url: string, options?: RequestOptions): Promise<HttpResponse<T>>;
  patch<T>(url: string, data?: any, options?: RequestOptions): Promise<HttpResponse<T>>;
  head<T>(url: string, options?: RequestOptions): Promise<HttpResponse<T>>;
  
  // 流式请求
  getStream(url: string, options?: RequestOptions): Promise<ReadableStream>;
  postStream(url: string, stream: ReadableStream, options?: RequestOptions): Promise<HttpResponse<void>>;
  
  // 拦截器管理
  addRequestInterceptor(interceptor: RequestInterceptor): string;
  addResponseInterceptor(interceptor: ResponseInterceptor): string;
  removeRequestInterceptor(id: string): void;
  removeResponseInterceptor(id: string): void;
  
  // 配置管理
  setDefaultConfig(config: Partial<RequestOptions>): void;
  getDefaultConfig(): RequestOptions;
}

/**
 * 请求选项
 */
export interface RequestOptions {
  headers?: Record<string, string>;
  timeout?: number;
  retryCount?: number;
  retryDelay?: number;
  responseType?: 'json' | 'text' | 'blob' | 'arraybuffer';
  withCredentials?: boolean;
  signal?: AbortSignal;
}

/**
 * HTTP响应
 */
export interface HttpResponse<T> {
  data: T;
  status: number;
  statusText: string;
  headers: Record<string, string>;
  config: RequestOptions;
}

/**
 * 拦截器类型
 */
export type RequestInterceptor = (config: RequestOptions & { url: string; method: string; data?: any }) => 
  RequestOptions & { url: string; method: string; data?: any } | 
  Promise<RequestOptions & { url: string; method: string; data?: any }>;

export type ResponseInterceptor = <T>(response: HttpResponse<T>) => 
  HttpResponse<T> | Promise<HttpResponse<T>>;

/**
 * HTTP错误
 */
export class HttpError extends Error {
  constructor(
    message: string,
    public status: number,
    public response?: any,
    public config?: RequestOptions
  ) {
    super(message);
    this.name = 'HttpError';
  }
}
```

### 步骤2：创建HTTP客户端实现
**文件**: `src/infrastructure/network/HttpClient.ts`

```typescript
import { 
  IHttpClient, 
  RequestOptions, 
  HttpResponse, 
  RequestInterceptor, 
  ResponseInterceptor,
  HttpError 
} from '../interfaces/IHttpClient';

/**
 * 基于fetch的HTTP客户端实现
 */
export class HttpClient implements IHttpClient {
  private defaultConfig: RequestOptions = {
    timeout: 30000,
    retryCount: 3,
    retryDelay: 1000,
    responseType: 'json'
  };
  
  private requestInterceptors: Map<string, RequestInterceptor> = new Map();
  private responseInterceptors: Map<string, ResponseInterceptor> = new Map();
  private interceptorIdCounter = 0;

  /**
   * GET请求
   */
  async get<T>(url: string, options?: RequestOptions): Promise<HttpResponse<T>> {
    return this.request<T>('GET', url, undefined, options);
  }

  /**
   * POST请求
   */
  async post<T>(url: string, data?: any, options?: RequestOptions): Promise<HttpResponse<T>> {
    return this.request<T>('POST', url, data, options);
  }

  /**
   * PUT请求
   */
  async put<T>(url: string, data?: any, options?: RequestOptions): Promise<HttpResponse<T>> {
    return this.request<T>('PUT', url, data, options);
  }

  /**
   * DELETE请求
   */
  async delete<T>(url: string, options?: RequestOptions): Promise<HttpResponse<T>> {
    return this.request<T>('DELETE', url, undefined, options);
  }

  /**
   * PATCH请求
   */
  async patch<T>(url: string, data?: any, options?: RequestOptions): Promise<HttpResponse<T>> {
    return this.request<T>('PATCH', url, data, options);
  }

  /**
   * HEAD请求
   */
  async head<T>(url: string, options?: RequestOptions): Promise<HttpResponse<T>> {
    return this.request<T>('HEAD', url, undefined, options);
  }

  /**
   * 获取流
   */
  async getStream(url: string, options?: RequestOptions): Promise<ReadableStream> {
    const config = this.mergeConfig(options);
    const response = await this.fetchWithRetry(url, {
      method: 'GET',
      headers: config.headers,
      signal: config.signal
    }, config);

    if (!response.ok) {
      throw new HttpError(
        `HTTP ${response.status}: ${response.statusText}`,
        response.status,
        response,
        config
      );
    }

    if (!response.body) {
      throw new Error('响应体为空');
    }

    return response.body;
  }

  /**
   * 上传流
   */
  async postStream(url: string, stream: ReadableStream, options?: RequestOptions): Promise<HttpResponse<void>> {
    const config = this.mergeConfig(options);
    
    const response = await this.fetchWithRetry(url, {
      method: 'POST',
      headers: config.headers,
      body: stream,
      signal: config.signal
    }, config);

    return this.processResponse<void>(response, config);
  }

  /**
   * 添加请求拦截器
   */
  addRequestInterceptor(interceptor: RequestInterceptor): string {
    const id = `req_${++this.interceptorIdCounter}`;
    this.requestInterceptors.set(id, interceptor);
    return id;
  }

  /**
   * 添加响应拦截器
   */
  addResponseInterceptor(interceptor: ResponseInterceptor): string {
    const id = `res_${++this.interceptorIdCounter}`;
    this.responseInterceptors.set(id, interceptor);
    return id;
  }

  /**
   * 移除请求拦截器
   */
  removeRequestInterceptor(id: string): void {
    this.requestInterceptors.delete(id);
  }

  /**
   * 移除响应拦截器
   */
  removeResponseInterceptor(id: string): void {
    this.responseInterceptors.delete(id);
  }

  /**
   * 设置默认配置
   */
  setDefaultConfig(config: Partial<RequestOptions>): void {
    this.defaultConfig = { ...this.defaultConfig, ...config };
  }

  /**
   * 获取默认配置
   */
  getDefaultConfig(): RequestOptions {
    return { ...this.defaultConfig };
  }

  /**
   * 核心请求方法
   */
  private async request<T>(
    method: string, 
    url: string, 
    data?: any, 
    options?: RequestOptions
  ): Promise<HttpResponse<T>> {
    let config = this.mergeConfig(options);
    
    // 应用请求拦截器
    let requestConfig = { ...config, url, method, data };
    for (const interceptor of this.requestInterceptors.values()) {
      requestConfig = await interceptor(requestConfig);
    }

    // 准备请求体
    let body: any = undefined;
    if (requestConfig.data !== undefined) {
      if (typeof requestConfig.data === 'string' || 
          requestConfig.data instanceof FormData ||
          requestConfig.data instanceof ArrayBuffer ||
          requestConfig.data instanceof ReadableStream) {
        body = requestConfig.data;
      } else {
        body = JSON.stringify(requestConfig.data);
        requestConfig.headers = {
          'Content-Type': 'application/json',
          ...requestConfig.headers
        };
      }
    }

    // 发送请求
    const response = await this.fetchWithRetry(requestConfig.url, {
      method: requestConfig.method,
      headers: requestConfig.headers,
      body,
      signal: requestConfig.signal
    }, requestConfig);

    // 处理响应
    let httpResponse = await this.processResponse<T>(response, requestConfig);

    // 应用响应拦截器
    for (const interceptor of this.responseInterceptors.values()) {
      httpResponse = await interceptor(httpResponse);
    }

    return httpResponse;
  }

  /**
   * 带重试的fetch
   */
  private async fetchWithRetry(
    url: string, 
    init: RequestInit, 
    config: RequestOptions
  ): Promise<Response> {
    let lastError: Error;
    
    for (let attempt = 0; attempt <= (config.retryCount || 0); attempt++) {
      try {
        // 设置超时
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), config.timeout);
        
        const response = await fetch(url, {
          ...init,
          signal: config.signal || controller.signal
        });
        
        clearTimeout(timeoutId);
        
        // 如果是第一次尝试或者响应成功，直接返回
        if (attempt === 0 || response.ok) {
          return response;
        }
        
        // 如果是客户端错误（4xx），不重试
        if (response.status >= 400 && response.status < 500) {
          return response;
        }
        
        lastError = new Error(`HTTP ${response.status}: ${response.statusText}`);
      } catch (error) {
        lastError = error as Error;
        
        // 如果是最后一次尝试，抛出错误
        if (attempt === (config.retryCount || 0)) {
          throw lastError;
        }
        
        // 等待后重试
        if (config.retryDelay && config.retryDelay > 0) {
          await this.delay(config.retryDelay * Math.pow(2, attempt));
        }
      }
    }
    
    throw lastError!;
  }

  /**
   * 处理响应
   */
  private async processResponse<T>(response: Response, config: RequestOptions): Promise<HttpResponse<T>> {
    if (!response.ok) {
      throw new HttpError(
        `HTTP ${response.status}: ${response.statusText}`,
        response.status,
        response,
        config
      );
    }

    // 解析响应头
    const headers: Record<string, string> = {};
    response.headers.forEach((value, key) => {
      headers[key] = value;
    });

    // 解析响应体
    let data: any;
    const responseType = config.responseType || 'json';
    
    try {
      switch (responseType) {
        case 'json':
          data = await response.json();
          break;
        case 'text':
          data = await response.text();
          break;
        case 'blob':
          data = await response.blob();
          break;
        case 'arraybuffer':
          data = await response.arrayBuffer();
          break;
        default:
          data = await response.text();
      }
    } catch (error) {
      throw new Error(`解析响应失败: ${error.message}`);
    }

    return {
      data,
      status: response.status,
      statusText: response.statusText,
      headers,
      config
    };
  }

  /**
   * 合并配置
   */
  private mergeConfig(options?: RequestOptions): RequestOptions {
    return {
      ...this.defaultConfig,
      ...options,
      headers: {
        ...this.defaultConfig.headers,
        ...options?.headers
      }
    };
  }

  /**
   * 延迟函数
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
```

### 步骤3：创建请求拦截器
**文件**: `src/infrastructure/network/RequestInterceptor.ts`

```typescript
import { RequestInterceptor } from '../interfaces/IHttpClient';

/**
 * 认证拦截器
 */
export const createAuthInterceptor = (getToken: () => string | null): RequestInterceptor => {
  return (config) => {
    const token = getToken();
    if (token) {
      config.headers = {
        ...config.headers,
        'Authorization': `Bearer ${token}`
      };
    }
    return config;
  };
};

/**
 * 日志拦截器
 */
export const createLoggingInterceptor = (): RequestInterceptor => {
  return (config) => {
    console.log(`[HTTP] ${config.method} ${config.url}`, {
      headers: config.headers,
      data: config.data
    });
    return config;
  };
};

/**
 * 用户代理拦截器
 */
export const createUserAgentInterceptor = (userAgent: string): RequestInterceptor => {
  return (config) => {
    config.headers = {
      ...config.headers,
      'User-Agent': userAgent
    };
    return config;
  };
};
```

### 步骤4：创建响应处理器
**文件**: `src/infrastructure/network/ResponseHandler.ts`

```typescript
import { ResponseInterceptor, HttpResponse } from '../interfaces/IHttpClient';

/**
 * 响应日志拦截器
 */
export const createResponseLoggingInterceptor = (): ResponseInterceptor => {
  return <T>(response: HttpResponse<T>) => {
    console.log(`[HTTP] ${response.status} ${response.statusText}`, {
      headers: response.headers,
      data: response.data
    });
    return response;
  };
};

/**
 * 错误处理拦截器
 */
export const createErrorHandlingInterceptor = (): ResponseInterceptor => {
  return <T>(response: HttpResponse<T>) => {
    // 可以在这里添加全局错误处理逻辑
    if (response.status >= 400) {
      console.error(`HTTP错误: ${response.status} ${response.statusText}`, response.data);
    }
    return response;
  };
};

/**
 * 数据转换拦截器
 */
export const createDataTransformInterceptor = <T, R>(
  transform: (data: T) => R
): ResponseInterceptor => {
  return (response: HttpResponse<T>) => {
    return {
      ...response,
      data: transform(response.data)
    } as HttpResponse<R>;
  };
};
```

### 步骤5：创建单元测试
**文件**: `src/infrastructure/network/__tests__/HttpClient.test.ts`

```typescript
import { HttpClient } from '../HttpClient';
import { HttpError } from '../interfaces/IHttpClient';

// Mock fetch
global.fetch = jest.fn();

describe('HttpClient', () => {
  let httpClient: HttpClient;
  let mockFetch: jest.MockedFunction<typeof fetch>;

  beforeEach(() => {
    httpClient = new HttpClient();
    mockFetch = fetch as jest.MockedFunction<typeof fetch>;
    mockFetch.mockClear();
  });

  describe('GET请求', () => {
    test('应该成功发送GET请求', async () => {
      const mockResponse = {
        ok: true,
        status: 200,
        statusText: 'OK',
        headers: new Headers({ 'content-type': 'application/json' }),
        json: jest.fn().mockResolvedValue({ message: 'success' })
      };

      mockFetch.mockResolvedValue(mockResponse as any);

      const response = await httpClient.get('/api/test');

      expect(mockFetch).toHaveBeenCalledWith('/api/test', expect.objectContaining({
        method: 'GET'
      }));
      expect(response.data).toEqual({ message: 'success' });
      expect(response.status).toBe(200);
    });

    test('应该处理HTTP错误', async () => {
      const mockResponse = {
        ok: false,
        status: 404,
        statusText: 'Not Found',
        headers: new Headers()
      };

      mockFetch.mockResolvedValue(mockResponse as any);

      await expect(httpClient.get('/api/notfound')).rejects.toThrow(HttpError);
    });
  });

  describe('拦截器', () => {
    test('应该应用请求拦截器', async () => {
      const mockResponse = {
        ok: true,
        status: 200,
        statusText: 'OK',
        headers: new Headers(),
        json: jest.fn().mockResolvedValue({})
      };

      mockFetch.mockResolvedValue(mockResponse as any);

      // 添加请求拦截器
      httpClient.addRequestInterceptor((config) => {
        config.headers = {
          ...config.headers,
          'X-Custom-Header': 'test-value'
        };
        return config;
      });

      await httpClient.get('/api/test');

      expect(mockFetch).toHaveBeenCalledWith('/api/test', expect.objectContaining({
        headers: expect.objectContaining({
          'X-Custom-Header': 'test-value'
        })
      }));
    });
  });
});
```

## ✅ 验收标准

### 功能验收
- [ ] HttpClient类创建成功
- [ ] 实现了IHttpClient接口的所有方法
- [ ] 支持请求和响应拦截器
- [ ] 支持重试机制和超时控制
- [ ] 错误处理机制完善

### 质量验收
- [ ] TypeScript编译无错误
- [ ] 单元测试覆盖率 ≥ 80%
- [ ] 代码符合项目编码规范
- [ ] 无ESLint警告或错误

## 🔄 幂等性保证

### 执行前检查
```bash
#!/bin/bash
echo "🔍 检查任务1.4执行状态..."

# 检查依赖任务
if [ ! -f "src/infrastructure/providers/MinioProvider.ts" ]; then
  echo "❌ 依赖任务Task-1.3未完成，请先执行Task-1.3"
  exit 1
fi

# 检查是否已存在新实现
if [ -f "src/infrastructure/network/HttpClient.ts" ]; then
  echo "⚠️  HttpClient.ts已存在，任务可能已完成"
  echo "请确认是否需要重新执行此任务"
  read -p "是否继续？(y/N): " confirm
  if [ "$confirm" != "y" ]; then
    exit 1
  fi
fi

echo "✅ 前置检查完成，可以执行任务1.4"
```

### 执行后验证
```bash
#!/bin/bash
echo "🔍 验证任务1.4执行结果..."

# 检查文件是否创建成功
files=(
  "src/infrastructure/interfaces/IHttpClient.ts"
  "src/infrastructure/network/HttpClient.ts"
  "src/infrastructure/network/RequestInterceptor.ts"
  "src/infrastructure/network/ResponseHandler.ts"
  "src/infrastructure/network/__tests__/HttpClient.test.ts"
)

all_created=true
for file in "${files[@]}"; do
  if [ -f "$file" ]; then
    echo "✅ $file 创建成功"
  else
    echo "❌ $file 创建失败"
    all_created=false
  fi
done

# TypeScript编译检查
if npm run type-check > /dev/null 2>&1; then
  echo "✅ TypeScript编译检查通过"
else
  echo "❌ TypeScript编译检查失败"
  all_created=false
fi

if [ "$all_created" = true ]; then
  echo "🎉 任务1.4执行成功！"
  exit 0
else
  echo "💥 任务1.4执行失败，请检查错误信息"
  exit 1
fi
```

## 📝 完成确认

任务完成后，请在检查清单中标记：
- [ ] 任务1.4已完成
- [ ] HttpClient创建成功
- [ ] 单元测试通过
- [ ] 准备执行任务1.5

## 🔗 相关链接

- [上一个任务：Task-1.3 重构MinIO提供者](./task-1.3-minio-provider.md)
- [下一个任务：Task-1.5 创建加密服务](./task-1.5-crypto-service.md)
- [重构检查清单](../00-refactoring-checklist.md)
