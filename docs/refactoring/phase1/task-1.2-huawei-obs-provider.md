# 任务1.2：重构华为云OBS提供者

## 📋 任务概述

**任务ID**: Task-1.2  
**任务名称**: 重构华为云OBS提供者  
**所属阶段**: 阶段1 - 基础设施层重构  
**预计时间**: 3小时  
**依赖任务**: Task-1.1 (创建存储提供者接口)  
**后续任务**: Task-1.3 (重构MinIO提供者)  

## 🎯 任务目标

重构现有的华为云OBS提供者，使其实现新的IStorageProvider接口，保持现有功能的同时提升代码质量和可维护性。

## 📋 前置条件检查

### 依赖检查
- [ ] Task-1.1 已完成
- [ ] IStorageProvider接口已定义
- [ ] 华为云OBS SDK已安装 (`esdk-obs-browserjs`)

### 文件检查
- [ ] `src/services/storage/HuaweiObsProvider.js` 存在
- [ ] `src/infrastructure/interfaces/IStorageProvider.ts` 存在

### 状态检查
```bash
# 检查当前华为云OBS提供者状态
if [ -f "src/infrastructure/providers/HuaweiObsProvider.ts" ]; then
  echo "⚠️  新的华为云OBS提供者已存在，请确认是否需要重新创建"
  exit 1
else
  echo "✅ 可以开始重构华为云OBS提供者"
fi
```

## 🛠️ 实施步骤

### 步骤1：创建目录结构
```bash
# 创建提供者目录
mkdir -p src/infrastructure/providers
mkdir -p src/infrastructure/providers/__tests__
```

### 步骤2：创建新的华为云OBS提供者
**文件**: `src/infrastructure/providers/HuaweiObsProvider.ts`

```typescript
import ObsClient from 'esdk-obs-browserjs';
import { BaseStorageProvider } from '../interfaces/IStorageProvider';
import { StorageType } from '../enums/StorageType';
import { IStorageConfig, ICloudStorageConfig } from '../types/StorageConfig';
import { 
  StorageResult, 
  StorageResultFactory,
  ObjectMetadata, 
  GetOptions, 
  PutOptions, 
  ListOptions, 
  SignedUrlOptions 
} from '../types/StorageResult';

/**
 * 华为云OBS存储提供者
 * 实现IStorageProvider接口，提供华为云对象存储服务
 */
export class HuaweiObsProvider extends BaseStorageProvider {
  private obsClient: ObsClient | null = null;
  private bucketName: string = '';
  
  constructor() {
    super('华为云OBS', StorageType.HUAWEI_OBS);
  }
  
  /**
   * 初始化华为云OBS客户端
   */
  async initialize(config: IStorageConfig): Promise<void> {
    try {
      const obsConfig = config as ICloudStorageConfig;
      
      // 验证配置参数
      this.validateConfig(obsConfig);
      
      // 创建OBS客户端
      this.obsClient = new ObsClient({
        access_key_id: obsConfig.accessKey,
        secret_access_key: obsConfig.secretKey,
        server: obsConfig.endpoint,
        timeout: obsConfig.timeout || 30000,
        max_retry_count: obsConfig.retryCount || 3
      });
      
      this.bucketName = obsConfig.bucketName;
      this.config = config;
      this.initialized = true;
      
      console.log(`华为云OBS提供者初始化成功: ${this.bucketName}`);
    } catch (error) {
      this.initialized = false;
      throw new Error(`华为云OBS初始化失败: ${error.message}`);
    }
  }
  
  /**
   * 释放资源
   */
  async dispose(): Promise<void> {
    if (this.obsClient) {
      this.obsClient = null;
    }
    this.initialized = false;
    console.log('华为云OBS提供者已释放');
  }
  
  /**
   * 测试连接
   */
  async testConnection(): Promise<boolean> {
    if (!this.obsClient) {
      return false;
    }
    
    try {
      const result = await this.obsClient.headBucket({
        Bucket: this.bucketName
      });
      return result.CommonMsg.Status === 200;
    } catch (error) {
      console.error('华为云OBS连接测试失败:', error);
      return false;
    }
  }
  
  /**
   * 获取对象
   */
  async get(key: string, options?: GetOptions): Promise<StorageResult<any>> {
    if (!this.obsClient) {
      return StorageResultFactory.failure(new Error('OBS客户端未初始化'));
    }
    
    try {
      const params: any = {
        Bucket: this.bucketName,
        Key: key
      };
      
      // 处理范围请求
      if (options?.range) {
        params.Range = `bytes=${options.range.start}-${options.range.end}`;
      }
      
      // 设置响应类型
      if (options?.saveByType) {
        params.SaveByType = options.saveByType;
      }
      
      const result = await this.obsClient.getObject(params);
      
      if (result.CommonMsg.Status === 200) {
        return StorageResultFactory.success(result.InterfaceResult.Content, {
          contentType: result.InterfaceResult.ContentType,
          lastModified: result.InterfaceResult.LastModified,
          etag: result.InterfaceResult.ETag
        });
      } else {
        return StorageResultFactory.failure(new Error(`获取对象失败: ${result.CommonMsg.Code}`));
      }
    } catch (error) {
      return StorageResultFactory.failure(error);
    }
  }
  
  /**
   * 上传对象
   */
  async put(key: string, data: any, options?: PutOptions): Promise<StorageResult<void>> {
    if (!this.obsClient) {
      return StorageResultFactory.failure(new Error('OBS客户端未初始化'));
    }
    
    try {
      const params: any = {
        Bucket: this.bucketName,
        Key: key,
        Body: data
      };
      
      // 设置内容类型
      if (options?.contentType) {
        params.ContentType = options.contentType;
      }
      
      // 设置元数据
      if (options?.metadata) {
        params.Metadata = options.metadata;
      }
      
      const result = await this.obsClient.putObject(params);
      
      if (result.CommonMsg.Status === 200) {
        return StorageResultFactory.success(undefined, {
          etag: result.InterfaceResult.ETag
        });
      } else {
        return StorageResultFactory.failure(new Error(`上传对象失败: ${result.CommonMsg.Code}`));
      }
    } catch (error) {
      return StorageResultFactory.failure(error);
    }
  }
  
  /**
   * 删除对象
   */
  async delete(key: string): Promise<StorageResult<void>> {
    if (!this.obsClient) {
      return StorageResultFactory.failure(new Error('OBS客户端未初始化'));
    }
    
    try {
      const result = await this.obsClient.deleteObject({
        Bucket: this.bucketName,
        Key: key
      });
      
      if (result.CommonMsg.Status === 204) {
        return StorageResultFactory.success();
      } else {
        return StorageResultFactory.failure(new Error(`删除对象失败: ${result.CommonMsg.Code}`));
      }
    } catch (error) {
      return StorageResultFactory.failure(error);
    }
  }
  
  /**
   * 列出对象
   */
  async list(prefix?: string, options?: ListOptions): Promise<StorageResult<string[]>> {
    if (!this.obsClient) {
      return StorageResultFactory.failure(new Error('OBS客户端未初始化'));
    }
    
    try {
      const params: any = {
        Bucket: this.bucketName,
        MaxKeys: options?.maxKeys || 1000
      };
      
      if (prefix) {
        params.Prefix = prefix;
      }
      
      if (options?.marker) {
        params.Marker = options.marker;
      }
      
      if (options?.delimiter) {
        params.Delimiter = options.delimiter;
      }
      
      const result = await this.obsClient.listObjects(params);
      
      if (result.CommonMsg.Status === 200) {
        const keys = result.InterfaceResult.Contents?.map((obj: any) => obj.Key) || [];
        return StorageResultFactory.success(keys);
      } else {
        return StorageResultFactory.failure(new Error(`列出对象失败: ${result.CommonMsg.Code}`));
      }
    } catch (error) {
      return StorageResultFactory.failure(error);
    }
  }
  
  /**
   * 批量获取对象
   */
  async getBatch(keys: string[]): Promise<StorageResult<Record<string, any>>> {
    const results: Record<string, any> = {};
    const errors: string[] = [];

    for (const key of keys) {
      try {
        const result = await this.get(key);
        if (result.success) {
          results[key] = result.data;
        } else {
          errors.push(`${key}: ${result.error?.message}`);
        }
      } catch (error) {
        errors.push(`${key}: ${error.message}`);
      }
    }

    if (errors.length > 0) {
      return StorageResultFactory.failure(new Error(`批量获取部分失败: ${errors.join(', ')}`), { results });
    }

    return StorageResultFactory.success(results);
  }

  /**
   * 批量上传对象
   */
  async putBatch(items: Record<string, any>): Promise<StorageResult<void>> {
    const errors: string[] = [];

    for (const [key, data] of Object.entries(items)) {
      try {
        const result = await this.put(key, data);
        if (!result.success) {
          errors.push(`${key}: ${result.error?.message}`);
        }
      } catch (error) {
        errors.push(`${key}: ${error.message}`);
      }
    }

    if (errors.length > 0) {
      return StorageResultFactory.failure(new Error(`批量上传部分失败: ${errors.join(', ')}`));
    }

    return StorageResultFactory.success();
  }

  /**
   * 批量删除对象
   */
  async deleteBatch(keys: string[]): Promise<StorageResult<void>> {
    if (!this.obsClient) {
      return StorageResultFactory.failure(new Error('OBS客户端未初始化'));
    }

    try {
      const deleteObjects = keys.map(key => ({ Key: key }));

      const result = await this.obsClient.deleteObjects({
        Bucket: this.bucketName,
        Delete: {
          Objects: deleteObjects
        }
      });

      if (result.CommonMsg.Status === 200) {
        return StorageResultFactory.success();
      } else {
        return StorageResultFactory.failure(new Error(`批量删除失败: ${result.CommonMsg.Code}`));
      }
    } catch (error) {
      return StorageResultFactory.failure(error);
    }
  }

  /**
   * 获取对象元数据
   */
  async getMetadata(key: string): Promise<StorageResult<ObjectMetadata>> {
    if (!this.obsClient) {
      return StorageResultFactory.failure(new Error('OBS客户端未初始化'));
    }

    try {
      const result = await this.obsClient.headObject({
        Bucket: this.bucketName,
        Key: key
      });

      if (result.CommonMsg.Status === 200) {
        const metadata: ObjectMetadata = {
          size: parseInt(result.InterfaceResult.ContentLength),
          lastModified: new Date(result.InterfaceResult.LastModified),
          etag: result.InterfaceResult.ETag,
          contentType: result.InterfaceResult.ContentType,
          customMetadata: result.InterfaceResult.Metadata
        };

        return StorageResultFactory.success(metadata);
      } else {
        return StorageResultFactory.failure(new Error(`获取元数据失败: ${result.CommonMsg.Code}`));
      }
    } catch (error) {
      return StorageResultFactory.failure(error);
    }
  }

  /**
   * 获取对象流
   */
  async getStream(key: string): Promise<ReadableStream> {
    throw new Error('华为云OBS暂不支持流式读取');
  }

  /**
   * 上传对象流
   */
  async putStream(key: string, stream: ReadableStream): Promise<StorageResult<void>> {
    try {
      const reader = stream.getReader();
      const chunks: Uint8Array[] = [];

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;
        chunks.push(value);
      }

      // 合并所有块
      const totalLength = chunks.reduce((sum, chunk) => sum + chunk.length, 0);
      const result = new Uint8Array(totalLength);
      let offset = 0;

      for (const chunk of chunks) {
        result.set(chunk, offset);
        offset += chunk.length;
      }

      return await this.put(key, result);
    } catch (error) {
      return StorageResultFactory.failure(error);
    }
  }

  /**
   * 生成签名URL
   */
  async getSignedUrl(key: string, options?: SignedUrlOptions): Promise<string> {
    if (!this.obsClient) {
      throw new Error('OBS客户端未初始化');
    }

    const params: any = {
      Bucket: this.bucketName,
      Key: key,
      Expires: options?.expires || 3600,
      Method: options?.method || 'GET'
    };

    return this.obsClient.createSignedUrlSync(params);
  }

  /**
   * 更新配置
   */
  async updateConfig(config: Partial<IStorageConfig>): Promise<void> {
    const newConfig = { ...this.config, ...config };
    await this.dispose();
    await this.initialize(newConfig);
  }

  /**
   * 验证配置参数
   */
  private validateConfig(config: ICloudStorageConfig): void {
    if (!config.accessKey) {
      throw new Error('accessKey不能为空');
    }
    if (!config.secretKey) {
      throw new Error('secretKey不能为空');
    }
    if (!config.endpoint) {
      throw new Error('endpoint不能为空');
    }
    if (!config.bucketName) {
      throw new Error('bucketName不能为空');
    }
  }
}
```

### 步骤3：创建单元测试
**文件**: `src/infrastructure/providers/__tests__/HuaweiObsProvider.test.ts`

```typescript
import { HuaweiObsProvider } from '../HuaweiObsProvider';
import { StorageConfigFactory } from '../../types/StorageConfig';
import { StorageType } from '../../enums/StorageType';

// Mock华为云OBS SDK
jest.mock('esdk-obs-browserjs');

describe('HuaweiObsProvider', () => {
  let provider: HuaweiObsProvider;
  let mockConfig: any;

  beforeEach(() => {
    provider = new HuaweiObsProvider();
    mockConfig = StorageConfigFactory.createHuaweiObsConfig({
      name: 'test-obs',
      endpoint: 'https://obs.cn-north-4.myhuaweicloud.com',
      region: 'cn-north-4',
      accessKey: 'test-access-key',
      secretKey: 'test-secret-key',
      bucketName: 'test-bucket'
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('基本属性', () => {
    test('应该有正确的名称和类型', () => {
      expect(provider.name).toBe('华为云OBS');
      expect(provider.type).toBe(StorageType.HUAWEI_OBS);
      expect(provider.isInitialized).toBe(false);
    });
  });

  describe('初始化', () => {
    test('应该成功初始化', async () => {
      await provider.initialize(mockConfig);
      expect(provider.isInitialized).toBe(true);
    });

    test('配置参数缺失时应该抛出错误', async () => {
      const invalidConfig = { ...mockConfig, accessKey: '' };
      await expect(provider.initialize(invalidConfig)).rejects.toThrow('accessKey不能为空');
    });
  });

  describe('连接测试', () => {
    test('未初始化时应该返回false', async () => {
      const result = await provider.testConnection();
      expect(result).toBe(false);
    });
  });
});
```

### 步骤4：创建迁移脚本
**文件**: `scripts/migrate-huawei-obs-provider.js`

```javascript
/**
 * 华为云OBS提供者迁移脚本
 */
const fs = require('fs');
const path = require('path');

const filesToUpdate = [
  'src/services/storage/StorageManager.js',
  'src/services/CloudStorageService.js',
  'src/services/OssStorageService.js'
];

function updateImports(filePath) {
  if (!fs.existsSync(filePath)) {
    console.log(`文件不存在: ${filePath}`);
    return;
  }

  let content = fs.readFileSync(filePath, 'utf8');

  // 更新import语句
  content = content.replace(
    /import.*HuaweiObsProvider.*from.*['"].*\/storage\/HuaweiObsProvider.*['"];?/g,
    "import { HuaweiObsProvider } from '../infrastructure/providers/HuaweiObsProvider';"
  );

  fs.writeFileSync(filePath, content, 'utf8');
  console.log(`已更新: ${filePath}`);
}

// 执行迁移
console.log('开始迁移华为云OBS提供者引用...');
filesToUpdate.forEach(updateImports);
console.log('迁移完成！');
```

## ✅ 验收标准

### 功能验收
- [ ] 新的HuaweiObsProvider类创建成功
- [ ] 实现了IStorageProvider接口的所有方法
- [ ] 保持了现有功能的兼容性
- [ ] 错误处理机制完善

### 质量验收
- [ ] TypeScript编译无错误
- [ ] 单元测试覆盖率 ≥ 80%
- [ ] 代码符合项目编码规范
- [ ] 无ESLint警告或错误

### 性能验收
- [ ] 初始化时间 < 1秒
- [ ] 基础操作响应时间与原实现相当
- [ ] 内存使用无明显增加

## 🔄 幂等性保证

### 执行前检查
```bash
#!/bin/bash
echo "🔍 检查任务1.2执行状态..."

# 检查依赖任务
if [ ! -f "src/infrastructure/interfaces/IStorageProvider.ts" ]; then
  echo "❌ 依赖任务Task-1.1未完成，请先执行Task-1.1"
  exit 1
fi

# 检查是否已存在新实现
if [ -f "src/infrastructure/providers/HuaweiObsProvider.ts" ]; then
  echo "⚠️  HuaweiObsProvider.ts已存在，任务可能已完成"
  echo "请确认是否需要重新执行此任务"
  read -p "是否继续？(y/N): " confirm
  if [ "$confirm" != "y" ]; then
    exit 1
  fi
fi

echo "✅ 前置检查完成，可以执行任务1.2"
```

### 执行后验证
```bash
#!/bin/bash
echo "🔍 验证任务1.2执行结果..."

# 检查文件是否创建成功
files=(
  "src/infrastructure/providers/HuaweiObsProvider.ts"
  "src/infrastructure/providers/__tests__/HuaweiObsProvider.test.ts"
  "scripts/migrate-huawei-obs-provider.js"
)

all_created=true
for file in "${files[@]}"; do
  if [ -f "$file" ]; then
    echo "✅ $file 创建成功"
  else
    echo "❌ $file 创建失败"
    all_created=false
  fi
done

# TypeScript编译检查
if npm run type-check > /dev/null 2>&1; then
  echo "✅ TypeScript编译检查通过"
else
  echo "❌ TypeScript编译检查失败"
  all_created=false
fi

# 运行单元测试
if npm test -- HuaweiObsProvider.test.ts > /dev/null 2>&1; then
  echo "✅ 单元测试通过"
else
  echo "❌ 单元测试失败"
  all_created=false
fi

if [ "$all_created" = true ]; then
  echo "🎉 任务1.2执行成功！"
  exit 0
else
  echo "💥 任务1.2执行失败，请检查错误信息"
  exit 1
fi
```

## 📝 完成确认

任务完成后，请在检查清单中标记：
- [ ] 任务1.2已完成
- [ ] 新的HuaweiObsProvider创建成功
- [ ] 单元测试通过
- [ ] 准备执行任务1.3

## 🔗 相关链接

- [上一个任务：Task-1.1 创建存储提供者接口](./task-1.1-storage-provider-interfaces.md)
- [下一个任务：Task-1.3 重构MinIO提供者](./task-1.3-minio-provider.md)
- [重构检查清单](../00-refactoring-checklist.md)
