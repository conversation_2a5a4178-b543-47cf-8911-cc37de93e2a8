# 任务1.6：创建工具服务

## 📋 任务概述

**任务ID**: Task-1.6
**任务名称**: 创建工具服务
**所属阶段**: 阶段1 - 基础设施层重构
**预计时间**: 2小时
**依赖任务**: Task-1.5 (创建加密服务)
**后续任务**: Task-1.7 (创建基础设施层测试页面)

## 🎯 任务目标

创建基础工具服务，包括日志记录器、事件发射器、数据验证器和日期处理工具，为上层提供通用功能支持。

## 📋 前置条件检查

### 依赖检查
- [ ] Task-1.5 已完成
- [ ] 工具接口已定义

### 文件检查
- [ ] `src/infrastructure/crypto/AESCryptoService.ts` 存在

### 状态检查
```bash
# 检查工具服务是否已存在
if [ -f "src/infrastructure/utils/Logger.ts" ]; then
  echo "⚠️  工具服务已存在，请确认是否需要重新创建"
  exit 1
else
  echo "✅ 可以开始创建工具服务"
fi
```

## 🛠️ 实施步骤

### 步骤1：创建日志记录器
**文件**: `src/infrastructure/utils/Logger.ts`

```typescript
/**
 * 日志级别枚举
 */
export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3
}

/**
 * 日志记录器接口
 */
export interface ILogger {
  debug(message: string, ...args: any[]): void;
  info(message: string, ...args: any[]): void;
  warn(message: string, ...args: any[]): void;
  error(message: string, ...args: any[]): void;

  setLevel(level: LogLevel): void;
  getLevel(): LogLevel;

  addAppender(appender: ILogAppender): void;
  removeAppender(appender: ILogAppender): void;
}

/**
 * 日志输出器接口
 */
export interface ILogAppender {
  append(level: LogLevel, message: string, timestamp: Date, ...args: any[]): void;
}

/**
 * 控制台日志输出器
 */
export class ConsoleAppender implements ILogAppender {
  append(level: LogLevel, message: string, timestamp: Date, ...args: any[]): void {
    const timeStr = timestamp.toISOString();
    const levelStr = LogLevel[level].padEnd(5);
    const fullMessage = `[${timeStr}] ${levelStr} ${message}`;

    switch (level) {
      case LogLevel.DEBUG:
        console.debug(fullMessage, ...args);
        break;
      case LogLevel.INFO:
        console.info(fullMessage, ...args);
        break;
      case LogLevel.WARN:
        console.warn(fullMessage, ...args);
        break;
      case LogLevel.ERROR:
        console.error(fullMessage, ...args);
        break;
    }
  }
}

/**
 * 内存日志输出器
 */
export class MemoryAppender implements ILogAppender {
  private logs: LogEntry[] = [];
  private maxSize: number;

  constructor(maxSize: number = 1000) {
    this.maxSize = maxSize;
  }

  append(level: LogLevel, message: string, timestamp: Date, ...args: any[]): void {
    this.logs.push({
      level,
      message,
      timestamp,
      args
    });

    // 保持日志数量在限制内
    if (this.logs.length > this.maxSize) {
      this.logs.shift();
    }
  }

  getLogs(): LogEntry[] {
    return [...this.logs];
  }

  clear(): void {
    this.logs = [];
  }
}

/**
 * 日志条目
 */
export interface LogEntry {
  level: LogLevel;
  message: string;
  timestamp: Date;
  args: any[];
}

/**
 * 默认日志记录器实现
 */
export class Logger implements ILogger {
  private level: LogLevel = LogLevel.INFO;
  private appenders: ILogAppender[] = [];

  constructor() {
    // 默认添加控制台输出器
    this.addAppender(new ConsoleAppender());
  }

  debug(message: string, ...args: any[]): void {
    this.log(LogLevel.DEBUG, message, ...args);
  }

  info(message: string, ...args: any[]): void {
    this.log(LogLevel.INFO, message, ...args);
  }

  warn(message: string, ...args: any[]): void {
    this.log(LogLevel.WARN, message, ...args);
  }

  error(message: string, ...args: any[]): void {
    this.log(LogLevel.ERROR, message, ...args);
  }

  setLevel(level: LogLevel): void {
    this.level = level;
  }

  getLevel(): LogLevel {
    return this.level;
  }

  addAppender(appender: ILogAppender): void {
    this.appenders.push(appender);
  }

  removeAppender(appender: ILogAppender): void {
    const index = this.appenders.indexOf(appender);
    if (index > -1) {
      this.appenders.splice(index, 1);
    }
  }

  private log(level: LogLevel, message: string, ...args: any[]): void {
    if (level >= this.level) {
      const timestamp = new Date();
      this.appenders.forEach(appender => {
        appender.append(level, message, timestamp, ...args);
      });
    }
  }
}

/**
 * 全局日志记录器实例
 */
export const logger = new Logger();
```

### 步骤2：创建事件发射器
**文件**: `src/infrastructure/utils/EventEmitter.ts`

```typescript
/**
 * 事件处理器类型
 */
export type EventHandler<T = any> = (data: T) => void | Promise<void>;

/**
 * 事件订阅
 */
export interface EventSubscription {
  unsubscribe(): void;
}

/**
 * 事件发射器接口
 */
export interface IEventEmitter {
  // 事件订阅
  on<T>(event: string, handler: EventHandler<T>): EventSubscription;
  once<T>(event: string, handler: EventHandler<T>): EventSubscription;
  off(event: string, handler?: EventHandler<any>): void;

  // 事件发布
  emit<T>(event: string, data?: T): void;

  // 事件管理
  clear(): void;
  getListeners(event: string): EventHandler<any>[];
  hasListeners(event: string): boolean;
}

/**
 * 事件监听器信息
 */
interface ListenerInfo {
  handler: EventHandler<any>;
  once: boolean;
}

/**
 * 默认事件发射器实现
 */
export class EventEmitter implements IEventEmitter {
  private listeners: Map<string, ListenerInfo[]> = new Map();

  /**
   * 订阅事件
   */
  on<T>(event: string, handler: EventHandler<T>): EventSubscription {
    return this.addListener(event, handler, false);
  }

  /**
   * 订阅一次性事件
   */
  once<T>(event: string, handler: EventHandler<T>): EventSubscription {
    return this.addListener(event, handler, true);
  }

  /**
   * 取消订阅
   */
  off(event: string, handler?: EventHandler<any>): void {
    if (!this.listeners.has(event)) {
      return;
    }

    if (!handler) {
      // 移除所有监听器
      this.listeners.delete(event);
      return;
    }

    const listeners = this.listeners.get(event)!;
    const index = listeners.findIndex(listener => listener.handler === handler);

    if (index > -1) {
      listeners.splice(index, 1);

      // 如果没有监听器了，删除事件
      if (listeners.length === 0) {
        this.listeners.delete(event);
      }
    }
  }

  /**
   * 发射事件
   */
  emit<T>(event: string, data?: T): void {
    if (!this.listeners.has(event)) {
      return;
    }

    const listeners = this.listeners.get(event)!.slice(); // 复制数组避免修改问题

    for (const listener of listeners) {
      try {
        const result = listener.handler(data);

        // 处理异步处理器
        if (result instanceof Promise) {
          result.catch(error => {
            console.error(`事件处理器错误 [${event}]:`, error);
          });
        }

        // 移除一次性监听器
        if (listener.once) {
          this.off(event, listener.handler);
        }
      } catch (error) {
        console.error(`事件处理器错误 [${event}]:`, error);
      }
    }
  }

  /**
   * 清除所有监听器
   */
  clear(): void {
    this.listeners.clear();
  }

  /**
   * 获取事件监听器
   */
  getListeners(event: string): EventHandler<any>[] {
    const listeners = this.listeners.get(event);
    return listeners ? listeners.map(l => l.handler) : [];
  }

  /**
   * 检查是否有监听器
   */
  hasListeners(event: string): boolean {
    const listeners = this.listeners.get(event);
    return listeners ? listeners.length > 0 : false;
  }

  /**
   * 添加监听器
   */
  private addListener<T>(event: string, handler: EventHandler<T>, once: boolean): EventSubscription {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }

    const listeners = this.listeners.get(event)!;
    listeners.push({ handler, once });

    return {
      unsubscribe: () => this.off(event, handler)
    };
  }
}

/**
 * 全局事件发射器实例
 */
export const eventBus = new EventEmitter();
```

### 步骤3：创建数据验证器
**文件**: `src/infrastructure/utils/Validator.ts`

```typescript
/**
 * 自定义验证器函数
 */
export type CustomValidator = (value: any) => boolean;

/**
 * 验证规则
 */
export interface ValidationRule {
  type: 'required' | 'string' | 'number' | 'boolean' | 'array' | 'object' |
        'email' | 'url' | 'uuid' | 'minLength' | 'maxLength' | 'min' | 'max' | 'custom';
  value?: any;
  message?: string;
  validator?: CustomValidator;
}

/**
 * 验证模式
 */
export interface ValidationSchema {
  [key: string]: ValidationRule[];
}

/**
 * 验证错误
 */
export interface ValidationError {
  field: string;
  message: string;
  value: any;
}

/**
 * 验证结果
 */
export interface ValidationResult {
  valid: boolean;
  errors: ValidationError[];
}

/**
 * 数据验证器接口
 */
export interface IValidator {
  // 基础验证
  isRequired(value: any): boolean;
  isString(value: any): boolean;
  isNumber(value: any): boolean;
  isBoolean(value: any): boolean;
  isArray(value: any): boolean;
  isObject(value: any): boolean;

  // 格式验证
  isEmail(value: string): boolean;
  isUrl(value: string): boolean;
  isUuid(value: string): boolean;

  // 范围验证
  minLength(value: string, min: number): boolean;
  maxLength(value: string, max: number): boolean;
  min(value: number, min: number): boolean;
  max(value: number, max: number): boolean;

  // 自定义验证
  custom(value: any, validator: CustomValidator): boolean;

  // 批量验证
  validate(data: any, schema: ValidationSchema): ValidationResult;
}

/**
 * 默认验证器实现
 */
export class Validator implements IValidator {
  /**
   * 检查值是否存在
   */
  isRequired(value: any): boolean {
    return value !== null && value !== undefined && value !== '';
  }

  /**
   * 检查是否为字符串
   */
  isString(value: any): boolean {
    return typeof value === 'string';
  }

  /**
   * 检查是否为数字
   */
  isNumber(value: any): boolean {
    return typeof value === 'number' && !isNaN(value);
  }

  /**
   * 检查是否为布尔值
   */
  isBoolean(value: any): boolean {
    return typeof value === 'boolean';
  }

  /**
   * 检查是否为数组
   */
  isArray(value: any): boolean {
    return Array.isArray(value);
  }

  /**
   * 检查是否为对象
   */
  isObject(value: any): boolean {
    return value !== null && typeof value === 'object' && !Array.isArray(value);
  }

  /**
   * 检查是否为有效邮箱
   */
  isEmail(value: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(value);
  }

  /**
   * 检查是否为有效URL
   */
  isUrl(value: string): boolean {
    try {
      new URL(value);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * 检查是否为有效UUID
   */
  isUuid(value: string): boolean {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return uuidRegex.test(value);
  }

  /**
   * 检查最小长度
   */
  minLength(value: string, min: number): boolean {
    return this.isString(value) && value.length >= min;
  }

  /**
   * 检查最大长度
   */
  maxLength(value: string, max: number): boolean {
    return this.isString(value) && value.length <= max;
  }

  /**
   * 检查最小值
   */
  min(value: number, min: number): boolean {
    return this.isNumber(value) && value >= min;
  }

  /**
   * 检查最大值
   */
  max(value: number, max: number): boolean {
    return this.isNumber(value) && value <= max;
  }

  /**
   * 自定义验证
   */
  custom(value: any, validator: CustomValidator): boolean {
    return validator(value);
  }

  /**
   * 批量验证
   */
  validate(data: any, schema: ValidationSchema): ValidationResult {
    const errors: ValidationError[] = [];

    for (const [field, rules] of Object.entries(schema)) {
      const value = data[field];

      for (const rule of rules) {
        const isValid = this.validateRule(value, rule);

        if (!isValid) {
          errors.push({
            field,
            message: rule.message || this.getDefaultMessage(rule.type, rule.value),
            value
          });
          break; // 一个字段遇到第一个错误就停止
        }
      }
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * 验证单个规则
   */
  private validateRule(value: any, rule: ValidationRule): boolean {
    switch (rule.type) {
      case 'required':
        return this.isRequired(value);
      case 'string':
        return this.isString(value);
      case 'number':
        return this.isNumber(value);
      case 'boolean':
        return this.isBoolean(value);
      case 'array':
        return this.isArray(value);
      case 'object':
        return this.isObject(value);
      case 'email':
        return this.isEmail(value);
      case 'url':
        return this.isUrl(value);
      case 'uuid':
        return this.isUuid(value);
      case 'minLength':
        return this.minLength(value, rule.value);
      case 'maxLength':
        return this.maxLength(value, rule.value);
      case 'min':
        return this.min(value, rule.value);
      case 'max':
        return this.max(value, rule.value);
      case 'custom':
        return rule.validator ? this.custom(value, rule.validator) : true;
      default:
        return true;
    }
  }

  /**
   * 获取默认错误消息
   */
  private getDefaultMessage(type: string, value?: any): string {
    const messages: Record<string, string> = {
      required: '此字段为必填项',
      string: '必须是字符串',
      number: '必须是数字',
      boolean: '必须是布尔值',
      array: '必须是数组',
      object: '必须是对象',
      email: '必须是有效的邮箱地址',
      url: '必须是有效的URL',
      uuid: '必须是有效的UUID',
      minLength: `长度不能少于${value}个字符`,
      maxLength: `长度不能超过${value}个字符`,
      min: `值不能小于${value}`,
      max: `值不能大于${value}`,
      custom: '验证失败'
    };

    return messages[type] || '验证失败';
  }
}

/**
 * 全局验证器实例
 */
export const validator = new Validator();
```

### 步骤4：创建日期处理工具
**文件**: `src/infrastructure/utils/DateHelper.ts`

```typescript
/**
 * 日期格式化选项
 */
export interface DateFormatOptions {
  locale?: string;
  timeZone?: string;
  dateStyle?: 'full' | 'long' | 'medium' | 'short';
  timeStyle?: 'full' | 'long' | 'medium' | 'short';
}

/**
 * 日期处理工具类
 */
export class DateHelper {
  /**
   * 格式化日期
   */
  static format(date: Date, format?: string, options?: DateFormatOptions): string {
    if (format) {
      return this.formatWithPattern(date, format);
    }

    return new Intl.DateTimeFormat(options?.locale || 'zh-CN', {
      timeZone: options?.timeZone,
      dateStyle: options?.dateStyle,
      timeStyle: options?.timeStyle
    }).format(date);
  }

  /**
   * 使用模式格式化日期
   */
  private static formatWithPattern(date: Date, pattern: string): string {
    const map: Record<string, string> = {
      'YYYY': date.getFullYear().toString(),
      'MM': (date.getMonth() + 1).toString().padStart(2, '0'),
      'DD': date.getDate().toString().padStart(2, '0'),
      'HH': date.getHours().toString().padStart(2, '0'),
      'mm': date.getMinutes().toString().padStart(2, '0'),
      'ss': date.getSeconds().toString().padStart(2, '0'),
      'SSS': date.getMilliseconds().toString().padStart(3, '0')
    };

    return pattern.replace(/YYYY|MM|DD|HH|mm|ss|SSS/g, match => map[match]);
  }

  /**
   * 解析日期字符串
   */
  static parse(dateString: string): Date {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) {
      throw new Error(`无效的日期字符串: ${dateString}`);
    }
    return date;
  }

  /**
   * 获取相对时间描述
   */
  static getRelativeTime(date: Date, baseDate: Date = new Date()): string {
    const diffMs = baseDate.getTime() - date.getTime();
    const diffSeconds = Math.floor(diffMs / 1000);
    const diffMinutes = Math.floor(diffSeconds / 60);
    const diffHours = Math.floor(diffMinutes / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffSeconds < 60) {
      return '刚刚';
    } else if (diffMinutes < 60) {
      return `${diffMinutes}分钟前`;
    } else if (diffHours < 24) {
      return `${diffHours}小时前`;
    } else if (diffDays < 7) {
      return `${diffDays}天前`;
    } else {
      return this.format(date, 'YYYY-MM-DD');
    }
  }

  /**
   * 添加时间
   */
  static add(date: Date, amount: number, unit: 'years' | 'months' | 'days' | 'hours' | 'minutes' | 'seconds'): Date {
    const result = new Date(date);

    switch (unit) {
      case 'years':
        result.setFullYear(result.getFullYear() + amount);
        break;
      case 'months':
        result.setMonth(result.getMonth() + amount);
        break;
      case 'days':
        result.setDate(result.getDate() + amount);
        break;
      case 'hours':
        result.setHours(result.getHours() + amount);
        break;
      case 'minutes':
        result.setMinutes(result.getMinutes() + amount);
        break;
      case 'seconds':
        result.setSeconds(result.getSeconds() + amount);
        break;
    }

    return result;
  }

  /**
   * 检查是否为同一天
   */
  static isSameDay(date1: Date, date2: Date): boolean {
    return date1.getFullYear() === date2.getFullYear() &&
           date1.getMonth() === date2.getMonth() &&
           date1.getDate() === date2.getDate();
  }

  /**
   * 获取日期范围内的所有日期
   */
  static getDateRange(startDate: Date, endDate: Date): Date[] {
    const dates: Date[] = [];
    const currentDate = new Date(startDate);

    while (currentDate <= endDate) {
      dates.push(new Date(currentDate));
      currentDate.setDate(currentDate.getDate() + 1);
    }

    return dates;
  }

  /**
   * 获取月份的第一天
   */
  static getFirstDayOfMonth(date: Date): Date {
    return new Date(date.getFullYear(), date.getMonth(), 1);
  }

  /**
   * 获取月份的最后一天
   */
  static getLastDayOfMonth(date: Date): Date {
    return new Date(date.getFullYear(), date.getMonth() + 1, 0);
  }

  /**
   * 检查是否为有效日期
   */
  static isValid(date: any): boolean {
    return date instanceof Date && !isNaN(date.getTime());
  }
}
```

### 步骤5：创建工具索引文件
**文件**: `src/infrastructure/utils/index.ts`

```typescript
// 日志相关
export * from './Logger';

// 事件相关
export * from './EventEmitter';

// 验证相关
export * from './Validator';

// 日期相关
export * from './DateHelper';
```

### 步骤6：创建单元测试
**文件**: `src/infrastructure/utils/__tests__/Logger.test.ts`

```typescript
import { Logger, LogLevel, MemoryAppender } from '../Logger';

describe('Logger', () => {
  let logger: Logger;
  let memoryAppender: MemoryAppender;

  beforeEach(() => {
    logger = new Logger();
    memoryAppender = new MemoryAppender();
    logger.addAppender(memoryAppender);
  });

  test('应该记录不同级别的日志', () => {
    logger.setLevel(LogLevel.DEBUG);

    logger.debug('Debug message');
    logger.info('Info message');
    logger.warn('Warn message');
    logger.error('Error message');

    const logs = memoryAppender.getLogs();
    expect(logs).toHaveLength(4);
    expect(logs[0].level).toBe(LogLevel.DEBUG);
    expect(logs[1].level).toBe(LogLevel.INFO);
    expect(logs[2].level).toBe(LogLevel.WARN);
    expect(logs[3].level).toBe(LogLevel.ERROR);
  });

  test('应该根据日志级别过滤消息', () => {
    logger.setLevel(LogLevel.WARN);

    logger.debug('Debug message');
    logger.info('Info message');
    logger.warn('Warn message');
    logger.error('Error message');

    const logs = memoryAppender.getLogs();
    expect(logs).toHaveLength(2);
    expect(logs[0].level).toBe(LogLevel.WARN);
    expect(logs[1].level).toBe(LogLevel.ERROR);
  });
});
```

## ✅ 验收标准

### 功能验收
- [ ] Logger类创建成功，支持多种日志级别
- [ ] EventEmitter类创建成功，支持事件订阅和发布
- [ ] Validator类创建成功，支持多种验证规则
- [ ] DateHelper类创建成功，提供日期处理功能
- [ ] 所有工具类都有完善的错误处理

### 质量验收
- [ ] TypeScript编译无错误
- [ ] 单元测试覆盖率 ≥ 80%
- [ ] 代码符合项目编码规范
- [ ] 无ESLint警告或错误

### 性能验收
- [ ] 日志记录性能良好
- [ ] 事件发射器性能稳定
- [ ] 验证器执行效率高
- [ ] 内存使用合理

## 🔄 幂等性保证

### 执行前检查
```bash
#!/bin/bash
echo "🔍 检查任务1.6执行状态..."

# 检查依赖任务
if [ ! -f "src/infrastructure/crypto/AESCryptoService.ts" ]; then
  echo "❌ 依赖任务Task-1.5未完成，请先执行Task-1.5"
  exit 1
fi

# 检查是否已存在新实现
if [ -f "src/infrastructure/utils/Logger.ts" ]; then
  echo "⚠️  工具服务已存在，任务可能已完成"
  echo "请确认是否需要重新执行此任务"
  read -p "是否继续？(y/N): " confirm
  if [ "$confirm" != "y" ]; then
    exit 1
  fi
fi

echo "✅ 前置检查完成，可以执行任务1.6"
```

### 执行后验证
```bash
#!/bin/bash
echo "🔍 验证任务1.6执行结果..."

# 检查文件是否创建成功
files=(
  "src/infrastructure/utils/Logger.ts"
  "src/infrastructure/utils/EventEmitter.ts"
  "src/infrastructure/utils/Validator.ts"
  "src/infrastructure/utils/DateHelper.ts"
  "src/infrastructure/utils/index.ts"
  "src/infrastructure/utils/__tests__/Logger.test.ts"
)

all_created=true
for file in "${files[@]}"; do
  if [ -f "$file" ]; then
    echo "✅ $file 创建成功"
  else
    echo "❌ $file 创建失败"
    all_created=false
  fi
done

# TypeScript编译检查
if npm run type-check > /dev/null 2>&1; then
  echo "✅ TypeScript编译检查通过"
else
  echo "❌ TypeScript编译检查失败"
  all_created=false
fi

if [ "$all_created" = true ]; then
  echo "🎉 任务1.6执行成功！"
  exit 0
else
  echo "💥 任务1.6执行失败，请检查错误信息"
  exit 1
fi
```

## 📝 完成确认

任务完成后，请在检查清单中标记：
- [ ] 任务1.6已完成
- [ ] 所有工具服务创建成功
- [ ] 单元测试通过
- [ ] 准备执行任务1.7

## 🔗 相关链接

- [上一个任务：Task-1.5 创建加密服务](./task-1.5-crypto-service.md)
- [下一个任务：Task-1.7 创建基础设施层测试页面](./task-1.7-infrastructure-test-page.md)
- [重构检查清单](../00-refactoring-checklist.md)
```
```
```
```