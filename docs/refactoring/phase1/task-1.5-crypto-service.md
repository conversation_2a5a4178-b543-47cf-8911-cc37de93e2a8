# 任务1.5：创建加密服务

## 📋 任务概述

**任务ID**: Task-1.5  
**任务名称**: 创建统一加密服务  
**所属阶段**: 阶段1 - 基础设施层重构  
**预计时间**: 2小时  
**依赖任务**: Task-1.4 (创建网络客户端)  
**后续任务**: Task-1.6 (创建工具服务)  

## 🎯 任务目标

创建统一的加密服务，提供数据加密、解密、哈希和数字签名功能，支持多种加密算法。

## 📋 前置条件检查

### 依赖检查
- [ ] Task-1.4 已完成
- [ ] ICryptoService接口已定义
- [ ] Web Crypto API支持

### 文件检查
- [ ] `src/infrastructure/network/HttpClient.ts` 存在
- [ ] 浏览器环境支持Web Crypto API

### 状态检查
```bash
# 检查加密服务是否已存在
if [ -f "src/infrastructure/crypto/CryptoService.ts" ]; then
  echo "⚠️  加密服务已存在，请确认是否需要重新创建"
  exit 1
else
  echo "✅ 可以开始创建加密服务"
fi
```

## 🛠️ 实施步骤

### 步骤1：创建加密接口定义
**文件**: `src/infrastructure/interfaces/ICryptoService.ts`

```typescript
/**
 * 加密服务统一接口
 */
export interface ICryptoService {
  // 基础加密解密
  encrypt(data: string | ArrayBuffer, key?: string): Promise<EncryptResult>;
  decrypt(encryptedData: string | ArrayBuffer, key?: string): Promise<DecryptResult>;
  
  // 密钥管理
  generateKey(): Promise<string>;
  deriveKey(password: string, salt: string): Promise<string>;
  
  // 哈希函数
  hash(data: string | ArrayBuffer, algorithm?: HashAlgorithm): Promise<string>;
  
  // 流式加密
  createEncryptStream(key: string): TransformStream;
  createDecryptStream(key: string): TransformStream;
  
  // 数字签名
  sign(data: string | ArrayBuffer, privateKey: string): Promise<string>;
  verify(data: string | ArrayBuffer, signature: string, publicKey: string): Promise<boolean>;
}

/**
 * 加密结果
 */
export interface EncryptResult {
  encryptedData: string;
  iv: string;
  salt?: string;
  algorithm: string;
}

/**
 * 解密结果
 */
export interface DecryptResult {
  decryptedData: string | ArrayBuffer;
  algorithm: string;
}

/**
 * 哈希算法枚举
 */
export enum HashAlgorithm {
  SHA256 = 'SHA-256',
  SHA512 = 'SHA-512',
  SHA1 = 'SHA-1'
}

/**
 * 加密算法枚举
 */
export enum EncryptionAlgorithm {
  AES_GCM = 'AES-GCM',
  AES_CBC = 'AES-CBC',
  AES_CTR = 'AES-CTR'
}

/**
 * 密钥派生算法枚举
 */
export enum KeyDerivationAlgorithm {
  PBKDF2 = 'PBKDF2'
}
```

### 步骤2：创建AES加密服务实现
**文件**: `src/infrastructure/crypto/AESCryptoService.ts`

```typescript
import { 
  ICryptoService, 
  EncryptResult, 
  DecryptResult, 
  HashAlgorithm,
  EncryptionAlgorithm,
  KeyDerivationAlgorithm
} from '../interfaces/ICryptoService';

/**
 * 基于Web Crypto API的AES加密服务
 */
export class AESCryptoService implements ICryptoService {
  private defaultAlgorithm: EncryptionAlgorithm = EncryptionAlgorithm.AES_GCM;
  private defaultKeyLength = 256;
  private defaultIterations = 100000;

  /**
   * 加密数据
   */
  async encrypt(data: string | ArrayBuffer, key?: string): Promise<EncryptResult> {
    try {
      // 转换数据为ArrayBuffer
      const dataBuffer = this.toArrayBuffer(data);
      
      // 生成或使用提供的密钥
      const cryptoKey = key ? await this.importKey(key) : await this.generateCryptoKey();
      
      // 生成随机IV
      const iv = crypto.getRandomValues(new Uint8Array(12)); // GCM模式使用12字节IV
      
      // 加密数据
      const encryptedBuffer = await crypto.subtle.encrypt(
        {
          name: this.defaultAlgorithm,
          iv: iv
        },
        cryptoKey,
        dataBuffer
      );
      
      // 导出密钥（如果是新生成的）
      const exportedKey = key || await this.exportKey(cryptoKey);
      
      return {
        encryptedData: this.arrayBufferToBase64(encryptedBuffer),
        iv: this.arrayBufferToBase64(iv),
        algorithm: this.defaultAlgorithm
      };
    } catch (error) {
      throw new Error(`加密失败: ${error.message}`);
    }
  }

  /**
   * 解密数据
   */
  async decrypt(encryptedData: string | ArrayBuffer, key?: string): Promise<DecryptResult> {
    try {
      if (!key) {
        throw new Error('解密需要提供密钥');
      }

      // 解析加密数据
      let encryptedBuffer: ArrayBuffer;
      let iv: ArrayBuffer;
      
      if (typeof encryptedData === 'string') {
        // 假设格式为 "iv:encryptedData"
        const parts = encryptedData.split(':');
        if (parts.length !== 2) {
          throw new Error('加密数据格式错误');
        }
        iv = this.base64ToArrayBuffer(parts[0]);
        encryptedBuffer = this.base64ToArrayBuffer(parts[1]);
      } else {
        throw new Error('不支持的加密数据格式');
      }
      
      // 导入密钥
      const cryptoKey = await this.importKey(key);
      
      // 解密数据
      const decryptedBuffer = await crypto.subtle.decrypt(
        {
          name: this.defaultAlgorithm,
          iv: iv
        },
        cryptoKey,
        encryptedBuffer
      );
      
      return {
        decryptedData: decryptedBuffer,
        algorithm: this.defaultAlgorithm
      };
    } catch (error) {
      throw new Error(`解密失败: ${error.message}`);
    }
  }

  /**
   * 生成密钥
   */
  async generateKey(): Promise<string> {
    const cryptoKey = await this.generateCryptoKey();
    return await this.exportKey(cryptoKey);
  }

  /**
   * 派生密钥
   */
  async deriveKey(password: string, salt: string): Promise<string> {
    try {
      // 导入密码作为密钥材料
      const passwordKey = await crypto.subtle.importKey(
        'raw',
        new TextEncoder().encode(password),
        'PBKDF2',
        false,
        ['deriveBits', 'deriveKey']
      );
      
      // 派生密钥
      const derivedKey = await crypto.subtle.deriveKey(
        {
          name: KeyDerivationAlgorithm.PBKDF2,
          salt: new TextEncoder().encode(salt),
          iterations: this.defaultIterations,
          hash: HashAlgorithm.SHA256
        },
        passwordKey,
        {
          name: this.defaultAlgorithm,
          length: this.defaultKeyLength
        },
        true,
        ['encrypt', 'decrypt']
      );
      
      return await this.exportKey(derivedKey);
    } catch (error) {
      throw new Error(`密钥派生失败: ${error.message}`);
    }
  }

  /**
   * 计算哈希
   */
  async hash(data: string | ArrayBuffer, algorithm: HashAlgorithm = HashAlgorithm.SHA256): Promise<string> {
    try {
      const dataBuffer = this.toArrayBuffer(data);
      const hashBuffer = await crypto.subtle.digest(algorithm, dataBuffer);
      return this.arrayBufferToHex(hashBuffer);
    } catch (error) {
      throw new Error(`哈希计算失败: ${error.message}`);
    }
  }

  /**
   * 创建加密流
   */
  createEncryptStream(key: string): TransformStream {
    return new TransformStream({
      transform: async (chunk, controller) => {
        try {
          const result = await this.encrypt(chunk, key);
          controller.enqueue(result.encryptedData);
        } catch (error) {
          controller.error(error);
        }
      }
    });
  }

  /**
   * 创建解密流
   */
  createDecryptStream(key: string): TransformStream {
    return new TransformStream({
      transform: async (chunk, controller) => {
        try {
          const result = await this.decrypt(chunk, key);
          controller.enqueue(result.decryptedData);
        } catch (error) {
          controller.error(error);
        }
      }
    });
  }

  /**
   * 数字签名
   */
  async sign(data: string | ArrayBuffer, privateKey: string): Promise<string> {
    // 简化实现，实际项目中需要完整的RSA/ECDSA签名
    const dataBuffer = this.toArrayBuffer(data);
    const keyBuffer = this.toArrayBuffer(privateKey);
    const combined = new Uint8Array(dataBuffer.byteLength + keyBuffer.byteLength);
    combined.set(new Uint8Array(dataBuffer), 0);
    combined.set(new Uint8Array(keyBuffer), dataBuffer.byteLength);
    
    const signature = await this.hash(combined);
    return signature;
  }

  /**
   * 验证签名
   */
  async verify(data: string | ArrayBuffer, signature: string, publicKey: string): Promise<boolean> {
    // 简化实现，实际项目中需要完整的RSA/ECDSA验证
    const expectedSignature = await this.sign(data, publicKey);
    return expectedSignature === signature;
  }

  /**
   * 生成CryptoKey
   */
  private async generateCryptoKey(): Promise<CryptoKey> {
    return await crypto.subtle.generateKey(
      {
        name: this.defaultAlgorithm,
        length: this.defaultKeyLength
      },
      true,
      ['encrypt', 'decrypt']
    );
  }

  /**
   * 导出密钥
   */
  private async exportKey(cryptoKey: CryptoKey): Promise<string> {
    const exported = await crypto.subtle.exportKey('raw', cryptoKey);
    return this.arrayBufferToBase64(exported);
  }

  /**
   * 导入密钥
   */
  private async importKey(key: string): Promise<CryptoKey> {
    const keyBuffer = this.base64ToArrayBuffer(key);
    return await crypto.subtle.importKey(
      'raw',
      keyBuffer,
      {
        name: this.defaultAlgorithm,
        length: this.defaultKeyLength
      },
      false,
      ['encrypt', 'decrypt']
    );
  }

  /**
   * 转换为ArrayBuffer
   */
  private toArrayBuffer(data: string | ArrayBuffer): ArrayBuffer {
    if (typeof data === 'string') {
      return new TextEncoder().encode(data).buffer;
    }
    return data;
  }

  /**
   * ArrayBuffer转Base64
   */
  private arrayBufferToBase64(buffer: ArrayBuffer): string {
    const bytes = new Uint8Array(buffer);
    let binary = '';
    for (let i = 0; i < bytes.byteLength; i++) {
      binary += String.fromCharCode(bytes[i]);
    }
    return btoa(binary);
  }

  /**
   * Base64转ArrayBuffer
   */
  private base64ToArrayBuffer(base64: string): ArrayBuffer {
    const binary = atob(base64);
    const bytes = new Uint8Array(binary.length);
    for (let i = 0; i < binary.length; i++) {
      bytes[i] = binary.charCodeAt(i);
    }
    return bytes.buffer;
  }

  /**
   * ArrayBuffer转十六进制字符串
   */
  private arrayBufferToHex(buffer: ArrayBuffer): string {
    const bytes = new Uint8Array(buffer);
    return Array.from(bytes)
      .map(byte => byte.toString(16).padStart(2, '0'))
      .join('');
  }
}
```

### 步骤3：创建加密服务工厂
**文件**: `src/infrastructure/crypto/CryptoServiceFactory.ts`

```typescript
import { ICryptoService } from '../interfaces/ICryptoService';
import { AESCryptoService } from './AESCryptoService';

/**
 * 加密服务类型
 */
export enum CryptoServiceType {
  AES = 'aes',
  RSA = 'rsa'
}

/**
 * 加密服务工厂
 */
export class CryptoServiceFactory {
  private static instances: Map<CryptoServiceType, ICryptoService> = new Map();

  /**
   * 创建加密服务实例
   */
  static create(type: CryptoServiceType = CryptoServiceType.AES): ICryptoService {
    if (!this.instances.has(type)) {
      switch (type) {
        case CryptoServiceType.AES:
          this.instances.set(type, new AESCryptoService());
          break;
        case CryptoServiceType.RSA:
          // TODO: 实现RSA加密服务
          throw new Error('RSA加密服务尚未实现');
        default:
          throw new Error(`不支持的加密服务类型: ${type}`);
      }
    }

    return this.instances.get(type)!;
  }

  /**
   * 获取默认加密服务
   */
  static getDefault(): ICryptoService {
    return this.create(CryptoServiceType.AES);
  }

  /**
   * 清除所有实例
   */
  static clear(): void {
    this.instances.clear();
  }
}
```

### 步骤4：创建单元测试
**文件**: `src/infrastructure/crypto/__tests__/AESCryptoService.test.ts`

```typescript
import { AESCryptoService } from '../AESCryptoService';
import { HashAlgorithm } from '../interfaces/ICryptoService';

describe('AESCryptoService', () => {
  let cryptoService: AESCryptoService;

  beforeEach(() => {
    cryptoService = new AESCryptoService();
  });

  describe('密钥生成', () => {
    test('应该生成有效的密钥', async () => {
      const key = await cryptoService.generateKey();
      expect(typeof key).toBe('string');
      expect(key.length).toBeGreaterThan(0);
    });

    test('应该从密码派生密钥', async () => {
      const password = 'test-password';
      const salt = 'test-salt';
      const key = await cryptoService.deriveKey(password, salt);
      
      expect(typeof key).toBe('string');
      expect(key.length).toBeGreaterThan(0);
      
      // 相同的密码和盐应该生成相同的密钥
      const key2 = await cryptoService.deriveKey(password, salt);
      expect(key).toBe(key2);
    });
  });

  describe('加密解密', () => {
    test('应该成功加密和解密字符串', async () => {
      const originalData = 'Hello, World!';
      const key = await cryptoService.generateKey();
      
      const encryptResult = await cryptoService.encrypt(originalData, key);
      expect(encryptResult.encryptedData).toBeDefined();
      expect(encryptResult.iv).toBeDefined();
      
      const decryptResult = await cryptoService.decrypt(
        `${encryptResult.iv}:${encryptResult.encryptedData}`, 
        key
      );
      
      const decryptedText = new TextDecoder().decode(decryptResult.decryptedData as ArrayBuffer);
      expect(decryptedText).toBe(originalData);
    });

    test('应该成功加密和解密ArrayBuffer', async () => {
      const originalData = new TextEncoder().encode('Hello, World!');
      const key = await cryptoService.generateKey();
      
      const encryptResult = await cryptoService.encrypt(originalData.buffer, key);
      const decryptResult = await cryptoService.decrypt(
        `${encryptResult.iv}:${encryptResult.encryptedData}`, 
        key
      );
      
      const decryptedArray = new Uint8Array(decryptResult.decryptedData as ArrayBuffer);
      expect(decryptedArray).toEqual(originalData);
    });
  });

  describe('哈希计算', () => {
    test('应该计算SHA256哈希', async () => {
      const data = 'Hello, World!';
      const hash = await cryptoService.hash(data, HashAlgorithm.SHA256);
      
      expect(typeof hash).toBe('string');
      expect(hash.length).toBe(64); // SHA256产生64个十六进制字符
    });

    test('相同数据应该产生相同哈希', async () => {
      const data = 'test data';
      const hash1 = await cryptoService.hash(data);
      const hash2 = await cryptoService.hash(data);
      
      expect(hash1).toBe(hash2);
    });
  });

  describe('数字签名', () => {
    test('应该生成和验证签名', async () => {
      const data = 'test data';
      const privateKey = 'private-key';
      const publicKey = 'private-key'; // 简化实现中使用相同密钥
      
      const signature = await cryptoService.sign(data, privateKey);
      const isValid = await cryptoService.verify(data, signature, publicKey);
      
      expect(typeof signature).toBe('string');
      expect(isValid).toBe(true);
    });
  });
});
```

## ✅ 验收标准

### 功能验收
- [ ] AESCryptoService类创建成功
- [ ] 实现了ICryptoService接口的所有方法
- [ ] 支持AES-GCM加密算法
- [ ] 支持密钥生成和派生
- [ ] 支持多种哈希算法
- [ ] 错误处理机制完善

### 质量验收
- [ ] TypeScript编译无错误
- [ ] 单元测试覆盖率 ≥ 80%
- [ ] 代码符合项目编码规范
- [ ] 无ESLint警告或错误

### 安全验收
- [ ] 使用Web Crypto API标准实现
- [ ] 密钥生成使用安全随机数
- [ ] IV/盐值使用随机生成
- [ ] 敏感数据不在内存中长期保留

## 🔄 幂等性保证

### 执行前检查
```bash
#!/bin/bash
echo "🔍 检查任务1.5执行状态..."

# 检查依赖任务
if [ ! -f "src/infrastructure/network/HttpClient.ts" ]; then
  echo "❌ 依赖任务Task-1.4未完成，请先执行Task-1.4"
  exit 1
fi

# 检查是否已存在新实现
if [ -f "src/infrastructure/crypto/AESCryptoService.ts" ]; then
  echo "⚠️  AESCryptoService.ts已存在，任务可能已完成"
  echo "请确认是否需要重新执行此任务"
  read -p "是否继续？(y/N): " confirm
  if [ "$confirm" != "y" ]; then
    exit 1
  fi
fi

echo "✅ 前置检查完成，可以执行任务1.5"
```

### 执行后验证
```bash
#!/bin/bash
echo "🔍 验证任务1.5执行结果..."

# 检查文件是否创建成功
files=(
  "src/infrastructure/interfaces/ICryptoService.ts"
  "src/infrastructure/crypto/AESCryptoService.ts"
  "src/infrastructure/crypto/CryptoServiceFactory.ts"
  "src/infrastructure/crypto/__tests__/AESCryptoService.test.ts"
)

all_created=true
for file in "${files[@]}"; do
  if [ -f "$file" ]; then
    echo "✅ $file 创建成功"
  else
    echo "❌ $file 创建失败"
    all_created=false
  fi
done

# TypeScript编译检查
if npm run type-check > /dev/null 2>&1; then
  echo "✅ TypeScript编译检查通过"
else
  echo "❌ TypeScript编译检查失败"
  all_created=false
fi

if [ "$all_created" = true ]; then
  echo "🎉 任务1.5执行成功！"
  exit 0
else
  echo "💥 任务1.5执行失败，请检查错误信息"
  exit 1
fi
```

## 📝 完成确认

任务完成后，请在检查清单中标记：
- [ ] 任务1.5已完成
- [ ] AESCryptoService创建成功
- [ ] 单元测试通过
- [ ] 准备执行任务1.6

## 🔗 相关链接

- [上一个任务：Task-1.4 创建网络客户端](./task-1.4-network-client.md)
- [下一个任务：Task-1.6 创建工具服务](./task-1.6-utility-services.md)
- [重构检查清单](../00-refactoring-checklist.md)
