# 任务1.3：重构MinIO提供者

## 📋 任务概述

**任务ID**: Task-1.3  
**任务名称**: 重构MinIO提供者  
**所属阶段**: 阶段1 - 基础设施层重构  
**预计时间**: 3小时  
**依赖任务**: Task-1.2 (重构华为云OBS提供者)  
**后续任务**: Task-1.4 (创建网络客户端)  

## 🎯 任务目标

重构现有的MinIO提供者，使其实现新的IStorageProvider接口，解决现有的认证问题，并支持预签名URL生成。

## 📋 前置条件检查

### 依赖检查
- [ ] Task-1.2 已完成
- [ ] IStorageProvider接口已定义
- [ ] MinIO JavaScript SDK已安装 (`minio`)

### 文件检查
- [ ] `src/services/storage/MinioProvider.js` 存在
- [ ] `src/infrastructure/interfaces/IStorageProvider.ts` 存在

### 状态检查
```bash
# 检查当前MinIO提供者状态
if [ -f "src/infrastructure/providers/MinioProvider.ts" ]; then
  echo "⚠️  新的MinIO提供者已存在，请确认是否需要重新创建"
  exit 1
else
  echo "✅ 可以开始重构MinIO提供者"
fi
```

## 🛠️ 实施步骤

### 步骤1：创建新的MinIO提供者
**文件**: `src/infrastructure/providers/MinioProvider.ts`

```typescript
import * as Minio from 'minio';
import { BaseStorageProvider } from '../interfaces/IStorageProvider';
import { StorageType } from '../enums/StorageType';
import { IStorageConfig, ICloudStorageConfig } from '../types/StorageConfig';
import { 
  StorageResult, 
  StorageResultFactory,
  ObjectMetadata, 
  GetOptions, 
  PutOptions, 
  ListOptions, 
  SignedUrlOptions 
} from '../types/StorageResult';

/**
 * MinIO存储提供者
 * 实现IStorageProvider接口，提供MinIO对象存储服务
 */
export class MinioProvider extends BaseStorageProvider {
  private minioClient: Minio.Client | null = null;
  private bucketName: string = '';
  
  constructor() {
    super('MinIO对象存储', StorageType.MINIO);
  }
  
  /**
   * 初始化MinIO客户端
   */
  async initialize(config: IStorageConfig): Promise<void> {
    try {
      const minioConfig = config as ICloudStorageConfig;
      
      // 验证配置参数
      this.validateConfig(minioConfig);
      
      // 解析endpoint
      const endpointUrl = new URL(minioConfig.endpoint);
      
      // 创建MinIO客户端
      this.minioClient = new Minio.Client({
        endPoint: endpointUrl.hostname,
        port: minioConfig.port || (endpointUrl.protocol === 'https:' ? 443 : 80),
        useSSL: minioConfig.useSSL ?? (endpointUrl.protocol === 'https:'),
        accessKey: minioConfig.accessKey,
        secretKey: minioConfig.secretKey,
        region: minioConfig.region || 'us-east-1'
      });
      
      this.bucketName = minioConfig.bucketName;
      this.config = config;
      
      // 确保bucket存在
      await this.ensureBucketExists();
      
      this.initialized = true;
      console.log(`MinIO提供者初始化成功: ${this.bucketName}`);
    } catch (error) {
      this.initialized = false;
      throw new Error(`MinIO初始化失败: ${error.message}`);
    }
  }
  
  /**
   * 释放资源
   */
  async dispose(): Promise<void> {
    if (this.minioClient) {
      this.minioClient = null;
    }
    this.initialized = false;
    console.log('MinIO提供者已释放');
  }
  
  /**
   * 测试连接
   */
  async testConnection(): Promise<boolean> {
    if (!this.minioClient) {
      return false;
    }
    
    try {
      await this.minioClient.bucketExists(this.bucketName);
      return true;
    } catch (error) {
      console.error('MinIO连接测试失败:', error);
      return false;
    }
  }
  
  /**
   * 获取对象
   */
  async get(key: string, options?: GetOptions): Promise<StorageResult<any>> {
    if (!this.minioClient) {
      return StorageResultFactory.failure(new Error('MinIO客户端未初始化'));
    }
    
    try {
      const stream = await this.minioClient.getObject(this.bucketName, key);
      
      // 处理范围请求
      if (options?.range) {
        // MinIO SDK不直接支持范围请求，需要通过其他方式实现
        console.warn('MinIO范围请求功能待实现');
      }
      
      // 读取流数据
      const chunks: Buffer[] = [];
      
      return new Promise((resolve) => {
        stream.on('data', (chunk) => chunks.push(chunk));
        stream.on('end', () => {
          const buffer = Buffer.concat(chunks);
          
          let data: any = buffer;
          if (options?.saveByType === 'text') {
            data = buffer.toString('utf8');
          } else if (options?.saveByType === 'arraybuffer') {
            data = buffer.buffer.slice(buffer.byteOffset, buffer.byteOffset + buffer.byteLength);
          } else if (options?.saveByType === 'blob') {
            data = new Blob([buffer]);
          }
          
          resolve(StorageResultFactory.success(data));
        });
        stream.on('error', (error) => {
          resolve(StorageResultFactory.failure(error));
        });
      });
    } catch (error) {
      return StorageResultFactory.failure(error);
    }
  }
  
  /**
   * 上传对象
   */
  async put(key: string, data: any, options?: PutOptions): Promise<StorageResult<void>> {
    if (!this.minioClient) {
      return StorageResultFactory.failure(new Error('MinIO客户端未初始化'));
    }
    
    try {
      let buffer: Buffer;
      
      if (Buffer.isBuffer(data)) {
        buffer = data;
      } else if (data instanceof ArrayBuffer) {
        buffer = Buffer.from(data);
      } else if (typeof data === 'string') {
        buffer = Buffer.from(data, 'utf8');
      } else {
        buffer = Buffer.from(JSON.stringify(data), 'utf8');
      }
      
      const metadata: Record<string, string> = {};
      
      if (options?.contentType) {
        metadata['Content-Type'] = options.contentType;
      }
      
      if (options?.metadata) {
        Object.assign(metadata, options.metadata);
      }
      
      const result = await this.minioClient.putObject(
        this.bucketName, 
        key, 
        buffer, 
        buffer.length,
        metadata
      );
      
      return StorageResultFactory.success(undefined, { etag: result.etag });
    } catch (error) {
      return StorageResultFactory.failure(error);
    }
  }
  
  /**
   * 删除对象
   */
  async delete(key: string): Promise<StorageResult<void>> {
    if (!this.minioClient) {
      return StorageResultFactory.failure(new Error('MinIO客户端未初始化'));
    }
    
    try {
      await this.minioClient.removeObject(this.bucketName, key);
      return StorageResultFactory.success();
    } catch (error) {
      return StorageResultFactory.failure(error);
    }
  }
  
  /**
   * 列出对象
   */
  async list(prefix?: string, options?: ListOptions): Promise<StorageResult<string[]>> {
    if (!this.minioClient) {
      return StorageResultFactory.failure(new Error('MinIO客户端未初始化'));
    }
    
    try {
      const keys: string[] = [];
      const stream = this.minioClient.listObjects(this.bucketName, prefix, true);
      
      return new Promise((resolve) => {
        stream.on('data', (obj) => {
          if (obj.name) {
            keys.push(obj.name);
          }
        });
        stream.on('end', () => {
          resolve(StorageResultFactory.success(keys));
        });
        stream.on('error', (error) => {
          resolve(StorageResultFactory.failure(error));
        });
      });
    } catch (error) {
      return StorageResultFactory.failure(error);
    }
  }
  
  /**
   * 批量获取对象
   */
  async getBatch(keys: string[]): Promise<StorageResult<Record<string, any>>> {
    const results: Record<string, any> = {};
    const errors: string[] = [];

    for (const key of keys) {
      try {
        const result = await this.get(key);
        if (result.success) {
          results[key] = result.data;
        } else {
          errors.push(`${key}: ${result.error?.message}`);
        }
      } catch (error) {
        errors.push(`${key}: ${error.message}`);
      }
    }

    if (errors.length > 0) {
      return StorageResultFactory.failure(new Error(`批量获取部分失败: ${errors.join(', ')}`), { results });
    }

    return StorageResultFactory.success(results);
  }

  /**
   * 批量上传对象
   */
  async putBatch(items: Record<string, any>): Promise<StorageResult<void>> {
    const errors: string[] = [];

    for (const [key, data] of Object.entries(items)) {
      try {
        const result = await this.put(key, data);
        if (!result.success) {
          errors.push(`${key}: ${result.error?.message}`);
        }
      } catch (error) {
        errors.push(`${key}: ${error.message}`);
      }
    }

    if (errors.length > 0) {
      return StorageResultFactory.failure(new Error(`批量上传部分失败: ${errors.join(', ')}`));
    }

    return StorageResultFactory.success();
  }

  /**
   * 批量删除对象
   */
  async deleteBatch(keys: string[]): Promise<StorageResult<void>> {
    if (!this.minioClient) {
      return StorageResultFactory.failure(new Error('MinIO客户端未初始化'));
    }

    try {
      await this.minioClient.removeObjects(this.bucketName, keys);
      return StorageResultFactory.success();
    } catch (error) {
      return StorageResultFactory.failure(error);
    }
  }

  /**
   * 获取对象元数据
   */
  async getMetadata(key: string): Promise<StorageResult<ObjectMetadata>> {
    if (!this.minioClient) {
      return StorageResultFactory.failure(new Error('MinIO客户端未初始化'));
    }

    try {
      const stat = await this.minioClient.statObject(this.bucketName, key);

      const metadata: ObjectMetadata = {
        size: stat.size,
        lastModified: stat.lastModified,
        etag: stat.etag,
        contentType: stat.metaData['content-type'] || 'application/octet-stream',
        customMetadata: stat.metaData
      };

      return StorageResultFactory.success(metadata);
    } catch (error) {
      return StorageResultFactory.failure(error);
    }
  }

  /**
   * 获取对象流
   */
  async getStream(key: string): Promise<ReadableStream> {
    if (!this.minioClient) {
      throw new Error('MinIO客户端未初始化');
    }

    const stream = await this.minioClient.getObject(this.bucketName, key);
    
    return new ReadableStream({
      start(controller) {
        stream.on('data', (chunk) => {
          controller.enqueue(new Uint8Array(chunk));
        });
        stream.on('end', () => {
          controller.close();
        });
        stream.on('error', (error) => {
          controller.error(error);
        });
      }
    });
  }

  /**
   * 上传对象流
   */
  async putStream(key: string, stream: ReadableStream): Promise<StorageResult<void>> {
    try {
      const reader = stream.getReader();
      const chunks: Uint8Array[] = [];

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;
        chunks.push(value);
      }

      // 合并所有块
      const totalLength = chunks.reduce((sum, chunk) => sum + chunk.length, 0);
      const result = new Uint8Array(totalLength);
      let offset = 0;

      for (const chunk of chunks) {
        result.set(chunk, offset);
        offset += chunk.length;
      }

      return await this.put(key, result);
    } catch (error) {
      return StorageResultFactory.failure(error);
    }
  }

  /**
   * 生成签名URL
   */
  async getSignedUrl(key: string, options?: SignedUrlOptions): Promise<string> {
    if (!this.minioClient) {
      throw new Error('MinIO客户端未初始化');
    }

    const expires = options?.expires || 3600;
    const method = options?.method || 'GET';

    if (method === 'GET') {
      return await this.minioClient.presignedGetObject(this.bucketName, key, expires);
    } else if (method === 'PUT') {
      return await this.minioClient.presignedPutObject(this.bucketName, key, expires);
    } else {
      throw new Error(`不支持的HTTP方法: ${method}`);
    }
  }

  /**
   * 更新配置
   */
  async updateConfig(config: Partial<IStorageConfig>): Promise<void> {
    const newConfig = { ...this.config, ...config };
    await this.dispose();
    await this.initialize(newConfig);
  }

  /**
   * 确保bucket存在
   */
  private async ensureBucketExists(): Promise<void> {
    if (!this.minioClient) {
      throw new Error('MinIO客户端未初始化');
    }

    const exists = await this.minioClient.bucketExists(this.bucketName);
    if (!exists) {
      await this.minioClient.makeBucket(this.bucketName);
      console.log(`已创建bucket: ${this.bucketName}`);
    }
  }

  /**
   * 验证配置参数
   */
  private validateConfig(config: ICloudStorageConfig): void {
    if (!config.accessKey) {
      throw new Error('accessKey不能为空');
    }
    if (!config.secretKey) {
      throw new Error('secretKey不能为空');
    }
    if (!config.endpoint) {
      throw new Error('endpoint不能为空');
    }
    if (!config.bucketName) {
      throw new Error('bucketName不能为空');
    }
  }
}
```

### 步骤2：创建单元测试
**文件**: `src/infrastructure/providers/__tests__/MinioProvider.test.ts`

```typescript
import { MinioProvider } from '../MinioProvider';
import { StorageConfigFactory } from '../../types/StorageConfig';
import { StorageType } from '../../enums/StorageType';

// Mock MinIO SDK
jest.mock('minio');

describe('MinioProvider', () => {
  let provider: MinioProvider;
  let mockConfig: any;

  beforeEach(() => {
    provider = new MinioProvider();
    mockConfig = StorageConfigFactory.createMinioConfig({
      name: 'test-minio',
      endpoint: 'http://localhost:9000',
      accessKey: 'test-access-key',
      secretKey: 'test-secret-key',
      bucketName: 'test-bucket'
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('基本属性', () => {
    test('应该有正确的名称和类型', () => {
      expect(provider.name).toBe('MinIO对象存储');
      expect(provider.type).toBe(StorageType.MINIO);
      expect(provider.isInitialized).toBe(false);
    });
  });

  describe('初始化', () => {
    test('应该成功初始化', async () => {
      await provider.initialize(mockConfig);
      expect(provider.isInitialized).toBe(true);
    });

    test('配置参数缺失时应该抛出错误', async () => {
      const invalidConfig = { ...mockConfig, accessKey: '' };
      await expect(provider.initialize(invalidConfig)).rejects.toThrow('accessKey不能为空');
    });
  });

  describe('连接测试', () => {
    test('未初始化时应该返回false', async () => {
      const result = await provider.testConnection();
      expect(result).toBe(false);
    });
  });
});
```

## ✅ 验收标准

### 功能验收
- [ ] 新的MinioProvider类创建成功
- [ ] 实现了IStorageProvider接口的所有方法
- [ ] 解决了AWS4-HMAC-SHA256认证问题
- [ ] 支持预签名URL生成
- [ ] 错误处理机制完善

### 质量验收
- [ ] TypeScript编译无错误
- [ ] 单元测试覆盖率 ≥ 80%
- [ ] 代码符合项目编码规范
- [ ] 无ESLint警告或错误

### 性能验收
- [ ] 初始化时间 < 1秒
- [ ] 基础操作响应时间与原实现相当
- [ ] 内存使用无明显增加

## 🔄 幂等性保证

### 执行前检查
```bash
#!/bin/bash
echo "🔍 检查任务1.3执行状态..."

# 检查依赖任务
if [ ! -f "src/infrastructure/providers/HuaweiObsProvider.ts" ]; then
  echo "❌ 依赖任务Task-1.2未完成，请先执行Task-1.2"
  exit 1
fi

# 检查是否已存在新实现
if [ -f "src/infrastructure/providers/MinioProvider.ts" ]; then
  echo "⚠️  MinioProvider.ts已存在，任务可能已完成"
  echo "请确认是否需要重新执行此任务"
  read -p "是否继续？(y/N): " confirm
  if [ "$confirm" != "y" ]; then
    exit 1
  fi
fi

echo "✅ 前置检查完成，可以执行任务1.3"
```

### 执行后验证
```bash
#!/bin/bash
echo "🔍 验证任务1.3执行结果..."

# 检查文件是否创建成功
files=(
  "src/infrastructure/providers/MinioProvider.ts"
  "src/infrastructure/providers/__tests__/MinioProvider.test.ts"
)

all_created=true
for file in "${files[@]}"; do
  if [ -f "$file" ]; then
    echo "✅ $file 创建成功"
  else
    echo "❌ $file 创建失败"
    all_created=false
  fi
done

# TypeScript编译检查
if npm run type-check > /dev/null 2>&1; then
  echo "✅ TypeScript编译检查通过"
else
  echo "❌ TypeScript编译检查失败"
  all_created=false
fi

# 运行单元测试
if npm test -- MinioProvider.test.ts > /dev/null 2>&1; then
  echo "✅ 单元测试通过"
else
  echo "❌ 单元测试失败"
  all_created=false
fi

if [ "$all_created" = true ]; then
  echo "🎉 任务1.3执行成功！"
  exit 0
else
  echo "💥 任务1.3执行失败，请检查错误信息"
  exit 1
fi
```

## 📝 完成确认

任务完成后，请在检查清单中标记：
- [ ] 任务1.3已完成
- [ ] 新的MinioProvider创建成功
- [ ] 单元测试通过
- [ ] 准备执行任务1.4

## 🔗 相关链接

- [上一个任务：Task-1.2 重构华为云OBS提供者](./task-1.2-huawei-obs-provider.md)
- [下一个任务：Task-1.4 创建网络客户端](./task-1.4-network-client.md)
- [重构检查清单](../00-refactoring-checklist.md)
