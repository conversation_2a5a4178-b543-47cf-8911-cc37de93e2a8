# 任务1.1：创建存储提供者接口

## 📋 任务概述

**任务ID**: Task-1.1  
**任务名称**: 创建存储提供者接口  
**所属阶段**: 阶段1 - 基础设施层重构  
**预计时间**: 2小时  
**依赖任务**: Interface-01 (基础设施层接口设计)  
**后续任务**: Task-1.2 (重构华为云OBS提供者)  

## 🎯 任务目标

创建统一的存储提供者接口和相关类型定义，为所有存储提供者的实现提供标准契约。

## 📋 前置条件检查

### 环境检查
- [ ] Node.js 版本 >= 14.0.0
- [ ] TypeScript 已安装
- [ ] 项目依赖已安装 (`npm install`)
- [ ] 开发环境正常运行

### 文件检查
- [ ] `src` 目录存在
- [ ] `package.json` 文件存在
- [ ] TypeScript 配置文件存在

### 状态检查
```bash
# 检查是否已经存在接口文件
if [ -f "src/infrastructure/interfaces/IStorageProvider.ts" ]; then
  echo "⚠️  存储接口文件已存在，请确认是否需要重新创建"
  exit 1
else
  echo "✅ 可以开始创建存储接口文件"
fi
```

## 🛠️ 实施步骤

### 步骤1：创建目录结构
```bash
# 创建基础设施层目录
mkdir -p src/infrastructure/interfaces
mkdir -p src/infrastructure/types
mkdir -p src/infrastructure/enums
```

### 步骤2：创建存储类型枚举
**文件**: `src/infrastructure/enums/StorageType.ts`
```typescript
/**
 * 存储类型枚举
 * 定义系统支持的所有存储类型
 */
export enum StorageType {
  // 云存储提供者
  HUAWEI_OBS = 'huaweiObs',
  MINIO = 'minio',
  AMAZON_S3 = 'amazonS3',
  ALIYUN_OSS = 'aliyunOss',
  TENCENT_COS = 'tencentCos',
  
  // 本地存储
  LOCAL_STORAGE = 'localStorage',
  INDEXED_DB = 'indexedDB',
  
  // 内存存储（测试用）
  MEMORY_STORAGE = 'memoryStorage'
}

/**
 * 存储类型工具函数
 */
export class StorageTypeUtils {
  /**
   * 获取所有支持的存储类型
   */
  static getAllTypes(): StorageType[] {
    return Object.values(StorageType);
  }
  
  /**
   * 检查存储类型是否有效
   */
  static isValidType(type: string): type is StorageType {
    return Object.values(StorageType).includes(type as StorageType);
  }
  
  /**
   * 获取存储类型的显示名称
   */
  static getDisplayName(type: StorageType): string {
    const displayNames: Record<StorageType, string> = {
      [StorageType.HUAWEI_OBS]: '华为云对象存储',
      [StorageType.MINIO]: 'MinIO对象存储',
      [StorageType.AMAZON_S3]: '亚马逊S3存储',
      [StorageType.ALIYUN_OSS]: '阿里云对象存储',
      [StorageType.TENCENT_COS]: '腾讯云对象存储',
      [StorageType.LOCAL_STORAGE]: '本地存储',
      [StorageType.INDEXED_DB]: '浏览器数据库',
      [StorageType.MEMORY_STORAGE]: '内存存储'
    };
    return displayNames[type] || type;
  }
}
```

### 步骤3：创建存储配置类型
**文件**: `src/infrastructure/types/StorageConfig.ts`
```typescript
import { StorageType } from '../enums/StorageType';

/**
 * 存储配置基础接口
 */
export interface IStorageConfig {
  readonly type: StorageType;
  readonly name: string;
  readonly timeout?: number;
  readonly retryCount?: number;
  readonly retryDelay?: number;
}

/**
 * 云存储配置接口
 */
export interface ICloudStorageConfig extends IStorageConfig {
  readonly endpoint: string;
  readonly region?: string;
  readonly accessKey: string;
  readonly secretKey: string;
  readonly bucketName: string;
  readonly useSSL?: boolean;
  readonly port?: number;
}

/**
 * 本地存储配置接口
 */
export interface ILocalStorageConfig extends IStorageConfig {
  readonly storageKey: string;
  readonly maxSize?: number;
  readonly compression?: boolean;
}

/**
 * 内存存储配置接口
 */
export interface IMemoryStorageConfig extends IStorageConfig {
  readonly maxSize?: number;
  readonly ttl?: number; // 生存时间（毫秒）
}

/**
 * 存储配置工厂
 */
export class StorageConfigFactory {
  /**
   * 创建华为云OBS配置
   */
  static createHuaweiObsConfig(config: {
    name: string;
    endpoint: string;
    region: string;
    accessKey: string;
    secretKey: string;
    bucketName: string;
  }): ICloudStorageConfig {
    return {
      type: StorageType.HUAWEI_OBS,
      ...config,
      timeout: 30000,
      retryCount: 3,
      retryDelay: 1000
    };
  }
  
  /**
   * 创建MinIO配置
   */
  static createMinioConfig(config: {
    name: string;
    endpoint: string;
    accessKey: string;
    secretKey: string;
    bucketName: string;
    useSSL?: boolean;
    port?: number;
  }): ICloudStorageConfig {
    return {
      type: StorageType.MINIO,
      ...config,
      timeout: 30000,
      retryCount: 3,
      retryDelay: 1000
    };
  }
  
  /**
   * 创建本地存储配置
   */
  static createLocalStorageConfig(config: {
    name: string;
    storageKey: string;
    maxSize?: number;
  }): ILocalStorageConfig {
    return {
      type: StorageType.LOCAL_STORAGE,
      ...config,
      timeout: 5000,
      retryCount: 1
    };
  }
}
```

### 步骤4：创建存储结果类型
**文件**: `src/infrastructure/types/StorageResult.ts`
```typescript
/**
 * 存储操作结果
 */
export interface StorageResult<T> {
  success: boolean;
  data?: T;
  error?: Error;
  metadata?: Record<string, any>;
}

/**
 * 对象元数据
 */
export interface ObjectMetadata {
  size: number;
  lastModified: Date;
  etag: string;
  contentType: string;
  customMetadata?: Record<string, string>;
}

/**
 * 操作选项
 */
export interface GetOptions {
  range?: { start: number; end: number };
  saveByType?: 'text' | 'arraybuffer' | 'blob';
}

export interface PutOptions {
  contentType?: string;
  metadata?: Record<string, string>;
  overwrite?: boolean;
}

export interface ListOptions {
  maxKeys?: number;
  marker?: string;
  delimiter?: string;
}

export interface SignedUrlOptions {
  expires?: number; // 过期时间（秒）
  method?: 'GET' | 'PUT' | 'DELETE';
}

/**
 * 存储结果工厂
 */
export class StorageResultFactory {
  /**
   * 创建成功结果
   */
  static success<T>(data?: T, metadata?: Record<string, any>): StorageResult<T> {
    return {
      success: true,
      data,
      metadata
    };
  }
  
  /**
   * 创建失败结果
   */
  static failure<T>(error: Error, metadata?: Record<string, any>): StorageResult<T> {
    return {
      success: false,
      error,
      metadata
    };
  }
}
```

### 步骤5：创建存储提供者接口
**文件**: `src/infrastructure/interfaces/IStorageProvider.ts`
```typescript
import { StorageType } from '../enums/StorageType';
import { IStorageConfig } from '../types/StorageConfig';
import { 
  StorageResult, 
  ObjectMetadata, 
  GetOptions, 
  PutOptions, 
  ListOptions, 
  SignedUrlOptions 
} from '../types/StorageResult';

/**
 * 存储提供者统一接口
 * 所有存储提供者必须实现此接口
 */
export interface IStorageProvider {
  // 基本属性
  readonly name: string;
  readonly type: StorageType;
  readonly isInitialized: boolean;
  
  // 生命周期管理
  initialize(config: IStorageConfig): Promise<void>;
  dispose(): Promise<void>;
  
  // 连接测试
  testConnection(): Promise<boolean>;
  
  // 基础CRUD操作
  get(key: string, options?: GetOptions): Promise<StorageResult<any>>;
  put(key: string, data: any, options?: PutOptions): Promise<StorageResult<void>>;
  delete(key: string): Promise<StorageResult<void>>;
  list(prefix?: string, options?: ListOptions): Promise<StorageResult<string[]>>;
  
  // 批量操作
  getBatch(keys: string[]): Promise<StorageResult<Record<string, any>>>;
  putBatch(items: Record<string, any>): Promise<StorageResult<void>>;
  deleteBatch(keys: string[]): Promise<StorageResult<void>>;
  
  // 元数据操作
  getMetadata(key: string): Promise<StorageResult<ObjectMetadata>>;
  
  // 流式操作（大文件支持）
  getStream(key: string): Promise<ReadableStream>;
  putStream(key: string, stream: ReadableStream): Promise<StorageResult<void>>;
  
  // URL生成
  getSignedUrl(key: string, options?: SignedUrlOptions): Promise<string>;
  
  // 配置管理
  getConfig(): IStorageConfig;
  updateConfig(config: Partial<IStorageConfig>): Promise<void>;
}

/**
 * 存储提供者抽象基类
 * 提供通用功能的默认实现
 */
export abstract class BaseStorageProvider implements IStorageProvider {
  protected config: IStorageConfig;
  protected initialized: boolean = false;
  
  constructor(protected readonly providerName: string, protected readonly providerType: StorageType) {}
  
  get name(): string {
    return this.providerName;
  }
  
  get type(): StorageType {
    return this.providerType;
  }
  
  get isInitialized(): boolean {
    return this.initialized;
  }
  
  getConfig(): IStorageConfig {
    return { ...this.config };
  }
  
  // 抽象方法，子类必须实现
  abstract initialize(config: IStorageConfig): Promise<void>;
  abstract dispose(): Promise<void>;
  abstract testConnection(): Promise<boolean>;
  abstract get(key: string, options?: GetOptions): Promise<StorageResult<any>>;
  abstract put(key: string, data: any, options?: PutOptions): Promise<StorageResult<void>>;
  abstract delete(key: string): Promise<StorageResult<void>>;
  abstract list(prefix?: string, options?: ListOptions): Promise<StorageResult<string[]>>;
  abstract getBatch(keys: string[]): Promise<StorageResult<Record<string, any>>>;
  abstract putBatch(items: Record<string, any>): Promise<StorageResult<void>>;
  abstract deleteBatch(keys: string[]): Promise<StorageResult<void>>;
  abstract getMetadata(key: string): Promise<StorageResult<ObjectMetadata>>;
  abstract getStream(key: string): Promise<ReadableStream>;
  abstract putStream(key: string, stream: ReadableStream): Promise<StorageResult<void>>;
  abstract getSignedUrl(key: string, options?: SignedUrlOptions): Promise<string>;
  abstract updateConfig(config: Partial<IStorageConfig>): Promise<void>;
}
```

### 步骤6：创建索引文件
**文件**: `src/infrastructure/interfaces/index.ts`
```typescript
// 接口导出
export * from './IStorageProvider';

// 类型导出
export * from '../types/StorageConfig';
export * from '../types/StorageResult';

// 枚举导出
export * from '../enums/StorageType';
```

## ✅ 验收标准

### 功能验收
- [ ] 所有接口文件创建成功
- [ ] TypeScript 编译无错误
- [ ] 接口设计符合SOLID原则
- [ ] 类型定义完整且准确

### 质量验收
- [ ] 代码符合项目编码规范
- [ ] 接口文档注释完整
- [ ] 类型安全检查通过
- [ ] 无ESLint警告或错误

## 🔄 幂等性保证

### 执行前检查
```bash
#!/bin/bash
echo "🔍 检查任务1.1执行状态..."

# 检查目录是否存在
if [ -d "src/infrastructure" ]; then
  echo "✅ infrastructure目录已存在"
else
  echo "📁 需要创建infrastructure目录"
fi

# 检查接口文件是否存在
if [ -f "src/infrastructure/interfaces/IStorageProvider.ts" ]; then
  echo "⚠️  IStorageProvider.ts已存在，任务可能已完成"
  echo "请确认是否需要重新执行此任务"
  read -p "是否继续？(y/N): " confirm
  if [ "$confirm" != "y" ]; then
    exit 1
  fi
fi

echo "✅ 前置检查完成，可以执行任务1.1"
```

### 执行后验证
```bash
#!/bin/bash
echo "🔍 验证任务1.1执行结果..."

# 检查文件是否创建成功
files=(
  "src/infrastructure/enums/StorageType.ts"
  "src/infrastructure/types/StorageConfig.ts"
  "src/infrastructure/types/StorageResult.ts"
  "src/infrastructure/interfaces/IStorageProvider.ts"
  "src/infrastructure/interfaces/index.ts"
)

all_created=true
for file in "${files[@]}"; do
  if [ -f "$file" ]; then
    echo "✅ $file 创建成功"
  else
    echo "❌ $file 创建失败"
    all_created=false
  fi
done

# TypeScript编译检查
if npm run type-check > /dev/null 2>&1; then
  echo "✅ TypeScript编译检查通过"
else
  echo "❌ TypeScript编译检查失败"
  all_created=false
fi

if [ "$all_created" = true ]; then
  echo "🎉 任务1.1执行成功！"
  exit 0
else
  echo "💥 任务1.1执行失败，请检查错误信息"
  exit 1
fi
```

## 📝 完成确认

任务完成后，请在检查清单中标记：
- [ ] 任务1.1已完成
- [ ] 所有文件创建成功
- [ ] TypeScript编译通过
- [ ] 准备执行任务1.2

## 🔗 相关链接

- [下一个任务：Task-1.2 重构华为云OBS提供者](./task-1.2-huawei-obs-provider.md)
- [接口设计文档](../interfaces/01-infrastructure-interfaces.md)
- [重构检查清单](../00-refactoring-checklist.md)
